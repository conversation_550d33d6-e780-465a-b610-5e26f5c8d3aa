{"editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "cssVariables.lookupFiles": ["**/*.css", "**/*.scss", "**/*.sass", "**/*.less", "node_modules/@mantine/core/styles.css"], "nxConsole.generateAiAgentRules": true, "datadog.ust.service.setup.linkedNames": ["aussie-cms-latest-production", "gp-aussie-latest-production", "property-hub-api-latest-production"], "datadog.ust.environment.setup.focusedName": "production"}