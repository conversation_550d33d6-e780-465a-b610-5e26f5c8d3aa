#!/usr/bin/env bash

buildGeneric=0
while getopts ":b" opt; do
  case $opt in
    b)
      buildGeneric=1
      ;;
  esac
done

# This scripts starts up docker containers on developers local machine.

##########################################
# set tag for use in table names etc
##########################################
tag=$1
if [ -z "$1" ]
  then
    tag=$(git branch | sed -n -e 's/^\* \(.*\)/\1/p' | sed 's/feature\///g' | sed 's/release\///g' | sed 's/hotfix\///g')
fi

##########################################
# set IP for kafka address setup
##########################################
if [ -x "$(command -v ipconfig)" ]
  then
    export MY_IP=$(ipconfig getifaddr en0)
  else
    export MY_IP=$(hostname -I | awk '{print $1}')
fi

export LCD_TAG=$tag

if [ $buildGeneric -eq 1 ]
  then
    echo "Rebuilidng generic-image ..."
    docker-compose -f services/property-hub-kafka/docker-compose.yml build --no-cache generic-image
fi
docker-compose -f services/property-hub-kafka/docker-compose.yml up -d

export CONSUMER_NAME=PROPERTY
pnpm property-hub-kafka:build
export CONSUMER_NAME=AREA
pnpm property-hub-kafka:build

pnpm tsc --build -w --preserveWatchOutput services/property-hub-kafka
