steps:
  - label: ':buildkite: Platform Buildkite Plugin'
    agents:
      queue: k8s-setup
    key: buildkite-checks
    plugins:
      - ssh://*****************/lendi-dev/platform-buildkite-steps-plugin.git:
          team: growth_product
  - label: '🥑 Lendi'
    command: |
      echo "Checking for changes in Lendi app or libs..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(apps/lendi/|libs/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./apps/lendi/ci/pipeline.yml
      else
        echo "No changes detected, skipping Lendi pipeline"
      fi
  - label: '🐨 Aussie'
    command: |
      echo "Checking for changes in Aussie app or libs..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(apps/aussie/|libs/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./apps/aussie/ci/pipeline.yml
      else
        echo "No changes detected, skipping Aussie pipeline"
      fi
  - label: '🏠 Property Hub Database Schema Migration'
    command: |
      if [ "\$BUILDKITE_BRANCH" = "main" ]; then
        echo "Checking for changes in Property Hub database..."
        if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^libs/database/property-hub/'; then
          echo "Changes detected, uploading pipeline..."
          buildkite-agent pipeline upload ./libs/database/property-hub/ci/pipeline.yml
        else
          echo "No changes detected, skipping Property Hub database pipeline"
        fi
      else
        echo "Not on main branch, skipping Property Hub database pipeline"
      fi
  - label: '🏦 Stores and Brokers'
    command: |
      echo "Checking for changes in Stores and Brokers service..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(services/stores-and-brokers-api/|libs/api/(brokers|stores)/|libs/util/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./services/stores-and-brokers-api/ci/pipeline.yml
      else
        echo "No changes detected, skipping Stores and Brokers pipeline"
      fi
  - label: '🏦 Property Hub'
    commands:
      - buildkite-agent pipeline upload ./services/property-hub-api/ci/pipeline.yml
  - label: '🐨 Aussie Sitemap'
    command: |
      echo "Checking for changes in Aussie Sitemap..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(services/sitemap-generator/|libs/util/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./services/sitemap-generator/ci/pipeline.yml
      else
        echo "No changes detected, skipping Aussie Sitemap pipeline"
      fi
  - label: '🏦 Property Hub Kafka'
    command: |
      echo "Checking for changes in Property Hub Kafka service..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(services/property-hub-kafka/|libs/database/property-hub/|libs/data-access/property-hub/|libs/data-access/corelogic/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./services/property-hub-kafka/ci/pipeline.yml
      else
        echo "No changes detected, skipping Property Hub Kafka pipeline"
      fi
  - label: '💳 New Purchase Funnel'
    command: |
      echo "Checking for changes in New Purchase Funnel..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(apps/new-purchase-funnel/|libs/feature/new-purchase/|libs/feature/funnels/|libs/ui/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./apps/new-purchase-funnel/ci/pipeline.yml
      else
        echo "No changes detected, skipping New Purchase Funnel pipeline"
      fi
  # - label: '💰 Refinance Funnel'
  #   command: |
  #     echo "Checking for changes in Refinance Funnel..."
  #     if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(apps/refinance-funnel/|libs/feature/refinance/|libs/feature/funnels/|libs/ui/)'; then
  #       echo "Changes detected, uploading pipeline..."
  #       buildkite-agent pipeline upload ./apps/refinance-funnel/ci/pipeline.yml
  #     else
  #       echo "No changes detected, skipping Refinance Funnel pipeline"
  #     fi
  - label: '📈 Home Loan Health Check Funnel'
    command: |
      echo "Checking for changes in Home Loan Health Check Funnel..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(apps/home-loan-health-check/|libs/feature/home-loan-health-check/|libs/feature/funnels/|libs/ui/|libs/util/analytics/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./apps/home-loan-health-check/ci/pipeline.yml
      else
        echo "No changes detected, skipping Home Loan Health Check Funnel pipeline"
      fi
  - label: '™ Theme package'
    command: |
      echo "Checking for changes in Theme package..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^(packages/theme/|libs/theme/)'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./packages/theme/ci/pipeline.yml
      else
        echo "No changes detected, skipping Theme package pipeline"
      fi
  - label: '🚨 Monitoring'
    command: |
      echo "Checking for changes in Monitoring..."
      if git diff --name-only \${BUILDKITE_COMMIT}~1 \${BUILDKITE_COMMIT} | grep -qE '^monitoring/'; then
        echo "Changes detected, uploading pipeline..."
        buildkite-agent pipeline upload ./monitoring/pipeline.yml
      else
        echo "No changes detected, skipping Monitoring pipeline"
      fi
