# Database Libraries Guidelines

## Overview
Database libraries provide direct database access, schema definitions, migrations, and query utilities. They use Drizzle ORM for type-safe database operations and include comprehensive CRUD operations.

## Import Pattern
```typescript
import { DatabaseClient, QueryFunction } from '@gp/database/database-name';
```

## Available Database Libraries

### 1. Property Hub Database (`@gp/database/property-hub`)
Comprehensive property database with Drizzle ORM integration.

**Key Features:**
- PostgreSQL database with Drizzle ORM
- 20+ migrations for schema evolution
- Type-safe queries and operations
- Customer property relationships
- Property search and filtering
- CoreLogic integration
- AVM (Automated Valuation Model) caching

**Exports:**
- Database client and connection
- Schema definitions
- CRUD query functions
- Migration utilities

**Usage:**
```typescript
import { 
  propertyHubDb,
  getProperty,
  searchProperties,
  upsertCustomerProperty,
  getCustomerProperties 
} from '@gp/database/property-hub';

// Get a single property
const property = await getProperty('property-123');

// Search properties
const searchResults = await searchProperties({
  suburb: 'Sydney',
  minPrice: 500000,
  maxPrice: 1000000,
  bedrooms: 3
});

// Customer property operations
const customerProperty = await upsertCustomerProperty({
  customerId: 'customer-123',
  propertyId: 'property-456',
  relationship: 'owner'
});
```

## Database Schema Structure

### 1. Core Tables
```typescript
// Properties table
export const properties = pgTable('properties', {
  id: varchar('id', { length: 255 }).primaryKey(),
  address: varchar('address', { length: 500 }).notNull(),
  suburb: varchar('suburb', { length: 100 }).notNull(),
  state: varchar('state', { length: 10 }).notNull(),
  postcode: varchar('postcode', { length: 10 }).notNull(),
  bedrooms: integer('bedrooms'),
  bathrooms: integer('bathrooms'),
  carSpaces: integer('car_spaces'),
  landSize: integer('land_size'),
  price: bigint('price', { mode: 'number' }),
  propertyType: varchar('property_type', { length: 50 }),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// Customer properties relationship
export const customerProperties = pgTable('customer_properties', {
  id: varchar('id', { length: 255 }).primaryKey(),
  customerId: varchar('customer_id', { length: 255 }).notNull(),
  propertyId: varchar('property_id', { length: 255 }).notNull(),
  relationship: varchar('relationship', { length: 50 }).notNull(),
  createdAt: timestamp('created_at').defaultNow()
});
```

### 2. Geographic Tables
```typescript
// States table
export const states = pgTable('states', {
  id: varchar('id', { length: 10 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  abbreviation: varchar('abbreviation', { length: 10 }).notNull()
});

// Suburbs table
export const suburbs = pgTable('suburbs', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  postcode: varchar('postcode', { length: 10 }).notNull(),
  stateId: varchar('state_id', { length: 10 }).notNull(),
  slug: varchar('slug', { length: 200 }).unique()
});

// Areas/councils table
export const areas = pgTable('areas', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'council', 'region', etc.
  stateId: varchar('state_id', { length: 10 }).notNull()
});
```

## Query Functions

### 1. Property Queries
```typescript
// Get property by ID
export async function getProperty(id: string): Promise<Property | null> {
  const result = await propertyHubDb
    .select()
    .from(properties)
    .where(eq(properties.id, id))
    .limit(1);
    
  return result[0] || null;
}

// Search properties with filters
export async function searchProperties(params: PropertySearchParams): Promise<Property[]> {
  let query = propertyHubDb.select().from(properties);
  
  if (params.suburb) {
    query = query.where(eq(properties.suburb, params.suburb));
  }
  
  if (params.minPrice) {
    query = query.where(gte(properties.price, params.minPrice));
  }
  
  if (params.maxPrice) {
    query = query.where(lte(properties.price, params.maxPrice));
  }
  
  if (params.bedrooms) {
    query = query.where(eq(properties.bedrooms, params.bedrooms));
  }
  
  return query.limit(params.limit || 50);
}

// Get properties by suburb with pagination
export async function getPropertiesBySuburb(
  suburb: string,
  offset: number = 0,
  limit: number = 20
): Promise<Property[]> {
  return propertyHubDb
    .select()
    .from(properties)
    .where(eq(properties.suburb, suburb))
    .offset(offset)
    .limit(limit);
}
```

### 2. Customer Property Queries
```typescript
// Get customer properties with property details
export async function getCustomerProperties(customerId: string): Promise<CustomerProperty[]> {
  return propertyHubDb
    .select({
      id: customerProperties.id,
      customerId: customerProperties.customerId,
      propertyId: customerProperties.propertyId,
      relationship: customerProperties.relationship,
      createdAt: customerProperties.createdAt,
      property: properties
    })
    .from(customerProperties)
    .leftJoin(properties, eq(customerProperties.propertyId, properties.id))
    .where(eq(customerProperties.customerId, customerId));
}

// Upsert customer property relationship
export async function upsertCustomerProperty(data: UpsertCustomerPropertyData): Promise<CustomerProperty> {
  const existingRelation = await propertyHubDb
    .select()
    .from(customerProperties)
    .where(
      and(
        eq(customerProperties.customerId, data.customerId),
        eq(customerProperties.propertyId, data.propertyId)
      )
    )
    .limit(1);
    
  if (existingRelation.length > 0) {
    // Update existing
    const [updated] = await propertyHubDb
      .update(customerProperties)
      .set({ relationship: data.relationship })
      .where(eq(customerProperties.id, existingRelation[0].id))
      .returning();
    return updated;
  } else {
    // Insert new
    const [created] = await propertyHubDb
      .insert(customerProperties)
      .values({
        id: generateId(),
        customerId: data.customerId,
        propertyId: data.propertyId,
        relationship: data.relationship
      })
      .returning();
    return created;
  }
}
```

### 3. Geographic Queries
```typescript
// Get suburbs by state
export async function getSuburbsByState(stateId: string): Promise<Suburb[]> {
  return propertyHubDb
    .select()
    .from(suburbs)
    .where(eq(suburbs.stateId, stateId))
    .orderBy(suburbs.name);
}

// Search areas by name
export async function searchAreas(query: string, stateId?: string): Promise<Area[]> {
  let dbQuery = propertyHubDb
    .select()
    .from(areas)
    .where(ilike(areas.name, `%${query}%`));
    
  if (stateId) {
    dbQuery = dbQuery.where(eq(areas.stateId, stateId));
  }
  
  return dbQuery.limit(20);
}

// Get surrounding suburbs
export async function getSurroundingSuburbs(
  latitude: number,
  longitude: number,
  radiusKm: number = 5
): Promise<Suburb[]> {
  // Using PostGIS functions for geographic queries
  return propertyHubDb.execute(sql`
    SELECT s.*, 
           ST_Distance(
             ST_Point(${longitude}, ${latitude})::geography,
             ST_Point(p.longitude::float, p.latitude::float)::geography
           ) / 1000 as distance_km
    FROM suburbs s
    JOIN properties p ON p.suburb = s.name
    WHERE ST_DWithin(
      ST_Point(${longitude}, ${latitude})::geography,
      ST_Point(p.longitude::float, p.latitude::float)::geography,
      ${radiusKm * 1000}
    )
    GROUP BY s.id, s.name, s.postcode, s.state_id, s.slug
    ORDER BY distance_km
    LIMIT 20
  `);
}
```

### 4. Caching Queries
```typescript
// AVM (Automated Valuation Model) cache operations
export async function getAvmCache(propertyId: string): Promise<AvmCache | null> {
  const result = await propertyHubDb
    .select()
    .from(avmCache)
    .where(eq(avmCache.propertyId, propertyId))
    .limit(1);
    
  return result[0] || null;
}

export async function updateAvmCache(data: UpdateAvmCacheData): Promise<void> {
  await propertyHubDb
    .insert(avmCache)
    .values({
      propertyId: data.propertyId,
      valuation: data.valuation,
      confidence: data.confidence,
      lastUpdated: new Date(),
      source: data.source
    })
    .onConflictDoUpdate({
      target: avmCache.propertyId,
      set: {
        valuation: data.valuation,
        confidence: data.confidence,
        lastUpdated: new Date(),
        source: data.source
      }
    });
}
```

## Migration Management

### 1. Migration Structure
```typescript
// Example migration file: 0022_add_property_features.sql
CREATE TABLE IF NOT EXISTS "property_features" (
  "id" varchar(255) PRIMARY KEY NOT NULL,
  "property_id" varchar(255) NOT NULL,
  "feature_type" varchar(100) NOT NULL,
  "feature_value" text,
  "created_at" timestamp DEFAULT now(),
  FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS "idx_property_features_property_id" ON "property_features" ("property_id");
CREATE INDEX IF NOT EXISTS "idx_property_features_type" ON "property_features" ("feature_type");
```

### 2. Drizzle Commands
```bash
# Generate migration
pnpm nx run database/property-hub:drizzle-generate

# Run migrations
pnpm nx run database/property-hub:drizzle-migrate

# Drop database (development only)
pnpm nx run database/property-hub:drizzle-drop

# Open Drizzle Studio
pnpm nx run database/property-hub:drizzle-studio
```

## Database Client Configuration

### 1. Connection Setup
```typescript
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Connection configuration
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Create postgres client
const client = postgres(connectionString, {
  max: 10, // Maximum connections
  idle_timeout: 20,
  connect_timeout: 10
});

// Create Drizzle database instance
export const propertyHubDb = drizzle(client, { 
  schema,
  logger: process.env.NODE_ENV === 'development'
});

// Export types
export type PropertyHubDatabase = typeof propertyHubDb;
```

### 2. Transaction Support
```typescript
export async function createPropertyWithFeatures(
  propertyData: CreatePropertyData,
  features: CreateFeatureData[]
): Promise<Property> {
  return propertyHubDb.transaction(async (tx) => {
    // Insert property
    const [property] = await tx
      .insert(properties)
      .values(propertyData)
      .returning();
    
    // Insert features
    if (features.length > 0) {
      await tx
        .insert(propertyFeatures)
        .values(
          features.map(feature => ({
            ...feature,
            propertyId: property.id
          }))
        );
    }
    
    return property;
  });
}
```

## Best Practices

### 1. Type Safety
```typescript
// Use Drizzle's type inference
export type Property = typeof properties.$inferSelect;
export type NewProperty = typeof properties.$inferInsert;

// Create type-safe query builders
export function buildPropertyQuery() {
  return propertyHubDb.select().from(properties);
}
```

### 2. Query Optimization
```typescript
// Use indexes for common queries
export async function getPropertiesBySuburbOptimized(suburb: string): Promise<Property[]> {
  return propertyHubDb
    .select()
    .from(properties)
    .where(eq(properties.suburb, suburb))
    .orderBy(desc(properties.createdAt)) // Use indexed column for sorting
    .limit(50);
}

// Use prepared statements for repeated queries
const getPropertyById = propertyHubDb
  .select()
  .from(properties)
  .where(eq(properties.id, placeholder('id')))
  .prepare();

export async function getPropertyOptimized(id: string): Promise<Property | null> {
  const result = await getPropertyById.execute({ id });
  return result[0] || null;
}
```

### 3. Connection Management
```typescript
// Graceful shutdown
export async function closeDatabase(): Promise<void> {
  await client.end();
}

// Health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await propertyHubDb.execute(sql`SELECT 1`);
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}
```

### 4. Error Handling
```typescript
export class DatabaseError extends Error {
  constructor(
    message: string,
    public originalError?: Error,
    public query?: string
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export async function safeDatabaseOperation<T>(
  operation: () => Promise<T>,
  errorMessage: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    throw new DatabaseError(errorMessage, error as Error);
  }
}
```

## Testing Patterns

### 1. Test Database Setup
```typescript
// Test database configuration
export const testDb = drizzle(
  postgres(process.env.TEST_DATABASE_URL!),
  { schema }
);

// Test data cleanup
export async function cleanupTestData(): Promise<void> {
  await testDb.delete(customerProperties);
  await testDb.delete(properties);
  await testDb.delete(suburbs);
}

// Test data seeding
export async function seedTestData(): Promise<void> {
  await testDb.insert(states).values([
    { id: 'NSW', name: 'New South Wales', abbreviation: 'NSW' },
    { id: 'VIC', name: 'Victoria', abbreviation: 'VIC' }
  ]);
  
  await testDb.insert(suburbs).values([
    { 
      id: 'suburb-1', 
      name: 'Sydney', 
      postcode: '2000', 
      stateId: 'NSW',
      slug: 'sydney-nsw-2000'
    }
  ]);
}
```

### 2. Query Testing
```typescript
describe('Property Queries', () => {
  beforeEach(async () => {
    await cleanupTestData();
    await seedTestData();
  });
  
  it('should find properties by suburb', async () => {
    // Insert test property
    await testDb.insert(properties).values({
      id: 'prop-1',
      address: '123 Test Street',
      suburb: 'Sydney',
      state: 'NSW',
      postcode: '2000',
      bedrooms: 3,
      price: 800000
    });
    
    // Test query
    const results = await getPropertiesBySuburb('Sydney');
    expect(results).toHaveLength(1);
    expect(results[0].address).toBe('123 Test Street');
  });
});
```

## Performance Considerations

### 1. Indexing Strategy
```sql
-- Essential indexes for property queries
CREATE INDEX CONCURRENTLY idx_properties_suburb ON properties (suburb);
CREATE INDEX CONCURRENTLY idx_properties_price ON properties (price);
CREATE INDEX CONCURRENTLY idx_properties_bedrooms ON properties (bedrooms);
CREATE INDEX CONCURRENTLY idx_properties_location ON properties (latitude, longitude);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY idx_properties_suburb_price ON properties (suburb, price);
CREATE INDEX CONCURRENTLY idx_properties_search ON properties (suburb, bedrooms, price);
```

### 2. Query Optimization
```typescript
// Use EXPLAIN ANALYZE for query optimization
export async function analyzeQuery(query: string): Promise<any> {
  return propertyHubDb.execute(sql`EXPLAIN ANALYZE ${sql.raw(query)}`);
}

// Batch operations for better performance
export async function batchInsertProperties(properties: NewProperty[]): Promise<void> {
  const batchSize = 1000;
  
  for (let i = 0; i < properties.length; i += batchSize) {
    const batch = properties.slice(i, i + batchSize);
    await propertyHubDb.insert(properties).values(batch);
  }
}
```