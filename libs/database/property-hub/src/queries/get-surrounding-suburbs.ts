import { inArray } from 'drizzle-orm';

import { executeQuery, propertyHub, schema } from '../db';

const { suburbs } = schema;

export async function getSurroundingSuburbs(suburbSlugs: string[]): Promise<string[]> {
  const suburbsToSearch = new Set(suburbSlugs);

  // Fetch surrounding suburbs from database
  const surroundingSuburbs = await executeQuery(
    propertyHub
      .select({ surroundingSuburbs: suburbs.surroundingSuburbs })
      .from(suburbs)
      .where(inArray(suburbs.slug, suburbSlugs)),
    `Getting surroundingSuburbs for ${suburbSlugs.join(',')}`
  );

  surroundingSuburbs.forEach((row) => {
    row.surroundingSuburbs?.forEach((suburb) => suburbsToSearch.add(suburb));
  });
  return [...suburbsToSearch];
}
