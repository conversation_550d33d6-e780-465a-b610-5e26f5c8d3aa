import { inArray, or, sql } from 'drizzle-orm';
import { Logger } from 'pino';

import { executeQuery, propertyHub, schema } from '../db';

const { propertiesV2, properties: oldProperties } = schema;

// TODO: Need to confirm inser confilct scenario
export const upsertProperties = async (
  properties: (typeof propertiesV2.$inferInsert)[],
  logger?: Logger
) => {
  const oldPropertyIds = await propertyHub.query.properties.findMany({
    columns: {
      id: true,
      slug: true,
    },
    where: inArray(
      oldProperties.slug,
      properties.map((p) => p.slug)
    ),
  });
  const joinedNewProperties = properties.map((p) => ({
    ...p,
    oldId: oldPropertyIds.find((o) => o.slug === p.slug)?.id,
  }));
  const query = propertyHub
    .insert(propertiesV2)
    .values(joinedNewProperties)
    .onConflictDoUpdate({
      target: [propertiesV2.id],
      set: {
        slug: sql`excluded.slug`,
        data: sql`excluded.data`,
        address: sql`excluded.address`,
        corelogicId: sql`excluded.corelogic_id`,
        oldId: sql`excluded.old_id`,
        gnafId: sql`excluded.gnaf_id`,
        listingLoopId: sql`excluded.listing_loop_id`,
        reipId: sql`excluded.reip_id`,
        suburbSlug: sql`excluded.suburb_slug`,
        listed: sql`excluded.listed`,
        listedDate: sql`excluded.listed_date`,
        location: sql`excluded.location`,
        updatedAt: sql`now()`,
      },
    })
    .returning({ id: propertiesV2.id });
  const ids = executeQuery(query, `Upserting ${properties.length} properties`, true, logger);
  return ids;
};

// TODO: Need to confirm what fields can be updated
export const deleteProperties = async (slugs: string[], ids: string[]) => {
  const query = propertyHub
    .delete(propertiesV2)
    .where(or(inArray(propertiesV2.slug, slugs), inArray(propertiesV2.id, ids)));

  await executeQuery(
    query,
    `Deleating properties with ID: ${ids.toString()}, slug: ${slugs.toString()}`
  );
};
