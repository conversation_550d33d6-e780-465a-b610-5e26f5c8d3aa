import { eq, sql } from 'drizzle-orm';

import { executeQuery, propertyHub, schema } from '../db';

const { states } = schema;

// TODO: Need to confirm what fields can be updated
export const updateState = async (state: typeof states.$inferInsert) => {
  const query = propertyHub.update(states).set(state).where(eq(states.slug, state.slug));

  await executeQuery(query, `Updating state with ID: ${state.id}, slug: ${state.slug}`);
};

// TODO: Need to confirm inser confilct scenario
export const upsertState = async (state: typeof states.$inferInsert) => {
  const query = propertyHub
    .insert(states)
    .values(state)
    .onConflictDoUpdate({
      target: [states.slug],
      set: {
        data: sql`excluded.data`,
        name: sql`excluded.name`,
        updatedAt: sql`now()`,
      },
    });

  await executeQuery(query, `Upserting state with ID: ${state.id}, slug: ${state.slug}`);
};

// TODO: Need to confirm what fields can be updated
// TODO: Need to manage relationship / transaction?
export const deleteState = async (slug: string, id: string) => {
  const query = propertyHub.delete(states).where(eq(states.slug, slug));

  await executeQuery(query, `Updating state with ID: ${id}, slug: ${slug}`);
};
