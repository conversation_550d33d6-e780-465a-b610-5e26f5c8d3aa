import { eq, sql } from 'drizzle-orm';

import { executeQuery, propertyHub, schema } from '../db';

const { states, suburbs } = schema;

export async function getSuburbByState(stateSlug: string) {
  const query = propertyHub
    .select()
    .from(
      propertyHub
        .select({
          slug: states.slug,
          updatedAt: states.updatedAt,
          type: sql`'state'`.as('type'),
        })
        .from(states)
        .where(eq(states.slug, stateSlug))
        .union(
          propertyHub
            .select({
              slug: suburbs.slug,
              updatedAt: suburbs.updatedAt,
              type: sql`'suburb'`.as('type'),
            })
            .from(suburbs)
            .where(eq(suburbs.stateSlug, stateSlug))
        )
        .as('combined_results')
    )
    .orderBy(
      sql`CASE 
        WHEN type = 'state' THEN 1
        WHEN type = 'suburb' THEN 2
      END`
    );

  return await executeQuery(
    query,
    `Getting states, councils or suburbs with stateSlug - ${stateSlug}`
  );
}
