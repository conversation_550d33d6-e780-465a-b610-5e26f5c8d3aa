import { eq, sql } from 'drizzle-orm';

import { executeQuery, propertyHub, schema } from '../db';

const { suburbs } = schema;

// TODO: Need to confirm what fields can be updated
export const updateSuburb = async (suburb: typeof suburbs.$inferInsert) => {
  const query = propertyHub.update(suburbs).set(suburb).where(eq(suburbs.slug, suburb.slug));

  await executeQuery(query, `Updating suburb with ID: ${suburb.id}, slug: ${suburb.slug}`);
};

// TODO: Need to confirm inser confilct scenario
export const upsertSuburb = async (suburb: typeof suburbs.$inferInsert) => {
  const query = propertyHub
    .insert(suburbs)
    .values(suburb)
    .onConflictDoUpdate({
      target: [suburbs.slug],
      set: {
        data: sql`excluded.data`,
        name: sql`excluded.name`,
        postcode: sql`excluded.postcode`,
        otherPostcode: sql`excluded.otherPostcode`,
        councilSlug: sql`excluded.councilSlug`,
        stateSlug: sql`excluded.stateSlug`,
        surroundingSuburbs: sql`excluded.surroundingSuburbs`,
        location: sql`excluded.location`,
        updatedAt: sql`now()`,
      },
    });

  await executeQuery(query, `Upserting suburb with ID: ${suburb.id}, slug: ${suburb.slug}`);
};

// TODO: Need to confirm what fields can be updated
// TODO: Need to manage relationship / transaction?
export const deleteSuburb = async (slug: string, id: string) => {
  const query = propertyHub.delete(suburbs).where(eq(suburbs.slug, slug));

  await executeQuery(query, `Updating suburb with ID: ${id}, slug: ${slug}`);
};
