import { eq, sql } from 'drizzle-orm';

import { executeQuery, propertyHub, schema } from '../db';

const { councils } = schema;

// TODO: Need to confirm what fields can be updated
export const updateCouncil = async (council: typeof councils.$inferInsert) => {
  const query = propertyHub.update(councils).set(council).where(eq(councils.slug, council.slug));

  await executeQuery(query, `Updating council with ID: ${council.id}, slug: ${council.slug}`);
};

// TODO: Need to confirm inser confilct scenario
export const upsertCouncil = async (council: typeof councils.$inferInsert) => {
  const query = propertyHub
    .insert(councils)
    .values(council)
    .onConflictDoUpdate({
      target: [councils.slug],
      set: {
        data: sql`excluded.data`,
        name: sql`excluded.name`,
        stateSlug: sql`excluded.stateSlug`,
        updatedAt: sql`now()`,
      },
    });

  await executeQuery(query, `Upserting council with ID: ${council.id}, slug: ${council.slug}`);
};

// TODO: Need to confirm what fields can be updated
// TODO: Need to manage relationship / transaction?
export const deleteCouncil = async (slug: string, id: string) => {
  const query = propertyHub.delete(councils).where(eq(councils.slug, slug));

  await executeQuery(query, `Updating council with ID: ${id}, slug: ${slug}`);
};
