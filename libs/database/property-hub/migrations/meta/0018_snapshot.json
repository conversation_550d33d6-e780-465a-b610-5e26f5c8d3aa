{"id": "5aa8a8a7-9e12-4538-8e74-551749607fd7", "prevId": "78f22ca3-b410-4b15-93af-6a2cf1c48836", "version": "7", "dialect": "postgresql", "tables": {"public.corelogic_avm_cache": {"name": "corelogic_avm_cache", "schema": "", "columns": {"corelogic_id": {"name": "corelogic_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "estimated_rent": {"name": "estimated_rent", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{\"estimate\":0}'::jsonb"}, "estimated_value": {"name": "estimated_value", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{\"estimate\":0}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"corelogic_avm_cache_corelogic_id_pk": {"name": "corelogic_avm_cache_corelogic_id_pk", "columns": ["corelogic_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.councils": {"name": "councils", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "state_slug": {"name": "state_slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"council_slug_idx": {"name": "council_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "council_name_idx": {"name": "council_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "council_state_slug_idx": {"name": "council_state_slug_idx", "columns": [{"expression": "state_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"councils_state_slug_states_slug_fk": {"name": "councils_state_slug_states_slug_fk", "tableFrom": "councils", "tableTo": "states", "columnsFrom": ["state_slug"], "columnsTo": ["slug"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"councils_slug_unique": {"name": "councils_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_property_relations": {"name": "customer_property_relations", "schema": "", "columns": {"customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "relationship": {"name": "relationship", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"customer_property_relation_relationship_idx": {"name": "customer_property_relation_relationship_idx", "columns": [{"expression": "relationship", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_property_relations_property_id_properties_id_fk": {"name": "customer_property_relations_property_id_properties_id_fk", "tableFrom": "customer_property_relations", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"customer_property_relations_customer_id_property_id_pk": {"name": "customer_property_relations_customer_id_property_id_pk", "columns": ["customer_id", "property_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_property_searches": {"name": "customer_property_searches", "schema": "", "columns": {"customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "query_string": {"name": "query_string", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "search_type": {"name": "search_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"customer_property_searches_customer_id_query_string_pk": {"name": "customer_property_searches_customer_id_query_string_pk", "columns": ["customer_id", "query_string"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "corelogic_id": {"name": "corelogic_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "gnaf_id": {"name": "gnaf_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "suburb_slug": {"name": "suburb_slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "listed": {"name": "listed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "listed_date": {"name": "listed_date", "type": "date", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": false}}, "indexes": {"property_slug_idx": {"name": "property_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "property_address_idx": {"name": "property_address_idx", "columns": [{"expression": "address", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_location_idx": {"name": "property_location_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}, "property_corelogic_id_idx": {"name": "property_corelogic_id_idx", "columns": [{"expression": "corelogic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_gnaf_id_idx": {"name": "property_gnaf_id_idx", "columns": [{"expression": "gnaf_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_suburb_slug_idx": {"name": "property_suburb_slug_idx", "columns": [{"expression": "suburb_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_listed_date_idx": {"name": "property_listed_date_idx", "columns": [{"expression": "listed_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_primary_address_idx": {"name": "property_data_primary_address_idx", "columns": [{"expression": "((data->>'primaryAddress')::varchar)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_type_idx": {"name": "property_data_type_idx", "columns": [{"expression": "((data->>'type')::varchar)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_bathrooms_idx": {"name": "property_data_bathrooms_idx", "columns": [{"expression": "((data->>'bathrooms')::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_bedrooms_idx": {"name": "property_data_bedrooms_idx", "columns": [{"expression": "((data->>'bedrooms')::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_carspaces_idx": {"name": "property_data_carspaces_idx", "columns": [{"expression": "((data->>'carspaces')::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_landarea_idx": {"name": "property_data_landarea_idx", "columns": [{"expression": "((data->>'landarea')::float)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_listing_type_idx": {"name": "property_data_listing_type_idx", "columns": [{"expression": "((data->>'listingType')::varchar)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_suburb_slug_partial_listed_idx": {"name": "property_suburb_slug_partial_listed_idx", "columns": [{"expression": "suburb_slug", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "where": "listed", "concurrently": false, "method": "btree", "with": {}}, "property_suburb_slug_partial_for_sale_idx": {"name": "property_suburb_slug_partial_for_sale_idx", "columns": [{"expression": "suburb_slug", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "where": "data->>'forSale' = 'true'", "concurrently": false, "method": "btree", "with": {}}, "property_data_estimated_value_idx": {"name": "property_data_estimated_value_idx", "columns": [{"expression": "((data->'estimatedValue'->>'estimate')::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_price_guide_low_idx": {"name": "property_data_price_guide_low_idx", "columns": [{"expression": "((data->'priceGuide'->>0)::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_data_price_guide_high_idx": {"name": "property_data_price_guide_high_idx", "columns": [{"expression": "((data->'priceGuide'->>1)::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_suburb_slug_suburbs_slug_fk": {"name": "properties_suburb_slug_suburbs_slug_fk", "tableFrom": "properties", "tableTo": "suburbs", "columnsFrom": ["suburb_slug"], "columnsTo": ["slug"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"properties_slug_unique": {"name": "properties_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties_v2": {"name": "properties_v2", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "corelogic_id": {"name": "corelogic_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "listing_loop_id": {"name": "listing_loop_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reip_id": {"name": "reip_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "gnaf_id": {"name": "gnaf_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "suburb_slug": {"name": "suburb_slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "listed": {"name": "listed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "listed_date": {"name": "listed_date", "type": "date", "primaryKey": false, "notNull": false}, "old_id": {"name": "old_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": false}}, "indexes": {"property_v2_slug_idx": {"name": "property_v2_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "property_v2_corelogic_id_idx": {"name": "property_v2_corelogic_id_idx", "columns": [{"expression": "corelogic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_v2_suburb_slug_idx": {"name": "property_v2_suburb_slug_idx", "columns": [{"expression": "suburb_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_v2_suburb_slug_partial_for_sale_idx": {"name": "property_v2_suburb_slug_partial_for_sale_idx", "columns": [{"expression": "suburb_slug", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "where": "data->>'forSale' = 'true'", "concurrently": false, "method": "btree", "with": {}}, "property_v2_data_estimated_value_idx": {"name": "property_v2_data_estimated_value_idx", "columns": [{"expression": "((data->'estimatedValue'->>'estimate')::integer)", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_v2_suburb_slug_suburbs_slug_fk": {"name": "properties_v2_suburb_slug_suburbs_slug_fk", "tableFrom": "properties_v2", "tableTo": "suburbs", "columnsFrom": ["suburb_slug"], "columnsTo": ["slug"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"properties_v2_slug_unique": {"name": "properties_v2_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.server_action": {"name": "server_action", "schema": "", "columns": {"flag": {"name": "flag", "type": "text", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "doer": {"name": "doer", "type": "uuid", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "boolean", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.states": {"name": "states", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"state_slug_idx": {"name": "state_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "state_name_idx": {"name": "state_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"states_slug_unique": {"name": "states_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "states_name_unique": {"name": "states_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suburbs": {"name": "suburbs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "postcode": {"name": "postcode", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "other_postcode": {"name": "other_postcode", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "council_slug": {"name": "council_slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state_slug": {"name": "state_slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "surrounding_suburbs": {"name": "surrounding_suburbs", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": false}}, "indexes": {"suburb_slug_idx": {"name": "suburb_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "suburb_name_idx": {"name": "suburb_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_postcode_idx": {"name": "suburb_postcode_idx", "columns": [{"expression": "postcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_other_postcode_idx": {"name": "suburb_other_postcode_idx", "columns": [{"expression": "other_postcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_council_slug_idx": {"name": "suburb_council_slug_idx", "columns": [{"expression": "council_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_state_slug_idx": {"name": "suburb_state_slug_idx", "columns": [{"expression": "state_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_surrounding_suburbs_idx": {"name": "suburb_surrounding_suburbs_idx", "columns": [{"expression": "surrounding_suburbs", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "suburb_location_idx": {"name": "suburb_location_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"suburbs_council_slug_councils_slug_fk": {"name": "suburbs_council_slug_councils_slug_fk", "tableFrom": "suburbs", "tableTo": "councils", "columnsFrom": ["council_slug"], "columnsTo": ["slug"], "onDelete": "no action", "onUpdate": "no action"}, "suburbs_state_slug_states_slug_fk": {"name": "suburbs_state_slug_states_slug_fk", "tableFrom": "suburbs", "tableTo": "states", "columnsFrom": ["state_slug"], "columnsTo": ["slug"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"suburbs_slug_unique": {"name": "suburbs_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}