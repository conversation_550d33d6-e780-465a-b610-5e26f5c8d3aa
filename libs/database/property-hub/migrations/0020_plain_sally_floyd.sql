ALTER TABLE "customer_property_searches" ADD COLUMN "search_category" varchar(255);
UPDATE "customer_property_searches" SET "search_category" = 'buy';
ALTER TABLE "customer_property_searches" DROP CONSTRAINT "customer_property_searches_customer_id_query_string_pk";--> statement-breakpoint
ALTER TABLE "customer_property_searches" ADD CONSTRAINT "customer_property_searches_customer_id_query_string_search_category_pk" PRIMARY KEY("customer_id","query_string","search_category");--> statement-breakpoint
