CREATE TABLE IF NOT EXISTS "properties_v2" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"slug" varchar(255) NOT NULL,
	"address" varchar(255) NOT NULL,
	"corelogic_id" varchar(255),
	"listing_loop_id" varchar(255),
	"reip_id" varchar(255),
	"gnaf_id" varchar(255),
	"suburb_slug" varchar(255) NOT NULL,
	"listed" boolean DEFAULT false NOT NULL,
	"listed_date" date,
	"old_id" varchar(255),
	"data" jsonb NOT NULL,
	"location" geometry(point),
	CONSTRAINT "properties_v2_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "properties_v2" ADD CONSTRAINT "properties_v2_suburb_slug_suburbs_slug_fk" FOREIGN KEY ("suburb_slug") REFERENCES "public"."suburbs"("slug") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS "property_v2_slug_idx" ON "properties_v2" USING btree ("slug");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "property_v2_corelogic_id_idx" ON "properties_v2" USING btree ("corelogic_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "property_v2_suburb_slug_idx" ON "properties_v2" USING btree ("suburb_slug");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "property_v2_suburb_slug_partial_for_sale_idx" ON "properties_v2" USING btree (suburb_slug) WHERE data->>'forSale' = 'true';--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "property_v2_data_estimated_value_idx" ON "properties_v2" USING btree (((data->'estimatedValue'->>'estimate')::integer));--> statement-breakpoint
GRANT SELECT, INSERT, UPDATE, DELETE ON properties_v2 TO property_hub_app;