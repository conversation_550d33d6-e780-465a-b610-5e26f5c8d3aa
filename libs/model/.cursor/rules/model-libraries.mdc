# Model Libraries Guidelines

## Overview
Model libraries contain TypeScript interfaces, types, schemas, and data models that define the structure of business entities across the application. They provide shared type definitions and validation schemas for consistent data handling.

## Import Pattern
```typescript
import { ModelType, ModelSchema } from '@gp/model/domain-name';
```

## Available Model Libraries

### 1. Broker Models (`@gp/model/broker`)
Type definitions and schemas for broker-related entities.

**Types & Interfaces:**
```typescript
// Broker entity
export interface Broker {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  specialties: BrokerSpecialty[];
  rating: number;
  reviewCount: number;
  location: BrokerLocation;
  availability: BrokerAvailability;
  certifications: BrokerCertification[];
}

// Broker specialties
export enum BrokerSpecialty {
  FIRST_HOME_BUYER = 'first-home-buyer',
  INVESTMENT = 'investment',
  REFINANCE = 'refinance',
  COMMERCIAL = 'commercial',
  CONSTRUCTION = 'construction'
}

// Broker location
export interface BrokerLocation {
  state: string;
  suburb: string;
  postcode: string;
  serviceAreas: string[];
}

// Broker availability
export interface BrokerAvailability {
  timezone: string;
  workingHours: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
  workingDays: WeekDay[];
  appointmentSlots: AppointmentSlot[];
}
```

**Usage:**
```typescript
import { 
  Broker, 
  BrokerSpecialty, 
  BrokerLocation,
  BrokerSchema 
} from '@gp/model/broker';

// Type-safe broker data
const broker: Broker = {
  id: 'broker-123',
  firstName: 'John',
  lastName: 'Smith',
  email: '<EMAIL>',
  specialties: [BrokerSpecialty.FIRST_HOME_BUYER, BrokerSpecialty.INVESTMENT],
  rating: 4.8,
  reviewCount: 156,
  location: {
    state: 'NSW',
    suburb: 'Sydney',
    postcode: '2000',
    serviceAreas: ['Sydney', 'Parramatta', 'Chatswood']
  },
  availability: {
    timezone: 'Australia/Sydney',
    workingHours: { start: '09:00', end: '17:00' },
    workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    appointmentSlots: []
  },
  certifications: []
};

// Validate broker data
const validatedBroker = BrokerSchema.parse(brokerData);
```

### 2. CMS Models (`@gp/model/cms`)
Content Management System models for Contentful integration.

**Components Models:**
```typescript
// Hero component model
export interface HeroComponent {
  sys: ContentfulSys;
  contentType: 'hero';
  fields: {
    heading: string;
    subtitle?: string;
    backgroundImage: ContentfulAsset;
    ctaButtons: CtaButton[];
    trustBar?: boolean;
  };
}

// Feature block component model
export interface FeatureBlockComponent {
  sys: ContentfulSys;
  contentType: 'featureBlock';
  fields: {
    title: string;
    description: string;
    image: ContentfulAsset;
    imagePlacement: 'left' | 'right';
    cta?: CtaButton;
  };
}

// CTA button model
export interface CtaButton {
  label: string;
  href: string;
  variant: 'primary' | 'secondary' | 'outline';
  tracking?: {
    position: string;
    purpose: string;
  };
}
```

**Page Models:**
```typescript
// Landing page model
export interface LandingPage {
  sys: ContentfulSys;
  contentType: 'landingPage';
  fields: {
    title: string;
    slug: string;
    metaDescription: string;
    components: (HeroComponent | FeatureBlockComponent | AccordionComponent)[];
    seoSettings: SeoSettings;
  };
}

// Article model
export interface Article {
  sys: ContentfulSys;
  contentType: 'article';
  fields: {
    title: string;
    slug: string;
    excerpt: string;
    content: RichTextContent;
    featuredImage: ContentfulAsset;
    author: Author;
    publishDate: string;
    tags: Tag[];
    category: Category;
  };
}
```

**System Models:**
```typescript
// Contentful system fields
export interface ContentfulSys {
  id: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  locale: string;
  revision: number;
}

// Contentful asset
export interface ContentfulAsset {
  sys: ContentfulSys;
  fields: {
    title: string;
    description?: string;
    file: {
      url: string;
      details: {
        size: number;
        image?: {
          width: number;
          height: number;
        };
      };
      fileName: string;
      contentType: string;
    };
  };
}
```

**Usage:**
```typescript
import { 
  LandingPage, 
  HeroComponent, 
  Article,
  LandingPageSchema 
} from '@gp/model/cms';

// Type-safe CMS content
const landingPage: LandingPage = {
  sys: { /* sys fields */ },
  contentType: 'landingPage',
  fields: {
    title: 'Home Loans Made Easy',
    slug: 'home-loans',
    metaDescription: 'Get the best home loan rates...',
    components: [
      {
        sys: { /* sys fields */ },
        contentType: 'hero',
        fields: {
          heading: 'Find Your Perfect Home Loan',
          subtitle: 'Expert guidance every step of the way',
          backgroundImage: { /* asset */ },
          ctaButtons: [
            {
              label: 'Get Started',
              href: '/apply',
              variant: 'primary',
              tracking: {
                position: 'Hero',
                purpose: 'Lead Generation'
              }
            }
          ],
          trustBar: true
        }
      }
    ],
    seoSettings: { /* SEO settings */ }
  }
};
```

### 3. Customer Models (`@gp/model/customer`)
Customer-related data models and types.

**Customer Entity:**
```typescript
export interface Customer {
  id: string;
  personalDetails: PersonalDetails;
  contactDetails: ContactDetails;
  preferences: CustomerPreferences;
  status: CustomerStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface PersonalDetails {
  firstName: string;
  lastName: string;
  dateOfBirth?: Date;
  gender?: Gender;
  maritalStatus?: MaritalStatus;
  dependents?: number;
}

export interface ContactDetails {
  email: string;
  phone?: string;
  address?: Address;
  preferredContactMethod: ContactMethod;
  communicationPreferences: CommunicationPreferences;
}

export interface Address {
  street: string;
  suburb: string;
  state: string;
  postcode: string;
  country: string;
}

export enum CustomerStatus {
  PROSPECT = 'prospect',
  LEAD = 'lead',
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export enum ContactMethod {
  EMAIL = 'email',
  PHONE = 'phone',
  SMS = 'sms'
}
```

**Financial Information:**
```typescript
export interface CustomerFinancials {
  customerId: string;
  income: IncomeDetails;
  expenses: ExpenseDetails;
  assets: AssetDetails;
  liabilities: LiabilityDetails;
  creditScore?: number;
  lastUpdated: Date;
}

export interface IncomeDetails {
  primaryIncome: Income;
  secondaryIncome?: Income;
  otherIncome: Income[];
  totalAnnualIncome: number;
}

export interface Income {
  type: IncomeType;
  amount: number;
  frequency: IncomeFrequency;
  employer?: string;
  verified: boolean;
}

export enum IncomeType {
  SALARY = 'salary',
  WAGE = 'wage',
  SELF_EMPLOYED = 'self-employed',
  PENSION = 'pension',
  INVESTMENT = 'investment',
  OTHER = 'other'
}
```

**Usage:**
```typescript
import { 
  Customer, 
  PersonalDetails, 
  CustomerFinancials,
  CustomerStatus,
  CustomerSchema 
} from '@gp/model/customer';

const customer: Customer = {
  id: 'customer-123',
  personalDetails: {
    firstName: 'Jane',
    lastName: 'Doe',
    dateOfBirth: new Date('1985-06-15'),
    maritalStatus: 'married',
    dependents: 2
  },
  contactDetails: {
    email: '<EMAIL>',
    phone: '+61412345678',
    preferredContactMethod: ContactMethod.EMAIL,
    communicationPreferences: {
      marketing: true,
      updates: true,
      newsletters: false
    }
  },
  preferences: {
    loanTypes: ['home-loan', 'investment'],
    communicationFrequency: 'weekly'
  },
  status: CustomerStatus.ACTIVE,
  createdAt: new Date(),
  updatedAt: new Date()
};
```

### 4. Property Hub Models (`@gp/model/property-hub`)
Comprehensive property-related data models.

**Property Models:**
```typescript
export interface Property {
  id: string;
  address: PropertyAddress;
  details: PropertyDetails;
  valuation: PropertyValuation;
  market: PropertyMarket;
  features: PropertyFeatures;
  location: PropertyLocation;
  history: PropertyHistory[];
  images: PropertyImage[];
  status: PropertyStatus;
}

export interface PropertyAddress {
  street: string;
  suburb: string;
  state: string;
  postcode: string;
  unitNumber?: string;
  streetNumber: string;
  streetName: string;
  streetType: string;
}

export interface PropertyDetails {
  propertyType: PropertyType;
  bedrooms: number;
  bathrooms: number;
  carSpaces: number;
  landSize?: number;
  floorArea?: number;
  yearBuilt?: number;
  zoning?: string;
}

export interface PropertyValuation {
  currentValue: number;
  valuationDate: Date;
  confidence: ValuationConfidence;
  source: ValuationSource;
  priceHistory: PriceHistory[];
  comparables: ComparableProperty[];
}

export enum PropertyType {
  HOUSE = 'house',
  UNIT = 'unit',
  TOWNHOUSE = 'townhouse',
  APARTMENT = 'apartment',
  LAND = 'land',
  COMMERCIAL = 'commercial'
}

export enum PropertyStatus {
  FOR_SALE = 'for-sale',
  SOLD = 'sold',
  FOR_RENT = 'for-rent',
  RENTED = 'rented',
  OFF_MARKET = 'off-market'
}
```

**Agent Models:**
```typescript
export interface Agent {
  id: string;
  name: string;
  agency: Agency;
  contact: AgentContact;
  specialties: AgentSpecialty[];
  performance: AgentPerformance;
  listings: PropertyListing[];
}

export interface Agency {
  id: string;
  name: string;
  address: Address;
  contact: ContactDetails;
  licenseNumber: string;
  established: Date;
}

export interface AgentPerformance {
  salesCount: number;
  averageSalePrice: number;
  averageDaysOnMarket: number;
  clientSatisfactionRating: number;
  reviewCount: number;
}
```

**Usage:**
```typescript
import { 
  Property, 
  PropertyType, 
  PropertyStatus,
  Agent,
  PropertySchema 
} from '@gp/model/property-hub';

const property: Property = {
  id: 'prop-123',
  address: {
    street: '123 Example Street',
    suburb: 'Sydney',
    state: 'NSW',
    postcode: '2000',
    streetNumber: '123',
    streetName: 'Example',
    streetType: 'Street'
  },
  details: {
    propertyType: PropertyType.HOUSE,
    bedrooms: 3,
    bathrooms: 2,
    carSpaces: 2,
    landSize: 600,
    floorArea: 180,
    yearBuilt: 1995
  },
  valuation: {
    currentValue: 850000,
    valuationDate: new Date(),
    confidence: 'high',
    source: 'corelogic',
    priceHistory: [],
    comparables: []
  },
  status: PropertyStatus.FOR_SALE,
  // ... other fields
};
```

## Model Definition Patterns

### 1. Base Entity Pattern
```typescript
// Base entity with common fields
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

// Extend base entity
export interface SpecificEntity extends BaseEntity {
  name: string;
  description: string;
  status: EntityStatus;
}
```

### 2. Enum Definitions
```typescript
// Use enums for controlled vocabularies
export enum LoanType {
  HOME_LOAN = 'home-loan',
  INVESTMENT_LOAN = 'investment-loan',
  REFINANCE = 'refinance',
  CONSTRUCTION_LOAN = 'construction-loan',
  BRIDGING_LOAN = 'bridging-loan'
}

export enum LoanStatus {
  APPLICATION = 'application',
  ASSESSMENT = 'assessment',
  APPROVED = 'approved',
  SETTLED = 'settled',
  REJECTED = 'rejected'
}
```

### 3. Union Types
```typescript
// Use union types for flexible data structures
export type ContactMethod = 'email' | 'phone' | 'sms' | 'post';
export type PropertyListingType = 'sale' | 'rent' | 'sold' | 'leased';

// Discriminated unions
export interface EmailContact {
  type: 'email';
  address: string;
  verified: boolean;
}

export interface PhoneContact {
  type: 'phone';
  number: string;
  countryCode: string;
  verified: boolean;
}

export type Contact = EmailContact | PhoneContact;
```

### 4. Generic Types
```typescript
// Generic response wrapper
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: ValidationError[];
}

// Generic pagination
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Usage
export type PropertySearchResponse = PaginatedResponse<Property>;
export type BrokerListResponse = ApiResponse<Broker[]>;
```

### 5. Validation Schemas
```typescript
import { z } from 'zod';

// Zod schema for runtime validation
export const PropertySchema = z.object({
  id: z.string(),
  address: z.object({
    street: z.string(),
    suburb: z.string(),
    state: z.string().length(3),
    postcode: z.string().regex(/^\d{4}$/),
  }),
  details: z.object({
    propertyType: z.nativeEnum(PropertyType),
    bedrooms: z.number().min(0).max(10),
    bathrooms: z.number().min(0).max(10),
    carSpaces: z.number().min(0).max(10),
    landSize: z.number().positive().optional(),
  }),
  valuation: z.object({
    currentValue: z.number().positive(),
    valuationDate: z.date(),
    confidence: z.enum(['low', 'medium', 'high']),
  }),
});

// Infer TypeScript type from schema
export type Property = z.infer<typeof PropertySchema>;

// Validation function
export function validateProperty(data: unknown): Property {
  return PropertySchema.parse(data);
}
```

## Best Practices

### 1. Naming Conventions
```typescript
// Use descriptive interface names
export interface CustomerApplicationForm {
  personalDetails: PersonalDetails;
  financialInformation: FinancialInformation;
  loanRequirements: LoanRequirements;
}

// Use consistent naming for related types
export interface CreateCustomerRequest {
  personalDetails: PersonalDetails;
  contactDetails: ContactDetails;
}

export interface UpdateCustomerRequest {
  personalDetails?: Partial<PersonalDetails>;
  contactDetails?: Partial<ContactDetails>;
}

export interface CustomerResponse extends Customer {
  links: HateoasLinks;
}
```

### 2. Optional vs Required Fields
```typescript
// Be explicit about optional fields
export interface CustomerProfile {
  // Required fields
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  
  // Optional fields
  phone?: string;
  dateOfBirth?: Date;
  profileImage?: string;
  preferences?: CustomerPreferences;
}

// Use utility types for variations
export type CreateCustomerProfile = Omit<CustomerProfile, 'id'>;
export type UpdateCustomerProfile = Partial<Pick<CustomerProfile, 'phone' | 'dateOfBirth' | 'profileImage' | 'preferences'>>;
```

### 3. Composition over Inheritance
```typescript
// Compose complex types from smaller interfaces
export interface ContactInformation {
  email: string;
  phone?: string;
  address?: Address;
}

export interface PersonalInformation {
  firstName: string;
  lastName: string;
  dateOfBirth?: Date;
}

export interface Customer extends PersonalInformation, ContactInformation {
  id: string;
  status: CustomerStatus;
  createdAt: Date;
}
```

### 4. Documentation
```typescript
/**
 * Represents a property listing in the system
 * 
 * @example
 * ```typescript
 * const listing: PropertyListing = {
 *   id: 'listing-123',
 *   property: propertyData,
 *   agent: agentData,
 *   price: 850000,
 *   listingType: PropertyListingType.SALE,
 *   status: ListingStatus.ACTIVE
 * };
 * ```
 */
export interface PropertyListing {
  /** Unique identifier for the listing */
  id: string;
  
  /** Property being listed */
  property: Property;
  
  /** Listing agent */
  agent: Agent;
  
  /** Listing price in cents */
  price: number;
  
  /** Type of listing (sale, rent, etc.) */
  listingType: PropertyListingType;
  
  /** Current status of the listing */
  status: ListingStatus;
  
  /** Date when listing was created */
  createdAt: Date;
  
  /** Date when listing expires */
  expiresAt?: Date;
}
```

## Testing Models

### 1. Type Testing
```typescript
// Test type definitions with utility types
type TestCustomer = Customer;

// Ensure required fields are present
type RequiredCustomerFields = Required<Pick<Customer, 'id' | 'email' | 'firstName' | 'lastName'>>;

// Test that optional fields are optional
type OptionalCustomerFields = Pick<Customer, 'phone' | 'dateOfBirth'>;
const partialCustomer: OptionalCustomerFields = {}; // Should not error
```

### 2. Schema Testing
```typescript
describe('PropertySchema', () => {
  it('validates correct property data', () => {
    const validProperty = {
      id: 'prop-123',
      address: {
        street: '123 Test St',
        suburb: 'Sydney',
        state: 'NSW',
        postcode: '2000'
      },
      details: {
        propertyType: PropertyType.HOUSE,
        bedrooms: 3,
        bathrooms: 2,
        carSpaces: 2
      },
      valuation: {
        currentValue: 800000,
        valuationDate: new Date(),
        confidence: 'high'
      }
    };
    
    expect(() => PropertySchema.parse(validProperty)).not.toThrow();
  });
  
  it('rejects invalid postcode format', () => {
    const invalidProperty = {
      // ... valid fields
      address: {
        // ... valid fields
        postcode: 'INVALID'
      }
    };
    
    expect(() => PropertySchema.parse(invalidProperty)).toThrow();
  });
});
```

### 3. Mock Data Generation
```typescript
// Factory functions for test data
export function createMockCustomer(overrides: Partial<Customer> = {}): Customer {
  return {
    id: 'customer-123',
    personalDetails: {
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1985-06-15'),
    },
    contactDetails: {
      email: '<EMAIL>',
      phone: '+61412345678',
      preferredContactMethod: ContactMethod.EMAIL,
    },
    status: CustomerStatus.ACTIVE,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Usage in tests
const testCustomer = createMockCustomer({
  personalDetails: {
    firstName: 'Jane',
    lastName: 'Smith'
  }
});
```