# Aussie Theme Guidelines

## Typography

### Fonts
- Primary Font: `<PERSON>, sans-serif` for body text
- Heading Font: `tt-commons-pro, sans-serif` [[memory:5437836]] for headings and disclaimers
- Font Weight: 600 for headings

### Font Sizes
```typescript
{
  xxs: 10px
  xs: 12px
  sm: 14px
  md: 16px
  lg: 18px
  xl: 24px
  xxl: 28px
}
```

### Heading Sizes
```typescript
{
  h1: 46px/50px
  h2: 37px/40px
  h3: 28px/32px
  h4: 22px/28px
  h5: 18px/24px
  h6: 14px/20px
}
```

## Colors

### Primary Colors
- Primary: Grape (#4b1f68) - Used for main actions and primary branding
- Secondary: Yellow (#ffd11f) - Used for highlights and secondary actions
- Tertiary: Teal (#45d9ba) - Used for accents and tertiary elements

### Neutral Colors
- Gray Scale: #f8f8f6 to #1d1d1d
- Text Colors: Use appropriate gray shades for different text hierarchies

### Semantic Colors
- Success: Green (#2cac41)
- Error: Red (#ff282f)
- Warning: Orange (#fc7426)
- Info: Blue (#1898f5)
- Link: Indigo (#5e72ff)

## Spacing

### Scale
```typescript
{
  xxxs: 4px
  xxs: 8px
  xs: 12px
  sm: 16px
  md: 24px
  lg: 32px
  xl: 40px
  xxl: 48px
  xxxl: 64px
  xxxxl: 80px
}
```

## Shadows

### Elevation Scale
```typescript
{
  xs: '0 0 2px 0 rgba(0, 0, 0, 0.1), 0 2px 2px 0 rgba(0, 0, 0, 0.12), 0 1px 3px 0 rgba(0, 0, 0, 0.14)'
  sm: '0 3px 3px 0 rgba(0, 0, 0, 0.1), 0 3px 4px 0 rgba(0, 0, 0, 0.12), 0 1px 8px 0 rgba(0, 0, 0, 0.14)'
  md: '0 6px 10px 0 rgba(0, 0, 0, 0.1), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px 0 rgba(0, 0, 0, 0.14)'
  lg: '0 12px 17px 2px rgba(0, 0, 0, 0.1), 0 5px 22px 4px rgba(0, 0, 0, 0.12), 0 7px 8px 0 rgba(0, 0, 0, 0.14)'
}
```

## Components

### Available Components
The theme includes styled variants for:
- Accordion
- Alert
- Area Chart
- Badge
- Bar Chart
- Button
- Card
- Carousel
- Chart Legend
- Checkbox
- Combobox
- Container
- Divider
- Donut Chart
- Input
- List
- Modal
- Pagination
- Paper
- Radio
- Slider
- Tab
- Text
- Timeline
- Title

## Usage Guidelines

### Theme Implementation
1. Always import the theme from `libs/theme/aussie`
2. Use the Mantine theme provider at the root level
3. Utilize theme tokens instead of hard-coded values

### Best Practices
1. Use semantic color tokens instead of raw color values
2. Follow the spacing scale for consistent layouts
3. Maintain typography hierarchy using defined font sizes
4. Apply shadows consistently for elevation
5. Use provided component variants when available

### Accessibility
1. Maintain color contrast ratios (WCAG 2.1)
2. Use semantic heading levels
3. Ensure interactive elements have proper focus states
4. Provide sufficient color contrast for text

### Responsive Design
1. Use rem units for scalable typography
2. Follow mobile-first approach
3. Use theme breakpoints for consistent media queries

### Performance
1. Import only required component styles
2. Use CSS modules for component-specific styles
3. Avoid runtime theme modifications

## Code Examples

### Theme Import
```typescript
import { aussieTheme } from '@growth-product/theme/aussie';
```

### Component Usage
```typescript
import { MantineProvider } from '@mantine/core';
import { aussieTheme } from '@growth-product/theme/aussie';

export function App() {
  return (
    <MantineProvider theme={aussieTheme}>
      <YourApp />
    </MantineProvider>
  );
}
```

### Styling with Theme
```typescript
import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.gray[0],
  },
  heading: {
    fontFamily: theme.headings.fontFamily,
    fontSize: theme.headings.sizes.h2.fontSize,
    lineHeight: theme.headings.sizes.h2.lineHeight,
  },
}));
```