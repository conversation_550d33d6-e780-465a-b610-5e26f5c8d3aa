'use client';
import { Container, Stack, Text, Title } from '@mantine/core';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { Brand } from '@lendi/lala-utils';

import { CompetitionBanner } from '@gp/ui/blocks';
import {
  AnchorWithTracking,
  LinkButtonWithTracking,
  PageTracking,
  ProductReviewWidget,
} from '@gp/ui/components';
import { ArrowForward } from '@gp/ui/icons';

import FunnelFAQs from '../FunnelFAQs';

export default function BookAppointment() {
  const { gpDoorToMore } = useFlags();
  return (
    <PageTracking name="Basics page" category="FUNNELS">
      <Stack gap={48} align="center" flex="1">
        <Container maw={800} pt={48} px={16}>
          <Stack align="center">
            <Title order={1} size="h2" fw={600} c="primary" ta="center">
              {gpDoorToMore
                ? 'Win a share of $320K with Aussie'
                : 'Fast-track your home loan today'}
            </Title>
            <Text size="lg">
              {gpDoorToMore
                ? 'Book and attend an appointment with an Aussie Broker for your chance to win the grand prize of $200K or a $10K weekly cash prize!^ Designed to be flexible, our home loan products offer a variety of features and benefits to help you reach your goals – no matter where you are on your property journey.'
                : 'For over 30 years, Aussie has helped millions of Australians achieve their property dreams. Designed to be flexible, our home loan products offer a variety of features and benefits to help you reach your goals – no matter where you are on your property journey.'}
            </Text>
            <Text size="lg">
              Let’s fast-track your journey, simply book a free appointment* to receive expert
              guidance and explore your personalised options – with no obligation. Our brokers will
              compare thousands of loans from our panel to find the one that’s right for you.
            </Text>
          </Stack>
        </Container>
        <Container maw={570} px={16}>
          <Stack gap="sm">
            <LinkButtonWithTracking
              rightSection={<ArrowForward size={24} />}
              label="Chat to a broker"
              href="/book-appointment/"
              isExternalLink
            />
          </Stack>
        </Container>
        <Container px={16}>
          {gpDoorToMore && <CompetitionBanner />}
          <Stack gap="xs" align="center">
            <ProductReviewWidget brand={Brand.Aussie} />
            <Text>*Lender-charged fees will apply.</Text>
            {gpDoorToMore && (
              <Text>
                ^AU18+ only. Ends 29/6/25 11:59pm AEST. Entry limits apply (see full T&Cs for
                details).{' '}
                <AnchorWithTracking target="_blank" href="/about-us/promotions/" label="T&Cs">
                  T&Cs
                </AnchorWithTracking>{' '}
                apply.
              </Text>
            )}
          </Stack>
        </Container>
        <Container fluid bg="gray.0" py={48} px={16} flex="1">
          <FunnelFAQs type="BookAppointment" />
        </Container>
      </Stack>
    </PageTracking>
  );
}
