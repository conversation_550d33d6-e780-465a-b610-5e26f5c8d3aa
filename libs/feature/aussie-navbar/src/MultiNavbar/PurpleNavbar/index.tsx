'use client';

import { useEffect, useState } from 'react';
import { Anchor, Group } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import clsx from 'clsx';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { Status, useSession } from '@lendi/lala-react';

import { Aussie } from '@gp/ui/icons';

import { NavContext, setNavBrokerName, setNavCustomerApplication } from '../../context';
import Actions from './Actions';
import { NAV_LIST, NAV_LIST_EXPERIMENT } from './constants';
import NavLinks from './NavLinks';

import classes from './style.module.css';

interface NavBarProps {
  withActions?: boolean;
}

export default function PurpleNavbar({ withActions = true }: NavBarProps) {
  const { status, token, identity } = useSession();
  const [brokerName, setBrokerName] = useState('a broker');
  const [customerApplication, setCustomerApplication] = useState('');
  const { width } = useViewportSize();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const hideNavBreakPoint = isAuthenticated ? 1108 : 1180;

  const { gpNeedsBasedNavBarExperiment } = useFlags();
  const NAV_LIST_TO_USE = gpNeedsBasedNavBarExperiment ? NAV_LIST_EXPERIMENT : NAV_LIST;

  const shouldShowNavLinks = width === 0 ? true : width >= hideNavBreakPoint;

  useEffect(() => {
    setIsAuthenticated(status === Status.Authenticated);
    setNavBrokerName(setBrokerName, token);
    setNavCustomerApplication(identity?.id || '', setCustomerApplication, token);
  }, [status, token, identity]);

  return (
    <nav className={classes.nav}>
      <NavContext.Provider value={{ brokerName, application: customerApplication }}>
        <Group
          align="center"
          className={clsx(classes.navbar, isAuthenticated && classes.authenticated)}
          gap={0}
          wrap="nowrap"
          justify={shouldShowNavLinks ? 'flex-start' : 'space-between'}
        >
          <Group gap={0} mr="lg">
            <Anchor href="/" className={classes.logo}>
              <Aussie color="white" />
            </Anchor>
          </Group>
          {shouldShowNavLinks && <NavLinks menu={NAV_LIST_TO_USE} />}
          {withActions ? <Actions /> : null}
        </Group>
      </NavContext.Provider>
    </nav>
  );
}
