.nav {
  position: sticky;
  top: 0;
  z-index: var(--mantine-z-index-app);
  background-color: var(--mantine-color-grape-8);
  box-shadow: var(--mantine-shadow-sm);
  padding: 0 1rem;

  &.hidden {
    display: none;
  }
}

.navbar {
  height: 77px;
  max-width: 1272px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: start;

  svg {
    width: 99px;
    height: 18.5px;
  }
}

.baseNavLink{
  font-family: tt-commons-pro, sans-serif;
  font-size: var(--mantine-font-size-sm);
  line-height: 20px;
  font-weight: 600;
  color: white;
  height: 39px;
  span{
    margin-inline: 0;
  }
  &:hover{
    background: unset;
    color: var(--mantine-color-yellow-5);
    svg{
      color: var(--mantine-color-yellow-5);
    }
  }
}

.baseNavLink[aria-expanded=true] {
  & > .baseNavLinkSection{
    transform: rotate(180deg);
  }
}

.baseNavLinkSection:where([data-rotate]){
  transform: rotate(180deg);
}

.baseNavLinkItem{
  font-family: 'Barlow';
  line-height: 24px;
  font-weight: 400;
  color: white;
  background-color: unset;
  span{
    margin-inline: 0;
    font-size: var(--mantine-font-size-md);
  }
}