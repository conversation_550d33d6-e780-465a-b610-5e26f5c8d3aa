import { useContext } from 'react';
import { Group, Menu as MantineMenu, NavLink, Stack, Title } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import { useFlags } from 'launchdarkly-react-client-sdk';
import WebAnalytics, { EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { LinkButtonWithTracking } from '@gp/ui/components';
import { CallMade, ChevronDown } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import { NavContext } from '../../../context';
import { ActionHamburger } from '../ActionHamburger';
import { ACTION_LIST, MENU_ITEMS, MY_ACCOUNT_LIST } from '../constants';
import { Hamburger } from '../Hamburger';
import { onMenuItemClick, onNavLinkClick } from '../tracking';

import dropdownClasses from '../Dropdown/style.module.css';
import baseClasses from '../style.module.css';
import classes from './style.module.css';

const MenuTarget = ({
  label,
  path,
  onClick,
}: {
  label: string;
  path?: string;
  onClick?: () => void;
}) => {
  const { analytics } = useContext(LDContext);
  return (
    <MantineMenu.Target>
      <NavLink
        h="100%"
        p={0}
        w="fit-content"
        label={label}
        classNames={{ root: baseClasses.baseNavLink, section: baseClasses.baseNavLinkSection }}
        rightSection={<ChevronDown color="white" size={21} />}
        onClick={onNavLinkClick(
          { label, path, menuName: label },
          analytics as WebAnalytics,
          onClick
        )}
      />
    </MantineMenu.Target>
  );
};

const MenuItem = ({
  label,
  path,
  onClick,
  analytics,
}: {
  label: string;
  path?: string;
  analytics?: WebAnalytics;
  onClick?: () => void;
}) => {
  return (
    <MantineMenu.Item py="xs" px="sm" className={dropdownClasses.navDropdownItem}>
      <NavLink
        p={0}
        w="100%"
        label={label}
        className={baseClasses.baseNavLinkItem}
        rightSection={<CallMade size={24} color="white" />}
        href={path}
        onClick={onMenuItemClick({ label, path }, analytics as WebAnalytics, onClick)}
      />
    </MantineMenu.Item>
  );
};

const AuthedMenuItem = ({
  label,
  path,
  onClick,
  analytics,
}: {
  label: string;
  path?: string;
  analytics?: WebAnalytics;
  onClick?: () => void;
}) => (
  <MantineMenu.Item py="xs" px="sm" className={dropdownClasses.navDropdownItem}>
    <NavLink
      p={0}
      w="100%"
      label={label}
      href={path}
      className={baseClasses.baseNavLinkItem}
      onClick={onMenuItemClick({ label, path }, analytics as WebAnalytics, onClick)}
    />
  </MantineMenu.Item>
);

enum NAV_LAYOUT {
  TWO_ELEMENTS = 'TWO_ELEMENTS',
  ONE_ELEMENT = 'ONE_ELEMENT',
  THREE_ELEMENTS = 'THREE_ELEMENTS',
}

const DEFAULT_DASHBOARD_URL = '/v2/dashboard/';

const AuthedAuthMenu = () => {
  const session = useSession();
  const { brokerName, application } = useContext(NavContext);
  const { gpBookAppointmentInterim } = useFlags();
  const bookAppointmentCtaUrl = gpBookAppointmentInterim
    ? '/home-loans/basics/appointment/'
    : '/book-appointment/';
  return (
    <MantineMenu
      width={300}
      position="bottom-end"
      offset={0}
      loop={false}
      withinPortal={false}
      trapFocus={false}
      menuItemTabIndex={0}
    >
      <MenuTarget label="My account" />
      <MantineMenu.Dropdown className={dropdownClasses.navDropdown} bg="grape.8" pt={0} pb="sm">
        <MantineMenu.Label px="sm" c="white" py="xs" className={classes.menuLabel}>
          Manage your loan applications
        </MantineMenu.Label>
        <MantineMenu.Divider mx="sm" my="xs" />
        <Stack gap="xs" px="sm" py="xs">
          <LinkButtonWithTracking
            fullWidth
            label={application ? 'Continue application' : 'Apply now'}
            variant="highlight"
            href="/home-loans/basics"
            category={EventCategory.NAVBAR}
            px="sm"
            fz="md"
            styles={{
              label: { margin: 0, textTransform: 'none' },
            }}
          />
          <LinkButtonWithTracking
            fullWidth
            variant="outline_inverse"
            href={embedMarketingTrackingParams(bookAppointmentCtaUrl)}
            category={EventCategory.NAVBAR}
            px="sm"
            fz="md"
            label={`Talk to ${brokerName}`}
            styles={{
              label: { margin: 0, textTransform: 'none' },
            }}
          />
        </Stack>
        {MY_ACCOUNT_LIST.map((i) => (
          <AuthedMenuItem label={i.label} key={i.label} path={i.link} />
        ))}
        <AuthedMenuItem label={MENU_ITEMS.SIGN_OUT.label} onClick={session.logout} />
      </MantineMenu.Dropdown>
    </MantineMenu>
  );
};

const UnauthedAuthMenu = () => {
  return (
    <MantineMenu
      width={300}
      position="bottom-end"
      offset={0}
      loop={false}
      withinPortal={false}
      trapFocus={false}
      menuItemTabIndex={0}
    >
      <MenuTarget label="Login" />
      <MantineMenu.Dropdown className={dropdownClasses.navDropdown} bg="grape.9" pt="xs" pb={0}>
        <MantineMenu.Label px="sm" c="white" py="xs" className={classes.menuLabel}>
          Online banking login
        </MantineMenu.Label>
        {ACTION_LIST.map((i) => (
          <MenuItem label={i.label} path={i.link} key={i.label} />
        ))}
        <Stack
          bg="var(--mantine-color-grape-8)"
          px="sm"
          pb="sm"
          pt="xs"
          mt="sm"
          align="stretch"
          justify="center"
          gap="xs"
          className={classes.menuCTA}
        >
          <Title c="white" size="lg" lh="h6" my="xs">
            Loan application login
          </Title>
          <LinkButtonWithTracking
            variant="outline_inverse"
            label="Manage application"
            fz="md"
            href={`/sign-in/${generateReturnURL([DEFAULT_DASHBOARD_URL])}`}
            styles={{ label: { textTransform: 'none' } }}
          />
        </Stack>
      </MantineMenu.Dropdown>
    </MantineMenu>
  );
};

const AuthMenu = ({ authenticated }: LayoutProp) => {
  if (authenticated) {
    return <AuthedAuthMenu />;
  }
  return <UnauthedAuthMenu />;
};

interface LayoutProp {
  authenticated: boolean;
}

const ApplyNowButton = ({ label, noMargin }: { label?: string; noMargin?: boolean }) => (
  <LinkButtonWithTracking
    variant="highlight"
    mr={noMargin ? 0 : 'sm'}
    href="/home-loans/basics"
    category={EventCategory.NAVBAR}
    label={label || 'Apply now'}
    size="sm"
    px="sm"
    fz="sm"
    styles={{
      label: { margin: 0, textTransform: 'none' },
    }}
  />
);

const TalkToBrokerButton = ({ noMargin }: { noMargin?: boolean }) => {
  const { brokerName } = useContext(NavContext);
  const { gpBookAppointmentInterim } = useFlags();
  const bookAppointmentCtaUrl = gpBookAppointmentInterim
    ? '/home-loans/basics/appointment'
    : '/book-appointment/';

  let formattedBrokerName = brokerName;
  if (brokerName.length > 10) {
    const first10 = formattedBrokerName.substring(0, 10);
    formattedBrokerName = `${first10}...`;
  }
  return (
    <LinkButtonWithTracking
      variant="outline_inverse"
      size="sm"
      mr={noMargin ? 0 : 'sm'}
      px="sm"
      tt="none"
      fz="sm"
      label={`Talk to ${formattedBrokerName}`}
      category={EventCategory.NAVBAR}
      href={embedMarketingTrackingParams(bookAppointmentCtaUrl)}
      styles={{
        label: { margin: 0, textTransform: 'none' },
      }}
    />
  );
};

const ThreeElementsLayout = ({ authenticated }: LayoutProp) => {
  return (
    <Group align="center" gap={0} h="100%" wrap="nowrap" ml="md">
      <TalkToBrokerButton />
      {!authenticated && <ApplyNowButton />}
      <AuthMenu authenticated={authenticated} />
    </Group>
  );
};

const TwoElementsLayout = ({ authenticated }: LayoutProp) => {
  return (
    <Group align="center" gap="md" h="100%" wrap="nowrap" ml="md">
      {!authenticated && <ApplyNowButton noMargin />}
      <AuthMenu authenticated={authenticated} />
      <Hamburger />
    </Group>
  );
};

const OneElementLayout = ({ authenticated }: LayoutProp) => {
  return (
    <Group align="center" gap="sm" h="100%" wrap="nowrap" ml="md">
      <ActionHamburger />
      <Hamburger />
    </Group>
  );
};

export default function Actions() {
  const { status } = useSession();
  const { width } = useViewportSize();
  const authenticated = status === Status.Authenticated;
  let layoutStatus = NAV_LAYOUT.THREE_ELEMENTS;
  const hideNavBreakPoint = authenticated ? 1108 : 1180;
  if (width < hideNavBreakPoint && width > 576) {
    layoutStatus = NAV_LAYOUT.TWO_ELEMENTS;
  } else if (width <= 576) {
    layoutStatus = NAV_LAYOUT.ONE_ELEMENT;
  }
  switch (layoutStatus) {
    case NAV_LAYOUT.THREE_ELEMENTS:
      return <ThreeElementsLayout authenticated={authenticated} />;
    case NAV_LAYOUT.TWO_ELEMENTS:
      return <TwoElementsLayout authenticated={authenticated} />;
    case NAV_LAYOUT.ONE_ELEMENT:
      return <OneElementLayout authenticated={authenticated} />;
  }
}
