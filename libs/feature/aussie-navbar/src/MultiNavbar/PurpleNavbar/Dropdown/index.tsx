import { useContext } from 'react';
import { <PERSON><PERSON>, Menu as MantineMenu, NavLink } from '@mantine/core';
import { useFlags } from 'launchdarkly-react-client-sdk';
import WebAnalytics from '@lendi/analytics-web';

import { LDContext } from '@gp/shared/launchdarkly';
import { ChevronDown } from '@gp/ui/icons';

import { MENU_ITEMS, NavItem } from '../constants';
import { onNavLinkClick } from '../tracking';

import baseClasses from '../style.module.css';
import classes from './style.module.css';

const DropdownItem = ({
  label,
  path,
  menuName,
}: {
  label: string;
  menuName: string;
  path?: string;
}) => {
  const { analytics } = useContext(LDContext);
  return (
    <MantineMenu.Item py="xs" className={classes.navDropdownItem}>
      <NavLink
        p={0}
        noWrap
        w="fit-content"
        label={label}
        href={path}
        className={baseClasses.baseNavLinkItem}
        onClick={onNavLinkClick({ label, path, menuName }, analytics as WebAnalytics)}
      />
    </MantineMenu.Item>
  );
};

const DropdownTarget = ({ label, path }: { label: string; path?: string }) => {
  const { analytics } = useContext(LDContext);
  return (
    <MantineMenu.Target>
      <NavLink
        p={0}
        w="fit-content"
        noWrap
        label={label}
        href={path}
        classNames={{ root: baseClasses.baseNavLink, section: baseClasses.baseNavLinkSection }}
        rightSection={<ChevronDown color="white" size={21} />}
        onClick={onNavLinkClick({ label, path, menuName: label }, analytics as WebAnalytics)}
      />
    </MantineMenu.Target>
  );
};

export const NavDropdown = ({ target, list }: { target: NavItem; list: NavItem[] }) => {
  const { gpPropertyHubBeta, gpHeaderBuyersAgentNew } = useFlags();
  const { analytics } = useContext(LDContext);
  if (target.label === MENU_ITEMS.PROPERTY.label && gpPropertyHubBeta) {
    target.badge = 'BETA';
  }
  if (target.label === MENU_ITEMS.BUYERS_AGENTS.label && gpHeaderBuyersAgentNew) {
    target.badge = 'NEW';
  }
  if (list.length <= 0)
    return (
      <NavLink
        noWrap
        p={0}
        w="fit-content"
        label={
          target.badge ? (
            <>
              {target.label}
              <Badge
                ml="xxs"
                size="md"
                variant="filled"
                color="yellow.5"
                styles={{
                  label: {
                    color: 'var(--mantine-color-grape-8)',
                    fontSize: 'var(--mantine-font-size-xs)',
                  },
                }}
              >
                {target.badge}
              </Badge>
            </>
          ) : (
            target.label
          )
        }
        className={baseClasses.baseNavLink}
        onClick={onNavLinkClick(
          { label: target.label, path: target.link, menuName: target.label },
          analytics as WebAnalytics
        )}
        href={target.link}
      />
    );
  return (
    <MantineMenu
      position="bottom-start"
      offset={{ crossAxis: -16 }}
      loop={false}
      withinPortal={false}
      trapFocus={false}
      menuItemTabIndex={0}
    >
      <DropdownTarget label={target.label} path={target.link} />
      <MantineMenu.Dropdown className={classes.navDropdown} pt="sm">
        {list.map((i) => (
          <DropdownItem menuName={target.label} key={i.label} path={i.link} label={i.label} />
        ))}
      </MantineMenu.Dropdown>
    </MantineMenu>
  );
};
