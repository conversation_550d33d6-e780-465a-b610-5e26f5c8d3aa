import { PropsWith<PERSON>hildren, useContext } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, NavLink, Stack } from '@mantine/core';
import { useDisclosure, useViewportSize } from '@mantine/hooks';
import clsx from 'clsx';
import { useFlags } from 'launchdarkly-react-client-sdk';
import WebAnalytics, { EventCategory } from '@lendi/analytics-web';

import { LDContext } from '@gp/shared/launchdarkly';
import { LinkButtonWithTracking } from '@gp/ui/components';
import { Menu } from '@gp/ui/icons';
import { embedMarketingTrackingParams } from '@gp/util/url';

import { NavContext } from '../../../context';
import { MENU_ITEMS, NAV_LIST, NAV_LIST_EXPERIMENT } from '../constants';
import { onNavLinkClick } from '../tracking';

import baseClasses from '../style.module.css';
import classes from './style.module.css';

const MenuItem = ({
  label,
  path,
  menuName,
}: {
  label: string;
  menuName: string;
  path?: string;
}) => {
  const { analytics } = useContext(LDContext);
  return (
    <NavLink
      py={0}
      px="sm"
      noWrap
      label={label}
      href={path}
      className={clsx(baseClasses.baseNavLinkItem, classes.hamburgeNavLinkItem)}
      onClick={onNavLinkClick({ label, menuName, path }, analytics as WebAnalytics)}
    />
  );
};

const MenuTarget = ({
  label,
  children,
  path,
  badge,
}: PropsWithChildren<{ label: string; path?: string; badge?: string }>) => {
  const { analytics } = useContext(LDContext);
  return (
    <NavLink
      noWrap
      h={48}
      styles={{ label: { fontSize: 'var(--mantine-font-size-md)' } }}
      classNames={{
        root: baseClasses.baseNavLink,
        section: baseClasses.baseNavLinkSection,
        children: classes.hamburgerNavLinkChildren,
        chevron: classes.hamburgerNavLinkChevron,
      }}
      onClick={onNavLinkClick({ label, path, menuName: label }, analytics as WebAnalytics)}
      label={
        badge ? (
          <>
            {label}
            <Badge
              ml="xxs"
              size="md"
              variant="filled"
              color="yellow.5"
              styles={{
                label: {
                  color: 'var(--mantine-color-grape-8)',
                  fontSize: 'var(--mantine-font-size-xs)',
                },
              }}
            >
              {badge}
            </Badge>
          </>
        ) : (
          label
        )
      }
    >
      {children}
    </NavLink>
  );
};

export function Hamburger() {
  const [opened, { open, close }] = useDisclosure(false);
  const { width } = useViewportSize();
  const { brokerName } = useContext(NavContext);
  const { analytics } = useContext(LDContext);
  const { gpNeedsBasedNavBarExperiment, gpPropertyHubBeta, gpHeaderBuyersAgentNew } = useFlags();
  const NAV_LIST_TO_USE = gpNeedsBasedNavBarExperiment ? NAV_LIST_EXPERIMENT : NAV_LIST;
  let formattedBrokerName = brokerName;
  if (brokerName.length > 10) {
    const first10 = formattedBrokerName.substring(0, 10);
    formattedBrokerName = `${first10}...`;
  }
  const shouldFullWidth = width < 576;
  return (
    <>
      <Drawer.Root
        opened={opened}
        onClose={close}
        position="right"
        closeOnClickOutside
        closeOnEscape
        lockScroll={false}
        size={shouldFullWidth ? '100%' : 375}
      >
        <Drawer.Overlay bg="transparent" />
        <Drawer.Content bg="grape.8" bd="none">
          <Drawer.Header bg="none" h={64} px="sm" pos="relative">
            <Drawer.Title c="white" className={classes.drawerTitle} fz="xl">
              Menu
            </Drawer.Title>
            <Drawer.CloseButton size="lg" c="white" className={classes.closeButton} />
          </Drawer.Header>
          <Drawer.Body px={0}>
            <Divider c="white" mt="xs" mx="sm" />
            <Stack gap="xs" p="sm">
              <LinkButtonWithTracking
                fullWidth
                variant="highlight"
                href="/book-appointment"
                label="Apply now"
                category={EventCategory.NAVBAR}
                fz="md"
                px="sm"
                styles={{
                  label: { margin: 0, textTransform: 'none' },
                }}
              />
              <LinkButtonWithTracking
                fullWidth
                variant="outline_inverse"
                href={embedMarketingTrackingParams('/book-appointment')}
                px="sm"
                tt="none"
                fz="md"
                category={EventCategory.NAVBAR}
                label={`Talk to ${formattedBrokerName}`}
                styles={{
                  label: { margin: 0, textTransform: 'none' },
                }}
              />
            </Stack>

            {NAV_LIST_TO_USE.map((i) => {
              if (i.item.label === MENU_ITEMS.PROPERTY.label && gpPropertyHubBeta) {
                i.item.badge = 'BETA';
              }
              if (i.item.label === MENU_ITEMS.BUYERS_AGENTS.label && gpHeaderBuyersAgentNew) {
                i.item.badge = 'NEW';
              }
              if (i.children?.length > 0)
                return (
                  <MenuTarget key={i.item.label} label={i.item.label}>
                    {i.children.map((ii) => (
                      <MenuItem
                        key={ii.label}
                        label={ii.label}
                        path={ii.link}
                        menuName={i.item.label}
                      />
                    ))}
                    <Divider color="white" my="xs" mx="sm" />
                  </MenuTarget>
                );
              else
                return (
                  <MenuTarget
                    key={i.item.label}
                    label={i.item.label}
                    badge={i.item.badge}
                    path={i.item.link}
                  />
                );
            })}
          </Drawer.Body>
        </Drawer.Content>
      </Drawer.Root>

      <Button
        variant="transparent"
        onClick={onNavLinkClick(
          { label: 'Hamburger menu', menuName: 'Hamburger menu' },
          analytics as WebAnalytics,
          open,
          true
        )}
        styles={{ label: { margin: 0 } }}
        px={0}
      >
        <Menu color="white" size={24} />
      </Button>
    </>
  );
}
