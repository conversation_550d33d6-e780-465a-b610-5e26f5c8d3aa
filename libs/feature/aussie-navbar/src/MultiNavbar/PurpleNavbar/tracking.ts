import WebAnalytics, { EventCategory } from '@lendi/analytics-web';

type ClickFn = (event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>) => void;
type NavLinkClick = (
  { label, path, menuName }: { label: string; menuName?: string; path?: string },
  analyticsInstance?: WebAnalytics,
  handleClick?: ClickFn,
  sync?: boolean
) => ClickFn;
export const onNavLinkClick: NavLinkClick =
  ({ label, path, menuName }, analyticsInstance, handleClick?, sync?) =>
  (event) => {
    if (sync) {
      analyticsInstance?.trackEvent({
        event_name: label === menuName ? 'Menu Clicked' : 'Menu Element Clicked',
        category: EventCategory.NAVBAR,
        menuName: menuName ?? label,
        text: label,
        position: 'CMS',
      });
      handleClick?.(event);
      return;
    }
    event.preventDefault();
    Promise.race([
      analyticsInstance?.trackEvent({
        event_name: label === menuName ? 'Menu Clicked' : 'Menu Element Clicked',
        category: EventCategory.NAVBAR,
        menuName: menuName ?? label,
        text: label,
        position: 'CMS',
      }),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).finally(() => {
      handleClick?.(event);
      path && window.location.assign(path);
    });
  };

type MenuItemClick = (
  { label, path }: { label: string; path?: string },
  analyticsInstance?: WebAnalytics,
  handleClick?: ClickFn
) => ClickFn;
export const onMenuItemClick: MenuItemClick =
  ({ label, path }, analyticsInstance, handleClick?) =>
  (event) => {
    event.preventDefault();
    Promise.race([
      analyticsInstance?.trackEvent({
        event_name: 'Link Clicked',
        category: EventCategory.NAVBAR,
        linkType: 'LinkToPage',
        text: label,
        position: 'CMS',
      }),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).finally(() => {
      handleClick?.(event);
      path && window.location.assign(path);
    });
  };
