'use client';

import { Group } from '@mantine/core';

import { NavList } from '../constants';
import { NavDropdown } from '../Dropdown';

export default function NavLinks({ menu = [] }: { menu: NavList[] }) {
  return (
    <Group flex={1} gap="sm" wrap="nowrap">
      {menu.map((menuItem) => {
        return (
          <NavDropdown key={menuItem.item.label} target={menuItem.item} list={menuItem.children} />
        );
      })}
    </Group>
  );
}
