import { PropsWith<PERSON>hildren, useContext } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, NavLink, Stack, Title } from '@mantine/core';
import { useDisclosure, useViewportSize } from '@mantine/hooks';
import clsx from 'clsx';
import WebAnalytics, { EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { LinkButtonWithTracking } from '@gp/ui/components';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import { NavContext } from '../../../context';
import { ACTION_LIST, MENU_ITEMS, MY_ACCOUNT_LIST } from '../constants';
import { onNavLinkClick } from '../tracking';

import baseClasses from '../style.module.css';
import classes from './style.module.css';

const DEFAULT_DASHBOARD_URL = '/v2/dashboard/';

const MenuItem = ({
  label,
  path,
  menuName,
}: {
  label: string;
  menuName: string;
  path?: string;
}) => {
  const { analytics } = useContext(LDContext);
  return (
    <NavLink
      py={0}
      px={0}
      noWrap
      label={label}
      href={path}
      className={clsx(baseClasses.baseNavLinkItem, classes.hamburgeNavLinkItem)}
      onClick={onNavLinkClick({ label, menuName, path }, analytics as WebAnalytics)}
    />
  );
};

const MenuTarget = ({
  label,
  children,
  path,
  onClick,
}: PropsWithChildren<{ label: string; path?: string; onClick?: () => void }>) => {
  const { analytics } = useContext(LDContext);
  return (
    <NavLink
      noWrap
      label={label}
      h={48}
      styles={{ label: { fontSize: 'var(--mantine-font-size-md)' } }}
      classNames={{
        root: baseClasses.baseNavLink,
        section: baseClasses.baseNavLinkSection,
        children: classes.hamburgerNavLinkChildren,
        chevron: classes.hamburgerNavLinkChevron,
      }}
      onClick={onNavLinkClick({ label, path, menuName: label }, analytics as WebAnalytics, onClick)}
    >
      {children}
    </NavLink>
  );
};

function UnauthedMenu() {
  return (
    <Drawer.Content bg="grape.8" bd="none">
      <Drawer.Header bg="none" h={64} px="sm" pos="relative">
        <Drawer.Title c="white" className={classes.drawerTitle} fz="xl">
          Login
        </Drawer.Title>
        <Drawer.CloseButton size="lg" c="white" className={classes.closeButton} />
      </Drawer.Header>
      <Drawer.Body px={0}>
        <Stack gap={0} p="sm" pb="md" bg="grape.9">
          <Title order={2} c="white" fz="lg">
            Online banking login
          </Title>
          {ACTION_LIST.map((i) => (
            <MenuItem label={i.label} path={i.link} key={i.label} menuName="Login" />
          ))}
        </Stack>
        <Stack
          px="sm"
          pb="sm"
          pt="xs"
          align="stretch"
          justify="center"
          gap="xs"
          className={classes.menuCTA}
        >
          <Title c="white" size="lg" lh="h6" my="xs">
            Loan Application Login
          </Title>
          <LinkButtonWithTracking
            variant="outline_inverse"
            label="Manage application"
            fz="md"
            href={`/sign-in/${generateReturnURL([DEFAULT_DASHBOARD_URL])}`}
            styles={{ label: { textTransform: 'none' } }}
          />
        </Stack>
      </Drawer.Body>
    </Drawer.Content>
  );
}

function AuthedMenu() {
  const { brokerName, application } = useContext(NavContext);
  const { logout } = useSession();
  return (
    <Drawer.Content bg="grape.8" bd="none">
      <Drawer.Header bg="none" h={64} px="sm" pos="relative">
        <Drawer.Title c="white" className={classes.drawerTitle} fz="xl">
          My account
        </Drawer.Title>
        <Drawer.CloseButton size="lg" c="white" className={classes.closeButton} />
      </Drawer.Header>
      <Drawer.Body px={0}>
        <Title order={2} c="white" fz="lg" p="sm" pb={0}>
          Manage your loan applications
        </Title>
        <Divider mx="sm" my="md" style={{ borderColor: 'white', borderWidth: '2px' }} />
        <Stack gap="xs" px="sm" pb="xs">
          <LinkButtonWithTracking
            fullWidth
            label={application ? 'Continue application' : 'Apply now'}
            variant="highlight"
            href="/home-loans/basics"
            category={EventCategory.NAVBAR}
            px="sm"
            fz="md"
            styles={{
              label: { margin: 0, textTransform: 'none' },
            }}
          />
          <LinkButtonWithTracking
            fullWidth
            variant="outline_inverse"
            href={embedMarketingTrackingParams('/book-appointment')}
            category={EventCategory.NAVBAR}
            px="sm"
            fz="md"
            label={`Talk to ${brokerName}`}
            styles={{
              label: { margin: 0, textTransform: 'none' },
            }}
          />
        </Stack>
        {MY_ACCOUNT_LIST.map((i) => (
          <MenuTarget label={i.label} key={i.label} path={i.link} />
        ))}
        <MenuTarget label={MENU_ITEMS.SIGN_OUT.label} onClick={logout} />
      </Drawer.Body>
    </Drawer.Content>
  );
}

export function ActionHamburger() {
  const [opened, { open, close }] = useDisclosure(false);
  const { status } = useSession();
  const authenticated = status === Status.Authenticated;
  const { width } = useViewportSize();
  const { brokerName } = useContext(NavContext);
  let formattedBrokerName = brokerName;
  if (brokerName.length > 10) {
    const first10 = formattedBrokerName.substring(0, 10);
    formattedBrokerName = `${first10}...`;
  }
  const shouldFullWidth = width < 576;
  const { analytics } = useContext(LDContext);
  return (
    <>
      <Drawer.Root
        opened={opened}
        onClose={close}
        position="right"
        closeOnClickOutside
        closeOnEscape
        lockScroll={false}
        size={shouldFullWidth ? '100%' : 375}
      >
        <Drawer.Overlay bg="transparent" />
        {authenticated ? <AuthedMenu /> : <UnauthedMenu />}
      </Drawer.Root>

      <Button
        variant="highlight"
        onClick={onNavLinkClick(
          { label: 'Hamburger menu', menuName: 'Hamburger menu' },
          analytics as WebAnalytics,
          open,
          true
        )}
        styles={{
          label: { margin: 0, textTransform: 'none' },
        }}
        size="sm"
        px="sm"
        fz="sm"
      >
        {authenticated ? 'My account' : 'Login'}
      </Button>
    </>
  );
}
