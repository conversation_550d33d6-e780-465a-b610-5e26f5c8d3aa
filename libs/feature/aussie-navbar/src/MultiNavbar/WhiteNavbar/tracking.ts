import { EventCategory } from '@lendi/analytics-web';

type ClickFn = (event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>) => void;

// Update the type to accept the analytics object from LDContext
type AnalyticsInstance = {
  trackEvent: (event: any) => any;
  trackPage: (category: EventCategory, name: string, properties?: Record<string, unknown>) => any;
  identify: (
    userId: string,
    flags: Record<string, unknown>,
    traits?: Record<string, unknown>
  ) => any;
};

type NavLinkClick = (
  { label, path, menuName }: { label: string; menuName?: string; path?: string },
  analyticsInstance?: AnalyticsInstance,
  handleClick?: ClickFn,
  sync?: boolean,
  openInNewTab?: boolean
) => ClickFn;

export const onNavLinkClick: NavLinkClick =
  ({ label, path, menuName }, analyticsInstance, handleClick?, sync?, openInNewTab?) =>
  (event) => {
    const eventPayload = {
      event_name: 'Link Clicked',
      category: EventCategory.NAVBAR,
      menuName: menuName ?? label,
      text: label,
      position: 'CMS',
    };
    if (sync) {
      analyticsInstance?.trackEvent(eventPayload);
      handleClick?.(event);
      return;
    }

    if (openInNewTab) {
      // For external links that should open in new tabs, don't prevent default
      analyticsInstance?.trackEvent(eventPayload);
      handleClick?.(event);
      return;
    }

    event.preventDefault();
    Promise.race([
      analyticsInstance?.trackEvent(eventPayload),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).finally(() => {
      handleClick?.(event);
      path && window.location.assign(path);
    });
  };

type MenuItemClick = (
  { label, path }: { label: string; path?: string },
  analyticsInstance?: AnalyticsInstance,
  handleClick?: ClickFn
) => ClickFn;

export const onMenuItemClick: MenuItemClick =
  ({ label, path }, analyticsInstance, handleClick?) =>
  (event) => {
    const eventPayload = {
      event_name: 'Menu Element Clicked',
      category: EventCategory.NAVBAR,
      linkType: 'LinkToPage',
      text: label,
      position: 'CMS',
    };
    event.preventDefault();
    Promise.race([
      analyticsInstance?.trackEvent(eventPayload),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).finally(() => {
      handleClick?.(event);
      path && window.location.assign(path);
    });
  };
