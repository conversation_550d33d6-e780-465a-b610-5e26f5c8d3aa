.navLink {
  font-family: 'tt-commons-pro', sans-serif;
  font-weight: 700;
  line-height: 20px;
  color: var(--mantine-color-grape-8);
  &:active {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
  &:hover {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
  span {
    font-size: var(--mantine-font-size-md);
  }
  &[aria-expanded='true'] {
    background-color: var(--mantine-color-grape-0);
  }
}

.navDropdown {
  background-color: white;
  padding: var(--mantine-spacing-xxs) 0;
  border: none;
  border-radius: 6px;
  min-width: 285px;
  box-shadow: 0px 16px 24px -8px rgba(21, 20, 30, 0.32);
  box-shadow: 0px 0px 0px 1px rgba(21, 20, 30, 0.08);
}

.navDropdownItem {
  font-family: '<PERSON>';
  font-weight: 500;
  line-height: 24px;
  color: var(--mantine-color-gray-9);
  background-color: unset;
  padding-inline: var(--mantine-spacing-sm);
  padding-block: var(--mantine-spacing-xs);
  font-size: var(--mantine-font-size-md);

  &:hover {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
  &:active {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
}
.navDropdownItemLabel {
  font-family: 'Barlow';
  font-weight: 500;
  line-height: 20px;
  font-size: var(--mantine-font-size-md);
}

.baseNavLink[aria-expanded='true'] {
  * > .baseNavLinkSection {
    transform: rotate(180deg);
  }
}

.baseNavLinkSection:where([data-rotate]) {
  transform: rotate(180deg);
}
