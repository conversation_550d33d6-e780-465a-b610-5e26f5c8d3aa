import { useContext } from 'react';
import { Button, Group, HoverCard, NavLink } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';

import { LDContext } from '@gp/shared/launchdarkly';
import { ChevronDown } from '@gp/ui/icons';

import { FIND_BUY_OWN_MENU_ITEMS, LARGE_BREAKPOINT } from '../constants';
import { onMenuItemClick, onNavLinkClick } from '../tracking';

import classes from './style.module.css';

export const NavLinks = () => {
  const { width } = useViewportSize();
  const { analytics } = useContext(LDContext);

  return (
    <Group
      flex={1}
      gap="xxxs"
      wrap="nowrap"
      h="100%"
      justify="center"
      display={width < LARGE_BREAKPOINT ? 'none' : 'flex'}
    >
      {FIND_BUY_OWN_MENU_ITEMS.map((item) => {
        return (
          <HoverCard
            position="bottom-start"
            key={item.label}
            floatingStrategy="fixed"
            closeDelay={100}
          >
            <HoverCard.Target>
              <Button
                variant="outline"
                size="sm"
                className={classes.navLink}
                onClick={(e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>) => {
                  if (item.label === 'Find a broker') {
                    const clickHandler = onNavLinkClick(
                      { label: item.label, path: '/mortgage-broker/', menuName: item.label },
                      analytics
                    );
                    clickHandler(e);
                  } else {
                    const clickHandler = onMenuItemClick({ label: item.label }, analytics);
                    clickHandler(e);
                  }
                }}
                classNames={{ root: classes.baseNavLink, section: classes.baseNavLinkSection }}
                component={item.label === 'Find a broker' ? 'a' : 'button'}
                href={item.label === 'Find a broker' ? '/mortgage-broker/' : undefined}
                rightSection={
                  item.label === 'Find a broker' ? undefined : <ChevronDown size={21} />
                }
              >
                {item.label}
              </Button>
            </HoverCard.Target>
            {'links' in item && (
              <HoverCard.Dropdown className={classes.navDropdown}>
                {item.links.map(({ label, link }) => (
                  <NavLink
                    key={label}
                    label={label}
                    href={link}
                    className={`${classes.navDropdownItem} ${classes.baseNavLink}`}
                    classNames={{
                      label: classes.navDropdownItemLabel,
                    }}
                    onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                      const clickHandler = onNavLinkClick(
                        { label: label, path: link, menuName: item.label },
                        analytics
                      );
                      clickHandler(e);
                    }}
                  />
                ))}
              </HoverCard.Dropdown>
            )}
          </HoverCard>
        );
      })}
    </Group>
  );
};
