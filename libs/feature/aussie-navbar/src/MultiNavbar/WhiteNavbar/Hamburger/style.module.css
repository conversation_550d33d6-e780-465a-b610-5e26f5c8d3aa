.navLink {
  font-family: 'tt-commons-pro', sans-serif;
  font-size: var(--mantine-font-size-sm);
  font-weight: 700;
  line-height: 20px;
  color: var(--mantine-color-gray-9);
  height: rem(40px);

  span {
    margin-inline: 0;
  }

  &:hover {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
  &[data-expanded='true'] {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }
}

.navLinkSection[data-rotate] {
  .navLinkChevron {
    transform: rotate(90deg);
  }
}

.navLinkLabel {
  font-size: var(--mantine-font-size-sm);
  font-weight: 700;
}

.navLinkChildren {
  padding: 0;
}

.navLinkChevron {
  transform: rotate(0deg);
}

.navLinkDropdownItem {
  font-family: 'Barlow';
  font-weight: 500;
  line-height: 20px;
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-gray-9);
  background-color: unset;
  height: rem(44px);

  padding-left: var(--mantine-spacing-lg);

  &:hover {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
  }

  span {
    margin-inline: 0;
  }
}

.badge {
  margin-left: var(--mantine-spacing-xxs);

  span {
    color: var(--mantine-color-grape-8);
    font-size: var(--mantine-font-size-xs);
  }
}

.arrow {
  path {
    fill: none;
  }
}

.stickyFooter {
  flex-shrink: 0;
  background-color: var(--mantine-color-white);
  padding-top: var(--mantine-spacing-lg);
  padding-bottom: var(--mantine-spacing-lg);
  border-top: 1px solid var(--mantine-color-gray-1);
}
