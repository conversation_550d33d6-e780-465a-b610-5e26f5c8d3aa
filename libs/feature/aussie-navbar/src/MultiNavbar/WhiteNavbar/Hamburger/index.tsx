import { useContext, useState } from 'react';
import { Fragment } from 'react/jsx-runtime';
import { <PERSON><PERSON>, <PERSON>, Burger, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, NavLink, Stack } from '@mantine/core';
import { useDisclosure, useViewportSize } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { ArrowTopRight } from '@gp/ui/icons';
import { generateReturnURL } from '@gp/util/url';

import { NavContext } from '../../../context';
import {
  FIND_BUY_OWN_MENU_ITEMS,
  LARGE_BREAKPOINT,
  LOGIN_MENU_ITEMS,
  MY_ACCOUNT_MENU_ITEMS,
  SMALL_BREAKPOINT,
} from '../constants';
import { onMenuItemClick, onNavLinkClick } from '../tracking';

import classes from './style.module.css';

export const HamburgerMenu = () => {
  const [opened, { close, toggle }] = useDisclosure(false);
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const { width } = useViewportSize();
  const { brokerName } = useContext(NavContext);
  const { status, logout } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { analytics } = useContext(LDContext);
  const myAccountMenuItems = isAuthenticated ? MY_ACCOUNT_MENU_ITEMS : LOGIN_MENU_ITEMS;

  const handleMenuClick = (menuLabel: string) => {
    if (openMenu === menuLabel) {
      setOpenMenu(null);
    } else {
      setOpenMenu(menuLabel);
    }
  };

  if (!width || width >= LARGE_BREAKPOINT) {
    return null;
  }

  return (
    <>
      <Drawer.Root
        opened={opened}
        onClose={close}
        position="right"
        closeOnClickOutside
        closeOnEscape
        size={width < SMALL_BREAKPOINT ? '100%' : SMALL_BREAKPOINT}
        zIndex={99}
      >
        <Drawer.Overlay bg="transparent" />
        <Drawer.Content bg="white" bd="none">
          <Drawer.Header bg="none" h={76} px="sm" pos="relative"></Drawer.Header>
          <Drawer.Body
            px={0}
            h={`calc(100% - ${76}px)`}
            style={{ display: 'flex', flexDirection: 'column', paddingBottom: 0 }}
          >
            <Box style={{ flex: 1, overflow: 'auto' }}>
              <Stack gap={0}>
                {FIND_BUY_OWN_MENU_ITEMS.map((item, index) => {
                  if ('links' in item) {
                    return (
                      <Fragment key={item.label}>
                        {index > 0 && <Divider my="xxs" />}
                        <NavLink
                          noWrap
                          component="button"
                          opened={openMenu === item.label}
                          classNames={{
                            root: classes.navLink,
                            label: classes.navLinkLabel,
                            children: classes.navLinkChildren,
                            chevron: classes.navLinkChevron,
                            section: classes.navLinkSection,
                          }}
                          label={item.label}
                          onClick={(e) => {
                            handleMenuClick(item.label);
                            const clickHandler = onMenuItemClick({ label: item.label }, analytics);
                            clickHandler(e);
                          }}
                        >
                          {'links' in item &&
                            item.links.map(({ label, link }) => {
                              return (
                                <NavLink
                                  key={label}
                                  noWrap
                                  component="a"
                                  className={classes.navLinkDropdownItem}
                                  label={label}
                                  href={link}
                                  onClick={(e) => {
                                    const clickHandler = onNavLinkClick(
                                      {
                                        label: label,
                                        path: link,
                                        menuName: item.label,
                                      },
                                      analytics
                                    );
                                    clickHandler(e);
                                  }}
                                />
                              );
                            })}
                        </NavLink>
                      </Fragment>
                    );
                  }

                  return (
                    <Fragment key={item.label}>
                      <Divider my="xxs" />
                      <NavLink
                        noWrap
                        component="a"
                        classNames={{
                          root: classes.navLink,
                          label: classes.navLinkLabel,
                          children: classes.navLinkChildren,
                        }}
                        label={
                          <>
                            {item.label}
                            {item.badge && (
                              <Badge
                                className={classes.badge}
                                size="md"
                                variant="filled"
                                color="yellow.5"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        }
                        href={item.link}
                        onClick={(e) => {
                          const clickHandler = onNavLinkClick(
                            {
                              label: item.label,
                              path: item.link,
                              menuName: item.label,
                            },
                            analytics
                          );
                          clickHandler(e);
                        }}
                      />
                    </Fragment>
                  );
                })}
                <Divider my="xxs" />
                <NavLink
                  noWrap
                  component="a"
                  classNames={{
                    root: classes.navLink,
                    label: classes.navLinkLabel,
                    children: classes.navLinkChildren,
                    chevron: classes.navLinkChevron,
                  }}
                  label={`Talk to ${brokerName ? brokerName : 'a broker'}`}
                  href="/book-appointment/"
                  onClick={(e) => {
                    const clickHandler = onNavLinkClick(
                      {
                        label: `Talk to ${brokerName ? brokerName : 'a broker'}`,
                        path: '/book-appointment/',
                        menuName: `Talk to ${brokerName ? brokerName : 'a broker'}`,
                      },
                      analytics
                    );
                    clickHandler(e);
                  }}
                ></NavLink>
                <Divider my="xxs" />
                <NavLink
                  noWrap
                  component="button"
                  opened={openMenu === (isAuthenticated ? 'My account' : 'Login')}
                  classNames={{
                    root: classes.navLink,
                    label: classes.navLinkLabel,
                    children: classes.navLinkChildren,
                    chevron: classes.navLinkChevron,
                    section: classes.navLinkSection,
                  }}
                  label={isAuthenticated ? 'My account' : 'Login'}
                  onClick={(e) => {
                    handleMenuClick(isAuthenticated ? 'My account' : 'Login');
                    const clickHandler = onMenuItemClick(
                      { label: isAuthenticated ? 'My account' : 'Login' },
                      analytics
                    );
                    clickHandler(e);
                  }}
                >
                  {myAccountMenuItems.map((item) => {
                    return (
                      <NavLink
                        key={item.label}
                        noWrap
                        component="a"
                        className={classes.navLinkDropdownItem}
                        target={!isAuthenticated ? '_blank' : undefined}
                        rel={!isAuthenticated ? 'noopener noreferrer' : undefined}
                        rightSection={
                          isAuthenticated ? undefined : (
                            <ArrowTopRight
                              fill="black"
                              width={24}
                              height={24}
                              className={classes.arrow}
                            />
                          )
                        }
                        label={item.label}
                        href={item.link}
                        onClick={(e) => {
                          if (isAuthenticated) {
                            // For authenticated users (MY_ACCOUNT_MENU_ITEMS), use onNavLinkClick
                            const clickHandler = onNavLinkClick(
                              { label: item.label, path: item.link, menuName: item.label },
                              analytics,
                              undefined,
                              undefined,
                              false // do not open in new tab
                            );
                            clickHandler(e);
                          } else {
                            // For unauthenticated users (LOGIN_MENU_ITEMS), use onNavLinkClick with openInNewTab
                            const clickHandler = onNavLinkClick(
                              { label: item.label, path: item.link, menuName: item.label },
                              analytics,
                              undefined,
                              undefined,
                              true // openInNewTab
                            );
                            clickHandler(e);
                          }
                        }}
                      />
                    );
                  })}
                  {isAuthenticated && (
                    <NavLink
                      noWrap
                      component="button"
                      onClick={logout}
                      className={classes.navLinkDropdownItem}
                      label="Sign out"
                    ></NavLink>
                  )}
                </NavLink>
                <Divider my="xxs" />
              </Stack>
            </Box>
            {!isAuthenticated && (
              <Box px="lg" py="xxs" className={classes.stickyFooter}>
                <Button
                  variant="secondary"
                  fz="md"
                  component="a"
                  href={`/sign-in/${generateReturnURL(['/v2/dashboard/'])}`}
                  fullWidth
                  onClick={(e) => {
                    const clickHandler = onNavLinkClick(
                      {
                        label: 'Loan application login',
                        path: `/sign-in/${generateReturnURL(['/v2/dashboard/'])}`,
                        menuName: 'Login',
                      },
                      analytics
                    );
                    clickHandler(e);
                  }}
                >
                  Loan application login
                </Button>
              </Box>
            )}
            {isAuthenticated && (
              <Box px="lg" py="xxs" className={classes.stickyFooter}>
                <Button
                  variant="secondary"
                  component="a"
                  fullWidth
                  href="/v2/dashboard/"
                  fz="md"
                  onClick={(e) => {
                    const clickHandler = onNavLinkClick(
                      {
                        label: 'Continue application',
                        path: '/v2/dashboard/',
                        menuName: 'My account',
                      },
                      analytics
                    );
                    clickHandler(e);
                  }}
                >
                  Continue application
                </Button>
              </Box>
            )}
          </Drawer.Body>
        </Drawer.Content>
      </Drawer.Root>
      <Burger opened={opened} onClick={toggle} size="sm" />
    </>
  );
};
