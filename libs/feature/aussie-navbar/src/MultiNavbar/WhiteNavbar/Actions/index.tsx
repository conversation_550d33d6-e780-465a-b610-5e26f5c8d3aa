import { useContext } from 'react';
import { Button, Group } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { Phone } from '@gp/ui/icons';

import { NavContext } from '../../../context';
import { LARGE_BREAKPOINT, SMALL_BREAKPOINT } from '../constants';
import { HamburgerMenu } from '../Hamburger';
import { MyAccount } from '../MyAccount';
import { onNavLinkClick } from '../tracking';

import classes from './style.module.css';

export const Actions = () => {
  const { width } = useViewportSize();
  const { brokerName } = useContext(NavContext);
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { analytics } = useContext(LDContext);

  return (
    <Group gap="sm" h="100%">
      <Button
        variant={width > SMALL_BREAKPOINT ? 'highlight' : 'secondary'}
        size="sm"
        component="a"
        href="/book-appointment/"
        px={8}
        className={classes.hoverTarget}
        display={!isAuthenticated && width < LARGE_BREAKPOINT ? 'none' : 'block'}
        leftSection={width > SMALL_BREAKPOINT ? <Phone size={20} /> : undefined}
        onClick={(e) => {
          const clickHandler = onNavLinkClick(
            {
              label: `Talk to ${brokerName ? brokerName : 'a broker'}`,
              path: '/book-appointment/',
              menuName: `Talk to ${brokerName ? brokerName : 'a broker'}`,
            },
            analytics
          );
          clickHandler(e);
        }}
      >
        Talk to {brokerName ? brokerName : 'a broker'}
      </Button>
      <Button
        variant="primary"
        size={width < LARGE_BREAKPOINT ? 'md' : 'sm'}
        component="a"
        href="/home-loans/basics/"
        className={classes.applyNow}
        px={8}
        display={isAuthenticated ? 'none' : 'block'}
        onClick={(e) => {
          const clickHandler = onNavLinkClick(
            { label: 'Apply now', path: '/home-loans/basics/', menuName: 'Apply now' },
            analytics
          );
          clickHandler(e);
        }}
      >
        Apply now
      </Button>
      <MyAccount />
      <HamburgerMenu />
    </Group>
  );
};
