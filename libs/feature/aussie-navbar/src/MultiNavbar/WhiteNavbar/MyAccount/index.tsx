import { useContext } from 'react';
import { But<PERSON>, Di<PERSON>r, HoverCard, NavLink, Stack } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { ArrowTopRight } from '@gp/ui/icons';
import { generateReturnURL } from '@gp/util/url';

import { LARGE_BREAKPOINT, LOGIN_MENU_ITEMS, MY_ACCOUNT_MENU_ITEMS } from '../constants';
import { onMenuItemClick, onNavLinkClick } from '../tracking';

import classes from './style.module.css';

export const MyAccount = () => {
  const { width } = useViewportSize();

  const { status, logout } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { analytics } = useContext(LDContext);

  const menuItems = isAuthenticated ? MY_ACCOUNT_MENU_ITEMS : LOGIN_MENU_ITEMS;
  if (width < LARGE_BREAKPOINT) {
    return null;
  }

  return (
    <HoverCard width={300} position="bottom-end" floatingStrategy="fixed" closeDelay={100}>
      <HoverCard.Target>
        <Button
          variant="secondary"
          px={8}
          size="sm"
          display={width > 576 ? 'flex' : 'none'}
          className={classes.hoverTarget}
          onClick={(e) => {
            const clickHandler = onMenuItemClick(
              { label: isAuthenticated ? 'My account' : 'Login' },
              analytics
            );
            clickHandler(e);
          }}
        >
          {isAuthenticated ? 'My account' : 'Login'}
        </Button>
      </HoverCard.Target>
      <HoverCard.Dropdown className={classes.navDropdown}>
        {isAuthenticated ? (
          <p className={classes.menuLabel}>Manage your loan applications</p>
        ) : (
          <p className={classes.menuLabel}>Online banking login</p>
        )}
        {isAuthenticated && (
          <Stack gap="xs" px="sm" my="xs">
            <Button
              variant="secondary"
              size="md"
              component="a"
              href="/v2/dashboard"
              onClick={(e) => {
                const clickHandler = onNavLinkClick(
                  {
                    label: 'Continue my application',
                    path: '/v2/dashboard',
                    menuName: 'My account',
                  },
                  analytics
                );
                clickHandler(e);
              }}
            >
              Continue my application
            </Button>
          </Stack>
        )}
        {menuItems.map((item) => (
          <NavLink
            key={item.label}
            label={item.label}
            href={item.link}
            className={classes.navDropdownItem}
            target={!isAuthenticated ? '_blank' : undefined}
            rel={!isAuthenticated ? 'noopener noreferrer' : undefined}
            rightSection={
              isAuthenticated ? undefined : (
                <ArrowTopRight width={24} height={24} className={classes.arrow} />
              )
            }
            onClick={(e) => {
              if (isAuthenticated) {
                // For authenticated users (MY_ACCOUNT_MENU_ITEMS), use onNavLinkClick
                const clickHandler = onNavLinkClick(
                  { label: item.label, path: item.link, menuName: 'My account' },
                  analytics,
                  undefined,
                  undefined,
                  false // do not open in new tab
                );
                clickHandler(e);
              } else {
                // For unauthenticated users (LOGIN_MENU_ITEMS), use onNavLinkClick with openInNewTab
                const clickHandler = onNavLinkClick(
                  { label: item.label, path: item.link, menuName: 'Login' },
                  analytics,
                  undefined,
                  undefined,
                  true // openInNewTab
                );
                clickHandler(e);
              }
            }}
          />
        ))}
        {isAuthenticated && (
          <>
            <Divider my="xxs" />
            <NavLink
              component="button"
              className={classes.navDropdownItem}
              onClick={logout}
              label="Sign out"
            />
          </>
        )}
        {!isAuthenticated && <Divider my="xxs" />}
        {!isAuthenticated && (
          <Stack align="stretch" justify="center" gap="xs" className={classes.menuCTA}>
            <Button
              variant="secondary"
              fz="md"
              component="a"
              href={`/sign-in/${generateReturnURL(['/v2/dashboard/'])}`}
              onClick={(e) => {
                const clickHandler = onNavLinkClick(
                  {
                    label: 'Loan application login',
                    path: `/sign-in/${generateReturnURL(['/v2/dashboard/'])}`,
                    menuName: 'Login',
                  },
                  analytics
                );
                clickHandler(e);
              }}
            >
              Loan application login
            </Button>
          </Stack>
        )}
      </HoverCard.Dropdown>
    </HoverCard>
  );
};
