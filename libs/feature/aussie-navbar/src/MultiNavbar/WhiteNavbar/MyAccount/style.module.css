.hoverTarget {
  font-family: 'tt-commons-pro', sans-serif;
  font-weight: 700;
  line-height: 20px;
  color: var(--mantine-color-grape-8);
  &:active {
    background-color: var(--mantine-color-grape-0);
  }
  &:hover {
    background-color: var(--mantine-color-grape-0);
  }
  span {
    font-size: var(--mantine-font-size-md);
  }
  &[aria-expanded='true'] {
    background-color: var(--mantine-color-grape-0);
  }
}

.applyNow {
  span {
    font-size: var(--mantine-font-size-md);
  }
}

.arrow {
  path {
    fill: none;
  }
}

.navDropdown {
  padding: 0;
  background-color: white;
  padding-block: var(--mantine-spacing-xs);
  border: none;
  border-radius: 6px;
  min-width: 285px;
  box-shadow: var(--mantine-shadow-md);
}

.navDropdownItem {
  font-family: 'Barlow';
  font-weight: 500;
  line-height: 24px;
  color: var(--mantine-color-gray-9);
  background-color: unset;
  padding-inline: var(--mantine-spacing-sm);
  padding-block: var(--mantine-spacing-xs);
  span {
    font-size: var(--mantine-font-size-md);
    color: var(--mantine-color-gray-9);
  }

  &:hover {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
    span {
      color: var(--mantine-color-grape-8);
    }
  }
  &:active {
    background-color: var(--mantine-color-grape-0);
    color: var(--mantine-color-grape-8);
    span {
      color: var(--mantine-color-grape-8);
    }
  }
}

.menuLabel {
  font-family: 'Barlow', sans-serif;
  font-size: var(--mantine-font-size-xs);
  line-height: 20px;
  font-weight: 500;
  color: var(--mantine-color-gray-7);
  padding-inline: var(--mantine-spacing-sm);
  padding-block: var(--mantine-spacing-xxs);
  margin: 0;
}

.menuCTA {
  border-radius: 0 0 1rem 1rem;
  padding: var(--mantine-spacing-sm);
}
