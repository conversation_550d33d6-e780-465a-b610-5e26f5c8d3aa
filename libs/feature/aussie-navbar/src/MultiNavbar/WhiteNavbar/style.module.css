.header {
  position: sticky;
  top: 0;
  z-index: var(--mantine-z-index-app);
  background-color: white;
  padding: 0 1rem;
  border-bottom: 1px solid var(--mantine-color-gray-2);
}

.navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 68px;
  max-width: 1272px;
  margin: 0 auto;
  gap: var(--mantine-spacing-md);

  @mixin smaller-than $mantine-breakpoint-xs {
    height: 64px;
  }
  --blue: var(--mantine-color-grape-1);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: start;

  svg {
    width: 160px;
    height: 32px;
    @mixin smaller-than 1300px {
      width: 115px;
      height: 24px;
    }
  }
}
