'use client';
import { useContext, useEffect, useState } from 'react';
import { Anchor } from '@mantine/core';
import { useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';
import { Aussie } from '@gp/ui/icons';

import { NavContext, setNavBrokerName, setNavCustomerApplication } from '../../context';
import { Actions } from './Actions';
import { NavLinks } from './NavLinks';
import { onNavLinkClick } from './tracking';

import classes from './style.module.css';

export default function WhiteNavbar() {
  const { analytics } = useContext(LDContext);
  const { status, token, identity } = useSession();
  const [brokerName, setBrokerName] = useState('a broker');
  const [customerApplication, setCustomerApplication] = useState('');

  useEffect(() => {
    setNavBrokerName(setBrokerName, token);
    setNavCustomerApplication(identity?.id || '', setCustomerApplication, token);
  }, [status, token, identity]);

  return (
    <header className={classes.header}>
      <nav className={classes.navbar}>
        <NavContext.Provider value={{ brokerName, application: customerApplication }}>
          <Anchor
            className={classes.logo}
            href="/"
            onClick={onNavLinkClick({ label: 'Logo', menuName: 'Logo', path: '/' }, analytics)}
          >
            <Aussie />
          </Anchor>
          <NavLinks />
          <Actions />
        </NavContext.Provider>
      </nav>
    </header>
  );
}
