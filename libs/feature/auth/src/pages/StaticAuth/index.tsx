'use client';

import { useEffect, useState } from 'react';
import { Grid } from '@mantine/core';
import { AuthErrorCode } from '@lendi/auth';
import { AuthOrigin } from '@lendi/core-constants';
import { Brand } from '@lendi/lala-react';

import { Customer, customerSchema } from '@gp/data-access/customer';
import { PageTracking } from '@gp/ui/components';
import { AuthUtil } from '@gp/util/auth';
import { getEnv } from '@gp/util/session';

import { AuthHero, MobileNumber, NameAndEmail } from '../../components';
import { FunnelVariant } from '../../components/constants';
import Verification from '../../components/Verification';
import { doRedirect, sendLeadsData } from './utils';

export interface StaticAuthProps {
  variant: FunnelVariant;
}

export default function StaticAuth({ variant }: StaticAuthProps) {
  const [customer, setCustomer] = useState<Partial<Customer>>({});
  const [active, setActive] = useState(customerSchema.safeParse(customer).success ? 2 : 0);
  const [authError, setAuthError] = useState<AuthErrorCode | null>(null);
  const [auth, setAuth] = useState<AuthUtil | null>(null);

  const env = getEnv();

  // Initialize AuthUtil only after component mounts (client-side only)
  useEffect(() => {
    setAuth(new AuthUtil(Brand.Aussie, env));
  }, [env]);

  const handleFormSubmit = async (data: Partial<Customer>) => {
    setCustomer((current) => ({ ...current, ...data }));

    if (active === 0) {
      setActive((current) => current + 1);
    } else if (active === 1 && data.mobileNumber && data.firstName) {
      const success = await handleSendCode(data.mobileNumber, data.firstName);
      // Only navigate if sendCode succeeded
      if (success) {
        setActive((current) => current + 1);
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((data as any).verificationCode && active === 2) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      handleVerifyCode((data as any).verificationCode as string);
    }
  };

  const handleEditNumber = () => {
    setActive(1); // Go back to MobileNumber step
  };

  const handleSendCode = async (mobileNumber: string, firstName: string): Promise<boolean> => {
    if (auth) {
      try {
        setAuthError(null); // Clear previous errors
        await auth.sendCode(mobileNumber);
        const state = auth.getSessionState();
        if (!state?.identity?.id) {
          sendLeadsData({
            authOrigin: AuthOrigin.Passwordless,
            mobileNumber: mobileNumber,
            firstName: firstName,
          });
        }
        return true; // Success
      } catch (error: unknown) {
        console.error('Send code error:', error);
        if (
          error &&
          typeof error === 'object' &&
          'description' in error &&
          (error as { description: string }).description === 'OTP_LIMIT_EXCEEDED'
        ) {
          setAuthError(AuthErrorCode.TOO_MANY_ATTEMPTS);
        }
        return false; // Failed
      }
    }
    return false; // Auth util not initialized
  };

  const handleVerifyCode = async (code: string) => {
    if (!auth) return;

    try {
      const res = await auth.verifyCode({
        phone: customer.mobileNumber as string,
        firstName: customer.firstName,
        email: customer.email,
        code: code,
      });
      if (res.token && res.valid) {
        handleCleanUp();
        doRedirect();
      }
    } catch (err) {
      console.error('Verification code error', err);
      throw err; // Re-throw error so Verification component can catch it
    }
  };

  const handleCleanUp = () => {
    setCustomer({});
  };

  return (
    <PageTracking name="Static Auth" category="STATIC_AUTH" withMarketingCloudTracking>
      <Grid px={{ base: 'md', sm: 0 }} gutter={0}>
        <Grid.Col span={{ base: 0, sm: 7 }} visibleFrom="sm" p={0}>
          <AuthHero />
        </Grid.Col>
        <Grid.Col
          span={{ base: 12, sm: 5 }}
          pt={{ base: 'xxl', sm: '128px' }}
          px={{ base: 0, sm: 'xl' }}
        >
          {active === 0 && (
            <NameAndEmail variant={variant} data={customer} handleSubmit={handleFormSubmit} />
          )}
          {active === 1 && (
            <MobileNumber data={customer} handleSubmit={handleFormSubmit} authError={authError} />
          )}
          {active === 2 && (
            <Verification
              data={customer}
              handleSubmit={handleFormSubmit}
              onEditNumber={handleEditNumber}
              onResendCode={handleSendCode}
              onVerifyCode={handleVerifyCode}
              authError={authError}
            />
          )}
        </Grid.Col>
      </Grid>
    </PageTracking>
  );
}
