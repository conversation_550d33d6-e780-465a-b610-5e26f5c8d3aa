'use client';

import { useContext, useEffect, useRef, useState } from 'react';
import { Container, Group, NumberInput, Stack, Text, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { EventCategory } from '@lendi/analytics-web';
import { AuthErrorCode } from '@lendi/auth';

import { Customer } from '@gp/data-access/customer';
import { LDContext } from '@gp/shared/launchdarkly';
import { AnchorWithTracking, ButtonWithTracking, FormWithTracking } from '@gp/ui/components';
import { Lock } from '@gp/ui/icons';
import { verifyAustralianMobileNumber } from '@gp/util/intl';

import OtpAttemptsAlert from '../OtpAttemptsAlert';

interface ContactNumberProps {
  data: Partial<Customer>;
  handleSubmit: (data: Partial<Customer>) => void;
  authError?: AuthErrorCode | null;
}

const FORM_NAME = 'Static Auth - Mobile Number';

const validateData = (data: Partial<Customer>) => {
  return verifyAustralianMobileNumber(data.mobileNumber ?? '');
};

export default function MobileNumber({ data, handleSubmit, authError }: ContactNumberProps) {
  const { analytics } = useContext(LDContext);
  const [isFormValid, setIsFormValid] = useState(validateData(data));
  const hasTrackedView = useRef(false);

  // Track page view on mount
  useEffect(() => {
    if (!hasTrackedView.current) {
      analytics?.trackEvent({
        event_name: 'auth_step_2_viewed',
        step: 2,
      });
      hasTrackedView.current = true;
    }
  }, [analytics]);

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: data,
    onValuesChange: (formData) => {
      setIsFormValid(validateData(formData));
    },
  });

  // Track submit button click
  const handleFormSubmit = (formData: Partial<Customer>) => {
    analytics?.trackEvent({
      event_name: 'auth_step_2_submit_clicked',
      step: 2,
      is_form_valid: isFormValid,
    });

    // Track validation errors on submit if form is invalid
    if (!isFormValid) {
      const mobileNumberValid = verifyAustralianMobileNumber(formData.mobileNumber ?? '');

      if (!mobileNumberValid) {
        analytics?.trackEvent({
          event_name: 'auth_step_2_validation_error',
          field_name: 'mobileNumber',
          step: 2,
        });
      }
    }

    handleSubmit(formData);
  };

  const buttonLabel = 'SEND ME THE CODE';
  const mobileNumberAsValue = data.mobileNumber?.replace('+61', '');

  const mobileNumberValues = form.getValues().mobileNumber;
  const mobileNumberLength = mobileNumberValues?.length ?? 0;

  const showMobileNumberError =
    mobileNumberLength > 3 &&
    !(
      mobileNumberValues?.[3] === '4' ||
      (mobileNumberValues?.[3] === '0' &&
        (mobileNumberLength === 4 || mobileNumberValues?.[4] === '4'))
    ) &&
    !isFormValid;

  return (
    <Container maw={470}>
      <Stack gap="xxs">
        <Stack gap="md">
          <Stack gap="sm">
            <Title order={3} fz={'xl'}>
              Secure your account
            </Title>
            <Text size="lg" lh={'20px'}>
              We'll send you a quick SMS code to verify your number - no passwords, no hassle.
            </Text>
          </Stack>
          <FormWithTracking name={FORM_NAME} form={form} handleSubmit={handleFormSubmit}>
            <Stack gap="xxl">
              <Stack gap="md">
                {authError === AuthErrorCode.TOO_MANY_ATTEMPTS && <OtpAttemptsAlert />}
                <NumberInput
                  key="mobileNumber"
                  label="Phone number"
                  required
                  size="lg"
                  lh={'20px'}
                  type="tel"
                  hideControls
                  trimLeadingZeroesOnBlur
                  leftSection={
                    <Text size="lg" c="gray.4" mt="2px">
                      +61
                    </Text>
                  }
                  value={data.mobileNumber ? Number(mobileNumberAsValue) : undefined}
                  onChange={(e) => {
                    // Remove leading zeros from the input
                    const cleanedInput = String(e).replace(/^0+/, '');
                    const formattedValue = `+61${cleanedInput}`;
                    //submit with full mobile number format
                    form.setFieldValue('mobileNumber', formattedValue as string);
                  }}
                  styles={{
                    input: {
                      border: '1px solid #f2f2f0',
                      borderRadius: '4px',
                    },
                  }}
                  error={
                    showMobileNumberError
                      ? 'Please enter a valid Australian mobile number'
                      : undefined
                  }
                />
              </Stack>
              <ButtonWithTracking
                label={buttonLabel}
                type="submit"
                disabled={!isFormValid || authError === AuthErrorCode.TOO_MANY_ATTEMPTS}
                size="lg"
                fullWidth
                variant="primary"
                position="contact-number-verification"
                purpose="send-verification-code"
                category={EventCategory.CMS}
                style={{
                  backgroundColor: '#4B1F68',
                  opacity: isFormValid && authError !== AuthErrorCode.TOO_MANY_ATTEMPTS ? 1 : 0.4,
                }}
              />
            </Stack>
          </FormWithTracking>
        </Stack>
        <Stack gap="xxs">
          <Text size="xs" c="gray.6" ta="center" lh={1.4}>
            We are collecting your personal information in order to assist you with your home loan
            application or any other ancillary products. By clicking the {buttonLabel} button you
            have read, consented and agreed to be bound by{' '}
            <AnchorWithTracking
              label="Aussie's Privacy Statement"
              purpose="privacy-statement"
              position="contact-number-verification"
              href="/about-us/privacy/"
              fw="bold"
            />{' '}
            and the{' '}
            <AnchorWithTracking
              label="End User Terms"
              purpose="full-user-terms"
              position="contact-number-verification"
              href="/about-us/property-end-terms/"
              fw="bold"
            />
            .
          </Text>
          <Group justify="center" gap="xxxs">
            <Lock size={16} color="gray.4" />
            <Text size="xs" c="gray.4">
              256bit encryption
            </Text>
          </Group>
        </Stack>
      </Stack>
    </Container>
  );
}
