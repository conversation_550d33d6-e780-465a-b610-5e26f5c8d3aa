export enum FunnelVariant {
  BP_CALC = 'bp-calc',
  NEW_PURCHASE = 'new-purchase',
  REFINANCE = 'refinance',
}
export interface FunnelTextConfig {
  title: string;
  subtitle: string;
  buttonLabel: string;
}

export const FUNNEL_TEXT_CONFIG: Record<FunnelVariant, FunnelTextConfig> = {
  [FunnelVariant.BP_CALC]: {
    title: "Let's get your borrowing power results",
    subtitle:
      "We've run the numbers - let's save your details so you can access your personalised borrowing power insights anytime.",
    buttonLabel: 'VIEW MY BORROWING POWER',
  },
  [FunnelVariant.NEW_PURCHASE]: {
    title: "Let's match you with some great rates",
    subtitle:
      "We've run the numbers - let's save your details so you can access your personalised preliminary rate matches anytime.",
    buttonLabel: 'VIEW MY RATE MATCHES',
  },
  [FunnelVariant.REFINANCE]: {
    title: 'See how much you could save by refinancing',
    subtitle:
      "We've done the numbers - lock in your best rates and unlock your personalised refinance insights in just a few taps.",
    buttonLabel: 'UNLOCK MY SAVINGS',
  },
};

export enum FeatureFlagVariant {
  THREE_STEP = 'three-step',
  TWO_STEP = 'two-step',
}
