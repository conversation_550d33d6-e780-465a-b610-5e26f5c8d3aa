import { Alert, Text } from '@mantine/core';

import { Error } from '@gp/ui/icons';

export default function OtpAttemptsAlert() {
  return (
    <Alert
      variant="light"
      color="red"
      title="Login limit reached"
      icon={<Error size={16} />}
      styles={{
        root: {
          backgroundColor: '#FFF3F5',
          border: '1px solid #FF1C4A',
        },
        title: {
          color: '#FF1C4A',
          fontWeight: 600,
        },
        body: {
          color: '#FF1C4A',
        },
        icon: {
          color: '#FF1C4A',
        },
      }}
    >
      Login temporarily suspended for 24 hours due to multiple login attempts. Please try again
      later or{' '}
      <Text
        component="a"
        href="/get-in-touch/"
        target="_blank"
        size="sm"
        td="underline"
        style={{ color: '#FF1C4A', cursor: 'pointer' }}
      >
        contact us
      </Text>{' '}
      for further assistance.
    </Alert>
  );
}
