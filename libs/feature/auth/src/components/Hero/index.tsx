import {
  Anchor,
  BackgroundImage,
  Center,
  Container,
  Flex,
  Group,
  Stack,
  Text,
  Title,
} from '@mantine/core';

import {
  AussieLogoWhite,
  Australia,
  Bank,
  HeartHandshake,
  HomeHeart,
  HomeSearch,
  License,
  ProductReviewWith5Stars,
} from '@gp/ui/icons';

import { FunnelVariant } from '../constants';

export default function AuthHero({
  variant = FunnelVariant.NEW_PURCHASE,
}: {
  variant?: FunnelVariant;
}) {
  return (
    <BackgroundImage
      src="/growth-product-assets/auth/hero.jpg"
      mih={{ base: 'calc(100vh - 56px)', lg: 'calc(100vh - 88px)' }}
    >
      <Center py={{ base: 0, sm: 'xxl' }}>
        <Stack gap="xxl" align="stretch" justify="space-between" p="lg">
          <Container maw={680}>
            <Stack gap="md" align="center">
              <AussieLogoWhite size={90} role="img" aria-label="Aussie Home Loans logo" />
              <Title order={2} fz={'32px'} lh={'40px'} ta="center" c="white">
                {`Over 1 million Australians have used Aussie to ${
                  variant === FunnelVariant.NEW_PURCHASE || variant === FunnelVariant.BP_CALC
                    ? 'find and buy their home.'
                    : 'review their loan'
                }`}
              </Title>
            </Stack>
          </Container>
          <Flex direction={'column'} justify={'space-between'} gap="xxl" align="center">
            <Stack gap="md" align="center">
              {variant === FunnelVariant.NEW_PURCHASE || variant === FunnelVariant.BP_CALC ? (
                <>
                  <Group gap="8" align="flex-start">
                    <HeartHandshake size={24} fill="white" />
                    <Text c="white" fz="lg">
                      Over 1300 Aussie Brokers nationwide to help you
                    </Text>
                  </Group>
                  <Group gap="8" align="flex-start">
                    <License size={24} fill="white" />
                    <Text c="white" fz="lg">
                      3 free standard contract reviews¹ by Settle Easy before you commit to buy
                    </Text>
                  </Group>
                  <Group gap="8" align="flex-start">
                    <HomeSearch size={24} fill="white" />
                    <Text c="white" fz="lg">
                      Search 100,000+ listings with Aussie’s property search
                    </Text>
                  </Group>
                </>
              ) : (
                <>
                  <Group gap="8" align="flex-start">
                    <HomeHeart size={24} fill="white" />
                    <Text c="white" fz="lg">
                      30+ years helping Australians with their home loans
                    </Text>
                  </Group>
                  <Group gap="8" align="flex-start">
                    <Bank size={24} fill="white" />
                    <Text c="white" fz="lg">
                      25+ lenders and 4,000+ loan options
                    </Text>
                  </Group>
                  <Group gap="8" align="flex-start">
                    <Australia size={24} fill="white" />
                    <Text c="white" fz="lg">
                      Options to lower repayments or unlock equity*
                    </Text>
                  </Group>
                </>
              )}
            </Stack>
            <ProductReviewWith5Stars
              size={170}
              color="white"
              role="img"
              aria-label="5000+ 5 star reviews for Aussie Home Loans in ProductReview.com.au"
            />
            {(variant === FunnelVariant.NEW_PURCHASE || variant === FunnelVariant.BP_CALC) && (
              <Text c="white" fz="xs" ta="center" maw="620px">
                ¹Settle Easy provides up to 3 free standard contract reviews of residential
                contracts of sale, valued at $990 in total. A standard contract review excludes
                urgent requests (under 48 hours), complex residential transactions and contracts
                with extensive special conditions or exceeding 1,000 pages. Please consult the
                Service and Fee Schedule available on the{' '}
                <Anchor
                  c="white"
                  fz="xs"
                  href="https://www.settleeasy.com.au/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Settle Easy website
                </Anchor>{' '}
                for a comprehensive overview of the services offered, associated fees, and any
                potential additional charges.
              </Text>
            )}
          </Flex>
        </Stack>
      </Center>
    </BackgroundImage>
  );
}
