'use client';

import { useContext, useEffect, useRef, useState } from 'react';
import { Alert, Box, Container, Flex, Group, PinInput, Stack, Text, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { EventCategory } from '@lendi/analytics-web';
import { AuthErrorCode } from '@lendi/auth';

import { Customer } from '@gp/data-access/customer';
import { LDContext } from '@gp/shared/launchdarkly';
import { ButtonWithTracking, FormWithTracking } from '@gp/ui/components';
import { Edit, Info } from '@gp/ui/icons';
import { formatMobileNumber } from '@gp/util/intl';

import OtpAttemptsAlert from '../OtpAttemptsAlert';

interface VerificationProps {
  data: Partial<Customer>;
  handleSubmit: (data: Partial<Customer>) => void;
  onEditNumber?: () => void;
  onResendCode?: (mobileNumber: string, firstName: string) => void;
  onVerifyCode?: (code: string) => Promise<void>;
  authError?: AuthErrorCode | null;
}

const FORM_NAME = 'Static Auth - Verification';

const validateData = (verificationCode?: string) => {
  return !!(verificationCode && verificationCode.length === 6);
};

const resendCodeTimerCountdown = 29;

export default function Verification({
  data,
  handleSubmit,
  onEditNumber,
  onResendCode,
  onVerifyCode,
  authError,
}: VerificationProps) {
  const { analytics } = useContext(LDContext);
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [isFormValid, setIsFormValid] = useState(false);
  const [countdown, setCountdown] = useState(resendCodeTimerCountdown);
  const [canResend, setCanResend] = useState(false);
  const [showAlertBox, setShowAlertBox] = useState(false);
  const [verificationError, setVerificationError] = useState<string>('');
  const hasTrackedView = useRef(false);
  const hasTrackedOtpEntered = useRef(false);

  // Track page view on mount
  useEffect(() => {
    if (!hasTrackedView.current) {
      analytics?.trackEvent({
        event_name: 'auth_step_3_viewed',
        step: 3,
      });
      hasTrackedView.current = true;
    }
  }, [analytics]);

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: data,
  });

  const buttonLabel = 'VERIFY CODE';

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
      setShowAlertBox(true);
    }
  }, [countdown]);

  const handlePinChange = (value: string) => {
    setVerificationCode(value);
    setIsFormValid(validateData(value));

    // Track OTP entered (only once when 6 digits are entered)
    if (value.length === 6 && !hasTrackedOtpEntered.current) {
      analytics?.trackEvent({
        event_name: 'auth_step_3_otp_entered',
        step: 3,
      });
      hasTrackedOtpEntered.current = true;
    }

    // Clear error when user starts typing
    if (verificationError) {
      setVerificationError('');
    }
  };

  const handleVerifyCodeWithError = async (code: string) => {
    try {
      await onVerifyCode?.(code);
      setVerificationError(''); // Clear error on success

      // Track successful verification
      analytics?.trackEvent({
        event_name: 'auth_step_3_submit_success',
        step: 3,
      });
    } catch (error) {
      setVerificationError('Verification code entered is invalid, please retry.');

      // Track failed verification
      analytics?.trackEvent({
        event_name: 'auth_step_3_submit_failed',
        step: 3,
        error_message: 'Verification code entered is invalid, please retry.',
      });
    }
  };

  const handleFormSubmit = async () => {
    const isValid = validateData(verificationCode);
    setIsFormValid(isValid);
    if (isValid && verificationCode) {
      // Call the verification with error handling and tracking
      await handleVerifyCodeWithError(verificationCode);

      // If verification succeeds, submit the form data
      const submissionData = {
        ...data,
        verificationCode,
      };
      handleSubmit(submissionData);
    }
  };

  const handleResendClick = () => {
    if (canResend) {
      // Track resend click
      analytics?.trackEvent({
        event_name: 'auth_step_3_resend_clicked',
        step: 3,
      });

      setCountdown(resendCodeTimerCountdown);
      setCanResend(false);
      setVerificationCode('');
      setIsFormValid(false);
      // Reset OTP tracking so it can be tracked again
      hasTrackedOtpEntered.current = false;
      onResendCode?.(data.mobileNumber as string, data.firstName as string);
    }
  };

  const formattedMobileNumber = formatMobileNumber(data.mobileNumber as string);
  const isResendButtonEnabled = canResend && authError !== AuthErrorCode.TOO_MANY_ATTEMPTS;

  return (
    <Container maw={470}>
      <Stack gap="xl">
        <Stack gap="md">
          <Stack gap="sm">
            <Title order={3} fz={'xl'}>
              Enter verification code
            </Title>
            <Flex gap="xs" justify="space-between" align="center">
              {data.mobileNumber && (
                <>
                  <Text size="lg">Sent to {formattedMobileNumber}</Text>
                  <ButtonWithTracking
                    variant="transparent"
                    fz="md"
                    lh={'xs'}
                    p={0}
                    c="grape.8"
                    td="underline"
                    label="Edit number"
                    position="verification-code"
                    purpose="edit-phone-number"
                    category={EventCategory.CMS}
                    leftSection={<Edit size={16} color="grape.8" />}
                    onClick={onEditNumber}
                  />
                </>
              )}
            </Flex>
          </Stack>
          {showAlertBox && (
            <Alert
              color="blue"
              variant="light"
              radius="md"
              icon={<Info size={16} color="#488BD9" />}
              styles={{
                root: {
                  backgroundColor: '#F5F9FD',
                  border: '1px solid #488BD9',
                },
              }}
            >
              <Stack gap="xs">
                <Text size="sm" fw={600} c="#488BD9">
                  Not receiving your one-time passcode?
                </Text>
                <Text size="sm" c="#488BD9">
                  If you are an android user - please look at your junk / spam folder in your
                  messages app.
                </Text>
              </Stack>
            </Alert>
          )}
          {authError === AuthErrorCode.TOO_MANY_ATTEMPTS && !!verificationError && (
            <OtpAttemptsAlert />
          )}
          <FormWithTracking name={FORM_NAME} form={form} handleSubmit={handleFormSubmit}>
            <Stack gap="xl" align="stretch">
              <Stack gap="xs">
                <Box h="75px">
                  <PinInput
                    length={6}
                    size="xl"
                    type="number"
                    value={verificationCode}
                    onChange={handlePinChange}
                    oneTimeCode
                    placeholder=""
                    error={!!verificationError}
                    styles={{
                      root: {
                        display: 'flex',
                        justifyContent: 'space-between',
                      },
                      input: {
                        textAlign: 'center',
                        borderRadius: '4px',
                        height: '75px',
                        border: verificationError ? '1px solid #F03E3E' : '1px solid #8F8F8E',
                      },
                    }}
                  />
                </Box>
                {verificationError && (
                  <Text size="sm" c="#F03E3E" mt="xs">
                    {verificationError}
                  </Text>
                )}
              </Stack>
              <ButtonWithTracking
                label={buttonLabel}
                type="submit"
                disabled={!isFormValid}
                size="lg"
                fullWidth
                variant="primary"
                position="verification-code"
                purpose="verify-phone-number"
                category={EventCategory.CMS}
                style={{
                  backgroundColor: '#4B1F68',
                  opacity: isFormValid ? 1 : 0.4,
                }}
              />
            </Stack>
          </FormWithTracking>
          <Group justify="flex-start" mt="md">
            <Text size="md" lh={'xs'} c="gray.7">
              Didn't receive an SMS? (
              {countdown > 0 ? `0:${countdown.toString().padStart(2, '0')}` : '0:00'})
            </Text>
            <ButtonWithTracking
              variant="transparent"
              fz="md"
              lh={'xs'}
              p={0}
              c={isResendButtonEnabled ? 'grape.8' : 'gray.5'}
              td="underline"
              label="Resend code"
              position="verification-code"
              purpose="resend-code"
              category={EventCategory.CMS}
              onClick={isResendButtonEnabled ? handleResendClick : undefined}
              style={{
                opacity: isResendButtonEnabled ? 1 : 0.4,
                cursor: isResendButtonEnabled ? 'pointer' : 'not-allowed',
              }}
            />
          </Group>
        </Stack>
      </Stack>
    </Container>
  );
}
