'use client';

import { useContext, useEffect, useRef, useState } from 'react';
import { Container, Group, NumberInput, Stack, Text, TextInput, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { EventCategory } from '@lendi/analytics-web';

import { Customer } from '@gp/data-access/customer';
import { LDContext } from '@gp/shared/launchdarkly';
import { AnchorWithTracking, ButtonWithTracking, FormWithTracking } from '@gp/ui/components';
import { Lock } from '@gp/ui/icons';
import { verifyAustralianMobileNumber, verifyEmailFormat } from '@gp/util/intl';

import { FUNNEL_TEXT_CONFIG, FunnelVariant } from '../constants';

interface NameEmailMobileProps {
  variant: FunnelVariant;
  data: Partial<Customer>;
  handleSubmit: (data: Partial<Customer>) => void;
}

const FORM_NAME = 'Static Auth - Name Email and Mobile';

const validateData = (data: Partial<Customer>) => {
  return (
    !!data.firstName &&
    verifyEmailFormat(data.email ?? '') &&
    verifyAustralianMobileNumber(data.mobileNumber ?? '')
  );
};

export default function NameEmailMobile({ variant, data, handleSubmit }: NameEmailMobileProps) {
  const { analytics } = useContext(LDContext);
  const [isFormValid, setIsFormValid] = useState(validateData(data));
  const hasTrackedView = useRef(false);
  const hasTrackedFieldFocus = useRef({
    firstName: false,
    email: false,
    mobileNumber: false,
  });

  // Track page view on mount
  useEffect(() => {
    if (!hasTrackedView.current) {
      analytics?.trackEvent({
        event_name: 'auth_step_1_viewed',
        step: 1,
        variant,
      });
      hasTrackedView.current = true;
    }
  }, [analytics, variant]);

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: data,
    onValuesChange: (formData) => {
      setIsFormValid(validateData(formData));
    },
  });

  // Track field interactions (only once per field)
  const handleFieldFocus = (fieldName: 'firstName' | 'email' | 'mobileNumber') => {
    if (!hasTrackedFieldFocus.current[fieldName]) {
      analytics?.trackEvent({
        event_name: 'auth_step_1_field_interacted',
        field_name: fieldName,
        step: 1,
        variant,
      });
      hasTrackedFieldFocus.current[fieldName] = true;
    }
  };

  // Track submit button click
  const handleFormSubmit = (formData: Partial<Customer>) => {
    analytics?.trackEvent({
      event_name: 'auth_step_1_submit_clicked',
      step: 1,
      variant,
      is_form_valid: isFormValid,
    });

    // Track validation errors on submit if form is invalid
    if (!isFormValid) {
      const firstNameValid = !!formData.firstName;
      const emailValid = verifyEmailFormat(formData.email ?? '');
      const mobileNumberValid = verifyAustralianMobileNumber(formData.mobileNumber ?? '');

      if (!firstNameValid) {
        analytics?.trackEvent({
          event_name: 'auth_step_1_validation_error',
          field_name: 'firstName',
          step: 1,
          variant,
        });
      }

      if (!emailValid) {
        analytics?.trackEvent({
          event_name: 'auth_step_1_validation_error',
          field_name: 'email',
          step: 1,
          variant,
        });
      }

      if (!mobileNumberValid) {
        analytics?.trackEvent({
          event_name: 'auth_step_1_validation_error',
          field_name: 'mobileNumber',
          step: 1,
          variant,
        });
      }
    }

    handleSubmit(formData);
  };

  const textConfig = FUNNEL_TEXT_CONFIG[variant];
  const mobileNumberAsValue = data.mobileNumber?.replace('+61', '');

  const mobileNumberValues = form.getValues().mobileNumber;
  const mobileNumberLength = mobileNumberValues?.length ?? 0;

  const showMobileNumberError =
    mobileNumberLength > 3 &&
    !(
      mobileNumberValues?.[3] === '4' ||
      (mobileNumberValues?.[3] === '0' &&
        (mobileNumberLength === 4 || mobileNumberValues?.[4] === '4'))
    ) &&
    !verifyAustralianMobileNumber(mobileNumberValues ?? '');

  return (
    <Container maw={470}>
      <Stack gap="xxs">
        <Stack gap="md">
          <Stack gap="sm">
            <Title order={3} fz={'xl'}>
              {textConfig.title}
            </Title>
            <Text size="lg" lh={'20px'}>
              {textConfig.subtitle}
            </Text>
          </Stack>
          <FormWithTracking name={FORM_NAME} form={form} handleSubmit={handleFormSubmit}>
            <Stack gap="xxl">
              <Stack gap="md">
                <TextInput
                  key="firstName"
                  label="First name"
                  placeholder="Joe"
                  required
                  size="lg"
                  lh={'20px'}
                  styles={{
                    input: {
                      border: '1px solid #f2f2f0',
                      borderRadius: '4px',
                    },
                  }}
                  {...form.getInputProps('firstName')}
                  onFocus={(event) => {
                    handleFieldFocus('firstName');
                    form.getInputProps('firstName').onFocus?.(event);
                  }}
                />
                <TextInput
                  key="email"
                  label="Email"
                  placeholder="<EMAIL>"
                  type="email"
                  required
                  size="lg"
                  lh={'20px'}
                  styles={{
                    input: {
                      border: '1px solid #f2f2f0',
                      borderRadius: '4px',
                    },
                  }}
                  {...form.getInputProps('email')}
                  onFocus={(event) => {
                    handleFieldFocus('email');
                    form.getInputProps('email').onFocus?.(event);
                  }}
                />
                <NumberInput
                  key="mobileNumber"
                  label="Phone number"
                  required
                  size="lg"
                  lh={'20px'}
                  type="tel"
                  hideControls
                  trimLeadingZeroesOnBlur
                  leftSection={
                    <Text size="lg" c="gray.4" mt="2px">
                      +61
                    </Text>
                  }
                  value={data.mobileNumber ? Number(mobileNumberAsValue) : undefined}
                  onChange={(e) => {
                    // Remove leading zeros from the input
                    const cleanedInput = String(e).replace(/^0+/, '');
                    const formattedValue = `+61${cleanedInput}`;
                    //submit with full mobile number format
                    form.setFieldValue('mobileNumber', formattedValue as string);
                  }}
                  styles={{
                    input: {
                      border: '1px solid #f2f2f0',
                      borderRadius: '4px',
                    },
                  }}
                  error={
                    showMobileNumberError
                      ? 'Please enter a valid Australian mobile number'
                      : undefined
                  }
                  onFocus={() => {
                    handleFieldFocus('mobileNumber');
                  }}
                />
              </Stack>
              <Stack gap="xs">
                <Group justify="center" gap="xxxs">
                  <Lock size={16} color="gray.4" />
                  <Text size="xs" c="gray.4">
                    Secure password-free access
                  </Text>
                </Group>
                <ButtonWithTracking
                  label={textConfig.buttonLabel}
                  disabled={!isFormValid}
                  type="submit"
                  size="lg"
                  fullWidth
                  variant="primary"
                  position={`${variant} ${FORM_NAME}`}
                  purpose="save-user-details"
                  category={EventCategory.CMS}
                  style={{
                    backgroundColor: '#4B1F68',
                    opacity: isFormValid ? 1 : 0.4,
                    cursor: isFormValid ? 'pointer' : 'not-allowed',
                  }}
                />
              </Stack>
            </Stack>
          </FormWithTracking>
        </Stack>
        <Stack gap="xxs">
          <Text size="xs" c="gray.6" ta="center" lh={1.4}>
            We are collecting your personal information in order to assist you with your home loan
            application or any other ancillary products. By clicking the {textConfig.buttonLabel}{' '}
            button you have read, consented and agreed to be bound by{' '}
            <AnchorWithTracking
              label="Aussie's Privacy Statement"
              purpose="privacy-statement"
              position="borrowing-power-results"
              href="/about-us/privacy/"
              fw="bold"
            />{' '}
            and the{' '}
            <AnchorWithTracking
              label="End User Terms"
              purpose="full-user-terms"
              position="borrowing-power-results"
              href="/about-us/property-end-terms/"
              fw="bold"
            />
            .
          </Text>
        </Stack>
      </Stack>
    </Container>
  );
}
