'use client';

import { useState } from 'react';
import { Stepper, StepperCompleted } from '@mantine/core';
import { useMediaQuery, useSessionStorage } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import { type CustomerSituation, customerSituationSchema } from '@gp/data-access/conveyancing';
import { SessionRedirect } from '@gp/shared/session-redirect';
import { PageTracking } from '@gp/ui/components';
import { setConveyancingLead } from '@gp/util/leads';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import {
  Confirmation,
  ConveyancingService,
  QuoteEstimation,
  UserSituation,
} from '../../components';

export default function ConveyancingEngagement() {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const isMobile = useMediaQuery('(max-width: 36em)');
  const [customerSituation, setCustomerSituation] = useSessionStorage<Partial<CustomerSituation>>({
    key: 'GP_CONVEYANCING_ENGAGEMENT',
    getInitialValueInEffect: false,
    defaultValue: {},
  });
  const [active, setActive] = useState(
    customerSituationSchema.safeParse(customerSituation).success ? 2 : 0
  );

  const handleFormSubmit = (data: Partial<CustomerSituation>) => {
    setCustomerSituation((current) => ({ ...current, ...data }));
    if (active === 1 && !isAuthenticated) {
      setConveyancingLead({ ...customerSituation, ...data });
    }

    setActive((current) => current + 1);
  };

  const hideStepper = active === 2 && !isAuthenticated;

  return (
    <PageTracking name="Conveyancing Engagement" category="FUNNELS" withMarketingCloudTracking>
      <Stepper
        active={active}
        onStepClick={setActive}
        allowNextStepsSelect={false}
        maw={945}
        mx="auto"
        wrap={true}
        size={isMobile ? 'sm' : 'md'}
        display={hideStepper ? 'none' : 'block'}
      >
        <Stepper.Step
          label={isMobile ? 'Service selection' : 'First step'}
          description={isMobile ? undefined : 'Service selection'}
          allowStepSelect={active !== 2}
          allowStepClick={active !== 2}
        >
          <ConveyancingService data={customerSituation} handleSubmit={handleFormSubmit} />
        </Stepper.Step>
        <Stepper.Step
          label={isMobile ? 'Current status' : 'Second step'}
          description={isMobile ? undefined : 'Current status'}
          allowStepSelect={false}
          allowStepClick={false}
        >
          <UserSituation data={customerSituation} handleSubmit={handleFormSubmit} />
        </Stepper.Step>
        <Stepper.Step
          label={isMobile ? 'Quotation' : 'Third step'}
          description={isMobile ? undefined : 'Estimation'}
          allowStepSelect={false}
          allowStepClick={false}
        >
          <SessionRedirect
            returnURL={embedMarketingTrackingParams(
              `/sign-in/${generateReturnURL(['/conveyancing/engagement/'])}`
            )}
          >
            <QuoteEstimation
              data={customerSituation}
              handleSubmit={handleFormSubmit}
              setActive={() => setActive(3)}
            />
          </SessionRedirect>
        </Stepper.Step>
        <Stepper.Step
          label={isMobile ? 'Confirmation' : 'Final step'}
          description={isMobile ? undefined : 'Confirmation'}
          allowStepSelect={false}
          allowStepClick={false}
        >
          <Confirmation
            data={customerSituation}
            setCompleted={() => setActive(4)}
            cleanUp={() => {
              setCustomerSituation({});
            }}
          />
        </Stepper.Step>
        <StepperCompleted>
          <Confirmation data={customerSituation} cleanUp={() => setCustomerSituation({})} />
        </StepperCompleted>
      </Stepper>
    </PageTracking>
  );
}
