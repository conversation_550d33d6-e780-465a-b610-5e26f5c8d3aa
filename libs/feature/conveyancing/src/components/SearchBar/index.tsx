'use client';

import { ReactNode, useContext, useEffect, useState } from 'react';
import { Checkbox, Group, TextInput } from '@mantine/core';
import { EventCategory } from '@lendi/analytics-web';

import {
  getPropertyHubAPIs,
  SEARCH_RESULT_TYPE,
  type SearchResult,
} from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { Autocomplete } from '@gp/ui/components';
import { House, InfoOutline, Location, Pen, Search } from '@gp/ui/icons';

const searchCache: Record<string, SearchResult[]> = {};

const getSearchDescription = (type: SearchResult['type']) => {
  switch (type) {
    case 'suburb':
      return 'Suburb';
    case 'address':
      return 'Address';
  }
};

interface SearchBarProps {
  searchType: SEARCH_RESULT_TYPE;
  onSelect: (label: string) => void;
  allowManualInput?: boolean; //allowed for Address search, not allowed for Suburb search
}

type Option = {
  id?: string;
  value: string;
  label: string;
  icon?: ReactNode;
  description?: string;
  onClick?: () => void;
};

export default function SearchBar({ searchType, onSelect, allowManualInput }: SearchBarProps) {
  const [showManualInput, setShowManualInput] = useState(false);
  const { analytics } = useContext(LDContext);

  useEffect(() => {
    setShowManualInput(false);
  }, [searchType]);

  const getSearchResult = async (searchQuery?: string, searchType?: SEARCH_RESULT_TYPE) => {
    const { search } = getPropertyHubAPIs();
    let result: SearchResult[];
    if (!searchQuery || searchQuery.length < 3) {
      result = [];
    } else if (searchQuery in searchCache) {
      result = searchType
        ? searchCache[searchQuery].filter(({ type }) => type === searchType)
        : searchCache[searchQuery];
    } else {
      result = await search(searchQuery, 10, searchType ? [searchType] : undefined);
      searchCache[searchQuery] = result;
    }

    const results = result.map(({ id, name, slug, type }) => ({
      id,
      label: name,
      value: slug,
      description: getSearchDescription(type),
      icon:
        type === 'address' ? (
          <House size={14} color="white" />
        ) : (
          <Location size={14} color="white" />
        ),
    })) as Option[];

    const formattedResults = allowManualInput
      ? results.concat({
          label: '',
          value: '',
          icon: (
            <Checkbox
              label="Can't find what you're looking for? Enter your address manually instead"
              name="Enter address manually instead"
              onChange={() => setShowManualInput((state) => !state)}
              onClick={() => setShowManualInput((state) => !state)}
              checked={showManualInput}
              c="gray.5"
              size="sm"
            />
          ),
          onClick: () => setShowManualInput((state) => !state),
        })
      : results;

    return formattedResults;
  };

  return (
    <Group gap="xs">
      {showManualInput ? (
        <TextInput
          placeholder="Eg. 123 Main Road Sydney NSW 2000"
          leftSection={<Pen />}
          onBlur={(event) => {
            onSelect && onSelect(event.target.value);
          }}
          w="100%"
          style={{ border: '1px solid #D9D9D9' }}
        />
      ) : (
        <Autocomplete
          data-testid="conveyancing-location-search-autocomplete"
          id="search-bar"
          flex={1}
          options={(keyword) => getSearchResult(keyword, searchType)}
          leftSection={<Search />}
          placeholder={
            searchType === SEARCH_RESULT_TYPE.ADDRESS ? 'Search an address' : 'Search a suburb'
          }
          keepSearchText
          minSearchLength={3}
          noResults={{
            value: 'No result',
            label: 'No results found',
            description: `Try searching another ${
              searchType === SEARCH_RESULT_TYPE.ADDRESS ? 'address' : 'suburb'
            }`,
            icon: <InfoOutline size={16} />,
          }}
          onSelectOption={({ label }) => {
            onSelect && onSelect(label);
          }}
          onClick={() =>
            analytics?.trackEvent({
              event_name: 'Search Bar Clicked',
              category: EventCategory.CONVEYANCING,
              text:
                searchType === SEARCH_RESULT_TYPE.ADDRESS ? 'Search an address' : 'Search a suburb',
              position: 'Conveyancing User Situation location search bar',
            })
          }
        />
      )}
    </Group>
  );
}
