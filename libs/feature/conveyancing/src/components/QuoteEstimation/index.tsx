'use client';

import { useContext, useEffect, useState } from 'react';
import { Flex, Group, Paper, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { useDidUpdate } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import {
  benefitsList,
  CONVEYANCING_SERVICE_TYPE,
  CustomerSituation,
  disbursementFees,
  fixedProfessionalFees,
  submitEnquiry,
  transferCost,
} from '@gp/data-access/conveyancing';
import { LDContext } from '@gp/shared/launchdarkly';
import { ButtonWithTracking, Loader } from '@gp/ui/components';
import { SimpleCheck } from '@gp/ui/icons';
import { getEnvVar } from '@gp/util/data-service';
import { formatCurrency } from '@gp/util/intl';

export const FORM_NAME = 'Conveyancing Services';

interface QuotationProps {
  data: Partial<CustomerSituation>;
  handleSubmit: (data: Partial<CustomerSituation>) => void;
  setActive: () => void;
}

export default function QuoteEstimation({ data, handleSubmit, setActive }: QuotationProps) {
  const { status, identity, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const customerId = identity?.id;
  const [isLoading, setIsLoading] = useState(false);
  const [enquiryId, setEnquiryId] = useState('');
  const { analytics } = useContext(LDContext);

  useEffect(() => {
    if (isAuthenticated && customerId) {
      setIsLoading(true);
    }
  }, [customerId, isAuthenticated]);

  useDidUpdate(() => {
    if (isLoading && customerId && token) {
      submitEnquiry(customerId, token)
        .then((res) => {
          if (res) {
            setEnquiryId(res);
          } else {
            throw new Error(`Submission to Conveyancing Enquiry API failed`);
          }
        })
        .finally(() => {
          setIsLoading(false);
          analytics?.trackEvent({ event_name: 'Quote Estimation Displayed' });
        });
    }
  }, [isLoading, customerId, token, data]);

  const conveyancingService = data.conveyancingService?.split('_').join(' ').toLocaleLowerCase();

  const isPropertyTransfer =
    data.conveyancingService === CONVEYANCING_SERVICE_TYPE.PROPERTY_TRANSFER;

  const isSellingAndBuying =
    data.conveyancingService === CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING;

  const feeQuotation = fixedProfessionalFees.find((item) => {
    const { location } = data;
    const state = location?.split(' ').slice(-2)[0];
    return state?.includes(item.state) || state?.includes(item.state.toLocaleLowerCase());
  });

  // Determine the correct professional fee
  let professionalFeeValue = 0;
  if (isPropertyTransfer) {
    professionalFeeValue = transferCost;
  } else if (isSellingAndBuying) {
    professionalFeeValue = (feeQuotation?.price || 0) * 2;
  } else {
    professionalFeeValue = feeQuotation?.price || 0;
  }

  // Determine the correct disbursement fee
  let disbursementFeeValue = 0;
  const { location } = data;
  const state = location
    ?.split(' ')
    .slice(-2)[0]
    ?.toUpperCase() as keyof typeof disbursementFees.buyingOnly;
  if (state) {
    if (isPropertyTransfer) {
      disbursementFeeValue = disbursementFees.transferOnly[state] || 0;
    } else if (isSellingAndBuying) {
      // For selling and buying, sum both
      const buy = disbursementFees.buyingOnly[state] || 0;
      const sell = disbursementFees.sellingOnly[state] || 0;
      disbursementFeeValue = buy + sell;
    } else if (data.conveyancingService === CONVEYANCING_SERVICE_TYPE.BUYING) {
      disbursementFeeValue = disbursementFees.buyingOnly[state] || 0;
    } else if (data.conveyancingService === CONVEYANCING_SERVICE_TYPE.SELLING) {
      disbursementFeeValue = disbursementFees.sellingOnly[state] || 0;
    }
  }

  const handleReferralSubmit = () => {
    enquiryId && handleSubmit({ ...data, enquiryId });
    setActive();
  };

  const clearSessionStorage = () => {
    sessionStorage.removeItem('GP_CONVEYANCING_ENGAGEMENT');
    window.location.reload();
  };

  if (isLoading) return <Loader />;

  return (
    <Stack gap="lg" p={{ base: 'sm', sm: 'lg' }} bg="white" align="center">
      <Title order={1} size="h2" c="primary" ta="center">
        Conveyancing service finder
      </Title>
      <Stack gap="lg" align="center" style={{ maxWidth: 800, width: '100%' }}>
        <Stack gap="xxs" align="center">
          <Title order={2} fz="xl" ta="center">
            Your estimated conveyancing fees
          </Title>
          <Text fz="sm" ta="center">
            Based on your selections, we estimate your conveyancing cost to be:
          </Text>
        </Stack>
        <Paper
          radius="md"
          p="xl"
          bg="grape.1"
          style={{ border: '1px solid #4b1f68', width: '100%' }}
        >
          <Stack gap="sm" align="center">
            <Text fz="sm" fw={500} c="gray.7" ta="center" mb={-8}>
              Estimated fee (incl. GST):
            </Text>
            <Title order={4} fz={32} c="grape.8" ta="center" lh={1.1}>
              {formatCurrency(professionalFeeValue)}
              <Text span fz="md" fw={400} c="gray.7" ml={8}>
                Fixed professional fees
              </Text>
            </Title>
            <Text fz={32} fw={700} c="grape.8" ta="center" mt={-8}>
              +
            </Text>
            <Text fz="sm" fw={500} c="gray.7" ta="center" mb={-8}>
              Estimated expenses:
            </Text>
            <Title order={4} fz={32} c="grape.8" ta="center" lh={1.1}>
              {formatCurrency(disbursementFeeValue)}
              <Text span fz="md" fw={400} c="gray.7" ml={8}>
                Disbursements
              </Text>
            </Title>
            <SimpleGrid cols={{ base: 1, md: 2 }} spacing="lg" pt="sm" mt="md">
              {benefitsList.map(({ title, description }) => (
                <Flex key={title} gap="xs" align="flex-start" justify="flex-start">
                  <SimpleCheck color="green" size={24} />
                  <Stack gap="xxxs">
                    <Text fz="lg" fw="600">
                      {title}
                    </Text>
                    <Text fz="sm">{description}</Text>
                  </Stack>
                </Flex>
              ))}
            </SimpleGrid>
          </Stack>
        </Paper>
        <ButtonWithTracking
          type="submit"
          miw={200}
          label="Get an obligation-free callback"
          position={FORM_NAME}
          onClick={handleReferralSubmit}
        />
        <Text fz="xs" c="gray.7" ta="center" style={{ maxWidth: 600 }}>
          By clicking the button above, you consent to Settle Easy contacting you to assist with
          your conveyancing needs. You also agree that your personal information may be shared with
          Settle Easy to facilitate your transaction, and that Aussie may receive relevant updates
          and information from Settle Easy – including, where applicable, a contract review – for
          this purpose.
        </Text>
        {getEnvVar('GP_APP_ENVIRONMENT') === 'development' && (
          // eslint-disable-next-line jsx-a11y/accessible-emoji
          <button
            onClick={clearSessionStorage}
            style={{
              position: 'fixed',
              bottom: '10px',
              right: '10px',
              fontSize: '10px',
              padding: '2px 4px',
              backgroundColor: '#ff6b6b',
              color: 'white',
              border: 'none',
              borderRadius: '2px',
              cursor: 'pointer',
              zIndex: 9999,
            }}
            title="Clear GP_CONVEYANCING_ENGAGEMENT from session storage"
          >
            🗑️
          </button>
        )}
      </Stack>
    </Stack>
  );
}
