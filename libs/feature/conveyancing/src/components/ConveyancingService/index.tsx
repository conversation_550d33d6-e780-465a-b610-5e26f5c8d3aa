'use client';

import { useState } from 'react';
import { ActionIcon, Group, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { useForm } from '@mantine/form';

import {
  CONVEYANCING_SERVICE_TYPE,
  conveyancingServiceTypeOptions,
  CustomerSituation,
} from '@gp/data-access/conveyancing';
import { ButtonWithTracking, CheckboxCardWithTracking, FormWithTracking } from '@gp/ui/components';
import { ArrowCycle, ArrowForward, House2, PaperAeroplane, People } from '@gp/ui/icons';

export const FORM_NAME = 'Conveyancing Services';

const validateData = (data: Partial<CustomerSituation>) => {
  return !!data.conveyancingService;
};

interface ConveyancingServiceProps {
  data: Partial<CustomerSituation>;
  handleSubmit: (data: Partial<CustomerSituation>) => void;
}

export default function ConveyancingService({ data, handleSubmit }: ConveyancingServiceProps) {
  const [isFormValid, setIsFormValid] = useState(validateData(data));

  const form = useForm({
    mode: 'controlled',
    initialValues: data,
    onValuesChange: (formData) => {
      setIsFormValid(validateData(formData));
    },
  });

  const formValue = form.getInputProps('conveyancingService').value;

  return (
    <Stack gap="lg" p={{ base: 'sm', sm: 'lg' }} bg="white">
      <Title order={1} size="h2" c="primary" ta="center">
        Conveyancing service finder
      </Title>
      <FormWithTracking name={FORM_NAME} form={form} handleSubmit={handleSubmit}>
        <Stack gap="lg">
          <Stack gap="xxs">
            <Title order={4} fz="lg">
              Select conveyancing service
            </Title>
            <Text fz="sm">Choose the service that best matches your needs</Text>
          </Stack>
          <SimpleGrid cols={{ base: 1, md: 2 }}>
            {conveyancingServiceTypeOptions.map(({ value, label, subtitle }) => (
              <CheckboxCardWithTracking
                showCheckbox={false}
                key={value}
                p="xs"
                radius={0}
                bg={value === formValue ? 'grape.1' : 'white'}
                style={{
                  border:
                    value === formValue
                      ? '1px solid var(--mantine-color-grape-8)'
                      : '1px solid var(--mantine-color-gray-2)',
                }}
                label={label}
                subtitle={subtitle}
                icon={
                  <ActionIcon
                    variant={value === formValue ? 'filled' : 'subtle'}
                    bg={value === formValue ? 'grape.8' : 'gray.2'}
                    size="xl"
                  >
                    {value === CONVEYANCING_SERVICE_TYPE.BUYING ? (
                      <House2 width={24} height={24} />
                    ) : value === CONVEYANCING_SERVICE_TYPE.SELLING ? (
                      <PaperAeroplane width={24} height={24} />
                    ) : value === CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING ? (
                      <ArrowCycle width={24} height={24} />
                    ) : (
                      <People width={24} height={24} />
                    )}
                  </ActionIcon>
                }
                onClick={() => {
                  form.setValues({ conveyancingService: value });
                }}
                position={`Conveyancing service ${value.toLocaleLowerCase()} checkbox`}
              />
            ))}
          </SimpleGrid>
          <Group justify="flex-end">
            <ButtonWithTracking
              type="submit"
              rightSection={<ArrowForward />}
              miw={200}
              disabled={!isFormValid}
              label="Continue"
              position={FORM_NAME}
            />
          </Group>
        </Stack>
      </FormWithTracking>
    </Stack>
  );
}
