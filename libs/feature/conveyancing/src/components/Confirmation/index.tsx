'use client';

import { useContext, useEffect, useState } from 'react';
import { Flex, Paper, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { getBrokerAPIs } from '@gp/data-access/broker';
import {
  CONVEYANCING_SERVICE_TYPE,
  CustomerSituation,
  DEFAULT_BROKER,
  FINANCING_STATUS,
  getCaseOwner,
  RESULTS_CTA_TYPE,
  resultsCTAs,
  submitEngagementReferral,
  USER_SITUATION,
} from '@gp/data-access/conveyancing';
import { fetchCustomer } from '@gp/data-access/customer';
import { LDContext } from '@gp/shared/launchdarkly';
import {
  AlertWithTracking,
  AnchorWithTracking,
  LinkButtonWithTracking,
  Loader,
} from '@gp/ui/components';
import { <PERSON>For<PERSON>, Check, Error as ErrorIcon } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import classes from './style.module.css';

export const FORM_NAME = 'Conveyancing Services';

interface ConfirmationProps {
  data: Partial<CustomerSituation>;
  setCompleted?: () => void;
  cleanUp: () => void;
}

export default function Confirmation({ data, setCompleted, cleanUp }: ConfirmationProps) {
  const { status, token, identity } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const customerId = identity?.id;
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { getBrokerDetails } = getBrokerAPIs();
  const { analytics } = useContext(LDContext);

  // Fetch the customer details if authenticated
  const { data: customer, isLoading: isLoadingCustomer } = useSWRImmutable(
    isAuthenticated && token ? 'me' : null,
    async () => fetchCustomer('me', token)
  );

  const { data: broker, isLoading: isLoadingBroker } = useSWRImmutable(
    customer?.ownerId && !isLoadingCustomer ? customer?.ownerId : null,
    async () => getBrokerDetails(customer?.ownerId as string)
  );

  const { data: caseOwner, isLoading: isLoadingCaseOwner } = useSWRImmutable(
    token && customerId && !isLoadingCustomer && !isLoadingBroker && !broker ? customerId : null,
    async () => getCaseOwner(customerId as string, token)
  );

  useEffect(() => {
    if (!isAuthenticated || !customerId) {
      window.location.assign(
        embedMarketingTrackingParams(`/sign-in/${generateReturnURL(['/conveyancing/engagement/'])}`)
      );
    } else {
      setIsLoading(true);
    }
  }, [customerId, isAuthenticated]);

  useEffect(() => {
    const brokerNameInArray = broker?.name ? broker?.name?.split(' ') : [];
    const formattedBroker = broker
      ? {
          id: broker.id,
          firstName: brokerNameInArray[0],
          lastName: brokerNameInArray[1],
          email: broker.email,
        }
      : caseOwner
      ? caseOwner
      : DEFAULT_BROKER;

    if (
      isLoading &&
      customer &&
      token &&
      !isLoadingBroker &&
      !isLoadingCaseOwner &&
      formattedBroker
    ) {
      submitEngagementReferral(customer, data, formattedBroker, token)
        .then((res) => {
          if (res?.id) {
            setCompleted && setCompleted();
            setIsSubmitted(true);
          } else {
            throw new Error(`Submission to Conveyancing Engagement API failed`);
          }
        })
        .finally(() => {
          setIsLoading(false);
          analytics?.trackEvent({ event_name: 'Confirmation Displayed' });
        });
    }
  }, [
    isLoading,
    customer,
    token,
    data,
    isLoadingBroker,
    isLoadingCaseOwner,
    broker,
    caseOwner,
    setCompleted,
    analytics,
  ]);

  //to clean up session storage when user navigates away
  const handler = () => {
    cleanUp();
  };
  useWindowEvent('beforeunload', handler);

  const filteredCTAs = resultsCTAs.filter((item) => {
    const list = [];
    const { conveyancingService, financingStatus, userSituation } = data;
    if (
      conveyancingService === CONVEYANCING_SERVICE_TYPE.BUYING ||
      conveyancingService === CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING
    ) {
      if (userSituation === USER_SITUATION.STILL_SEARCHING) {
        list.push(RESULTS_CTA_TYPE.BUYERS_AGENT);
      }
      if (userSituation === USER_SITUATION.FOUND_PROPERTY) {
        list.push(RESULTS_CTA_TYPE.BUYERS_AGENT_NEGOTIATION);
      }
      if (financingStatus === FINANCING_STATUS.NOT_PRE_APPROVED) {
        list.push(RESULTS_CTA_TYPE.GET_PRE_APPROVED);
      }
      return list.includes(item.type);
    }
    return false;
  });

  const isOneCTA = filteredCTAs.length === 1;

  if (!isAuthenticated || isLoading || isLoadingCustomer) return <Loader />;

  return (
    <Stack gap="lg" p={{ base: 'sm', sm: 'lg' }} bg="white">
      <Title order={1} size="h2" c="primary" ta="center">
        Conveyancing service finder
      </Title>
      {isSubmitted ? (
        <Stack gap="xxl">
          <Stack gap="xs" align="center" mt="sm">
            <Check size={64} fill="#4ec000" />
            <Title order={2} fz="xl">
              Thank you for your enquiry
            </Title>
            <Stack gap="xxxs">
              <Text fz="md" ta="center">
                You will get a call in 2 business hours from
              </Text>
              <Text fz="lg" c="grape.8" ta="center" fw="600">
                Aussie Conveyancing powered by Settle Easy
              </Text>
            </Stack>
          </Stack>
          {filteredCTAs.length > 0 && (
            <Stack gap="xxxs" align={isOneCTA ? 'center' : undefined}>
              <Text fz="md" ta="center" fw="600">
                While you wait, explore{' '}
                {isOneCTA ? 'this recommended service' : 'these recommended services'}
              </Text>
              <SimpleGrid cols={{ base: 1, md: isOneCTA ? 1 : 2 }} spacing="lg" pt="xxs">
                {filteredCTAs.map(({ title, subtitle, cta, link, type }) => (
                  <Stack
                    gap="xxs"
                    maw={{ base: '100%', md: isOneCTA ? '100%' : undefined }}
                    key={cta}
                  >
                    <AnchorWithTracking
                      key={cta}
                      label={''}
                      href={link}
                      underline="never"
                      c="black"
                      position={`Conveyancing Confirmation page - ${cta} card`}
                      linkType={'LinkToPage'}
                      name={cta}
                    >
                      <Paper radius="md" p="md" h="100%" className={classes.anchorCard}>
                        <Flex
                          direction="column"
                          align="flex-start"
                          justify="space-between"
                          h="100%"
                        >
                          <Stack gap="xxxs">
                            <Text fz="lg" fw="600">
                              {title}
                            </Text>
                            <Text fz="sm">{subtitle}</Text>
                          </Stack>
                          <LinkButtonWithTracking
                            className={classes.linkButton}
                            variant="transparent"
                            label={cta}
                            href={link}
                            size="md"
                            ta="left"
                            rightSection={<ArrowForward />}
                            onClick={cleanUp}
                            purpose={`Conveyancing Confirmation page - ${cta} button`}
                          />
                        </Flex>
                      </Paper>
                    </AnchorWithTracking>
                    {type === RESULTS_CTA_TYPE.GET_PRE_APPROVED && (
                      <Text fz="sm" pl="xxs">
                        ^Individual lenders may charge fees to the customer.
                      </Text>
                    )}
                  </Stack>
                ))}
              </SimpleGrid>
            </Stack>
          )}
        </Stack>
      ) : (
        <Stack gap="xs" align="center">
          <AlertWithTracking
            title={'Unable to connect with server'}
            name={'Unable to connect with server'}
            variant={'default'}
            c="orange.6"
            bg="orange.1"
            icon={<ErrorIcon size={18} />}
            style={{ border: '1px solid #fc7426' }}
          >
            <Stack gap="xxs">
              <Text fz="xs">Please refresh and try submitting again.</Text>
              <Text fz="xs">
                If the issue persists, please get in touch with us{' '}
                <AnchorWithTracking
                  href="/get-in-touch/"
                  label="here"
                  size="xs"
                  target="_blank"
                  c="orange.6"
                  underline="always"
                  position={`Conveyancing Confirmation page - failed referral api submission`}
                  linkType={'LinkToPage'}
                  name={'Get in touch here'}
                />{' '}
                or via the Aussie Chat
              </Text>
            </Stack>
          </AlertWithTracking>
        </Stack>
      )}
    </Stack>
  );
}
