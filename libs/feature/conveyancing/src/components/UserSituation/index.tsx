'use client';
import { useState } from 'react';
import { ActionIcon, Divider, Group, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { useForm } from '@mantine/form';

import {
  CONVEYANCING_SERVICE_TYPE,
  CustomerSituation,
  FINANCING_STATUS,
  financingStatusOptions,
  USER_SITUATION,
  userSituationOptions,
} from '@gp/data-access/conveyancing';
import { SEARCH_RESULT_TYPE } from '@gp/data-access/property-hub';
import { ButtonWithTracking, CheckboxCardWithTracking, FormWithTracking } from '@gp/ui/components';
import { ArrowForward, House2, Info, Search, Tick } from '@gp/ui/icons';

import SearchBar from '../SearchBar';

export const FORM_NAME = 'Conveyancing Services';

interface UserSituationProps {
  data: Partial<CustomerSituation>;
  handleSubmit: (data: Partial<CustomerSituation>) => void;
}

export default function UserSituation({ data, handleSubmit }: UserSituationProps) {
  const hasBuying = data.conveyancingService?.includes(
    CONVEYANCING_SERVICE_TYPE.BUYING || CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING
  );

  const validateData = (data: Partial<CustomerSituation>) => {
    return hasBuying
      ? !!data.userSituation && !!data.financingStatus && !!data.location
      : !!data.location;
  };
  const [isFormValid, setIsFormValid] = useState(validateData(data));

  const form = useForm({
    mode: 'controlled',
    initialValues: data,
    onValuesChange: (formData) => {
      setIsFormValid(validateData(formData));
    },
  });

  const formValueUserSituation = form.getInputProps('userSituation').value;
  const formValueFinancingStatus = form.getInputProps('financingStatus').value;

  return (
    <Stack gap="lg" p={{ base: 'sm', sm: 'lg' }} bg="white">
      <Title order={1} size="h2" c="primary" ta="center">
        Conveyancing service finder
      </Title>
      <FormWithTracking name={FORM_NAME} form={form} handleSubmit={handleSubmit}>
        <Stack gap="lg">
          {hasBuying && (
            <>
              <Stack>
                <Stack gap="xxs">
                  <Title order={4} fz="lg">
                    Tell us about your buying situation
                  </Title>
                  <Text fz="sm">This helps us provide the right support for your journey</Text>
                </Stack>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                  {userSituationOptions.map(({ value, label, subtitle }) => (
                    <CheckboxCardWithTracking
                      showCheckbox={false}
                      key={value}
                      p="xs"
                      radius={0}
                      bg={value === formValueUserSituation ? 'grape.1' : 'white'}
                      style={{
                        border:
                          value === formValueUserSituation
                            ? '1px solid var(--mantine-color-grape-8)'
                            : '1px solid var(--mantine-color-gray-2)',
                      }}
                      label={label}
                      subtitle={subtitle}
                      icon={
                        <ActionIcon
                          variant={value === formValueUserSituation ? 'filled' : 'subtle'}
                          bg={value === formValueUserSituation ? 'grape.8' : 'gray.2'}
                          size="xl"
                        >
                          {value === USER_SITUATION.STILL_SEARCHING ? (
                            <Search width={24} height={24} />
                          ) : (
                            <House2
                              stroke={value === formValueUserSituation ? 'white' : '#4b1f68'}
                              width={24}
                              height={24}
                            />
                          )}
                        </ActionIcon>
                      }
                      onClick={() => {
                        form.setValues({ userSituation: value });
                      }}
                      position={`User situation ${value.toLocaleLowerCase()} checkbox`}
                    />
                  ))}
                </SimpleGrid>
              </Stack>
              {!!formValueUserSituation && (
                <Stack>
                  <Divider my="xs" />
                  <Title order={4} fz="lg">
                    Property details
                  </Title>
                  <SearchBar
                    searchType={
                      formValueUserSituation === USER_SITUATION.STILL_SEARCHING
                        ? SEARCH_RESULT_TYPE.SUBURB
                        : SEARCH_RESULT_TYPE.ADDRESS
                    }
                    onSelect={(item: string) => form.setValues({ location: item })}
                    allowManualInput={formValueUserSituation === USER_SITUATION.FOUND_PROPERTY}
                  />
                </Stack>
              )}
              <Stack>
                <Divider my="xs" />
                <Stack gap="xxs">
                  <Title order={4} fz="lg">
                    Financing status
                  </Title>
                  <Text fz="sm">Let us know your current financing status</Text>
                </Stack>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                  {financingStatusOptions.map(({ value, label, subtitle }) => (
                    <CheckboxCardWithTracking
                      showCheckbox={false}
                      key={value}
                      p="xs"
                      radius={0}
                      bg={value === formValueFinancingStatus ? 'grape.1' : 'white'}
                      style={{
                        border:
                          value === formValueFinancingStatus
                            ? '1px solid var(--mantine-color-grape-8)'
                            : '1px solid var(--mantine-color-gray-2)',
                      }}
                      label={label}
                      subtitle={subtitle}
                      icon={
                        <ActionIcon
                          variant={value === formValueFinancingStatus ? 'filled' : 'subtle'}
                          bg={value === formValueFinancingStatus ? 'grape.8' : 'gray.2'}
                          size="xl"
                        >
                          {value === FINANCING_STATUS.PRE_APPROVED ? (
                            <Tick width={24} height={24} />
                          ) : (
                            <Info width={24} height={24} />
                          )}
                        </ActionIcon>
                      }
                      onClick={() => {
                        form.setValues({ financingStatus: value });
                      }}
                      position={`Financing status ${value.toLocaleLowerCase()} checkbox`}
                    />
                  ))}
                </SimpleGrid>
              </Stack>
            </>
          )}
          {!hasBuying && (
            <Stack>
              <Title order={4} fz="lg">
                Property details
              </Title>
              <SearchBar
                searchType={SEARCH_RESULT_TYPE.ADDRESS}
                onSelect={(item: string) => form.setValues({ location: item })}
                allowManualInput
              />
            </Stack>
          )}
          <Group justify="flex-end">
            <ButtonWithTracking
              type="submit"
              rightSection={<ArrowForward />}
              miw={200}
              disabled={!isFormValid}
              label="Get results"
              position={FORM_NAME}
            />
          </Group>
        </Stack>
      </FormWithTracking>
    </Stack>
  );
}
