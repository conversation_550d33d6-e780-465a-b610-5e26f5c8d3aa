'use client';
import {
  <PERSON><PERSON>,
  Con<PERSON>er,
  Di<PERSON>r,
  <PERSON>lex,
  <PERSON><PERSON>,
  TabsList,
  TabsPanel,
  TabsTab,
  Text,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import type { Property, Suburb } from '@gp/data-access/property-hub';
import { Collapse, SectionHeader, TabsWithTracking } from '@gp/ui/components';

import EducationCard from '../EducationCard';

export default function EducationV2(props: Suburb | Property) {
  const isProperty = 'suburb' in props;
  const { primarySchools, secondarySchools, universities } = isProperty
    ? props.suburb.data
    : props.data;

  const [opened, { toggle, close, open }] = useDisclosure(true);
  const name = isProperty
    ? props.address
    : `${props.council ? `- ${props.council?.name}` : ''}, ${props.state.name}`;
  const pageType = isProperty ? 'Property' : 'Suburb';

  const hasPrimarySchoolData = primarySchools.length > 0;
  const hasSecondarySchoolData = secondarySchools.length > 0;
  const hasUniversityData = universities.length > 0;

  const defaultActiveTab = hasPrimarySchoolData
    ? 'primary'
    : hasSecondarySchoolData
    ? 'secondary'
    : 'university';

  if (!hasPrimarySchoolData && !hasSecondarySchoolData && !hasUniversityData) {
    return null;
  }

  // Sort schools before rendering (types will be inferred automatically)
  // based on the property's catchment list. If not for a property,
  // the schools will be returned as is.
  const sortSchools = <T extends { name: string }>(schools: T[]): T[] => {
    if (!isProperty || !props.data.schools) return schools;

    return [...schools].sort((a, b) => {
      const aInCatchment = props.data.schools?.includes(a.name) ? 0 : 1;
      const bInCatchment = props.data.schools?.includes(b.name) ? 0 : 1;
      return aInCatchment - bInCatchment;
    });
  };

  const sortedPrimarySchools = sortSchools(primarySchools);
  const sortedSecondarySchools = sortSchools(secondarySchools);

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            order={3}
            size="h4"
            title="Nearby education"
            description="Schooling and educational options in the area, for all ages."
            info={
              <Stack gap="md" p="xs">
                <Text>This section shows you the educational options local to this area.</Text>
                <Text>
                  This may help you assess convenience and daily travel to school as your family
                  needs change over time, as well as how many options you may have to choose from,
                  to find educational options that fit your preferences.
                </Text>
                {isProperty && (
                  <>
                    <Badge size="xs" variant="light-green">
                      In catchment
                    </Badge>
                    <Text>
                      This 'In catchment' badge marks the public school enrolment areas this
                      property falls within.
                    </Text>
                    <Text>
                      Public schools have defined catchment areas where children are able to enrol
                      based on their residential address, this is because public schools reserve
                      places for students in their local enrolment area.{' '}
                    </Text>
                    <Text>
                      Catchment areas may have different names like school zones, intake areas or
                      enrolment areas, depending on which state you are in.
                    </Text>
                  </>
                )}
              </Stack>
            }
          />
          <TabsWithTracking
            defaultValue={defaultActiveTab}
            onChange={() => {
              close();
            }}
            position="Schools in Area"
            name={
              isProperty
                ? `Property - ${props.address}, ${props.suburb.name}, ${props.suburb.state.name}`
                : `${props.name} - ${props.council?.name}, ${props.state.name}`
            }
            type={pageType}
          >
            <TabsList>
              {hasPrimarySchoolData && (
                <TabsTab value="primary" data-testid="primary-school-toggle-in-education">
                  Primary
                </TabsTab>
              )}
              {hasSecondarySchoolData && (
                <TabsTab value="secondary" data-testid="secondary-school-toggle-in-education">
                  Secondary
                </TabsTab>
              )}
              {hasUniversityData && (
                <TabsTab value="university" data-testid="university-toggle-in-education">
                  University
                </TabsTab>
              )}
            </TabsList>
            <TabsPanel value="primary" px="sm" mt="md">
              <Stack gap="sm">
                {sortedPrimarySchools.slice(0, 3).map((data, index) => {
                  const isInCatchment = isProperty && props.data.schools?.includes(data.name);
                  return (
                    <Stack key={data.name} gap="sm">
                      <EducationCard
                        data={{
                          name: data.name,
                          years: data.years,
                          type: data.type || '',
                        }}
                        isInCatchment={isInCatchment}
                      />
                      {index === 2 ||
                        (index < sortedPrimarySchools.length - 1 && (
                          <Divider orientation="horizontal" />
                        ))}
                    </Stack>
                  );
                })}
                {sortedPrimarySchools.length > 3 && (
                  <Collapse
                    in={false}
                    expandText={'See more'}
                    collapseText="See less"
                    data-testid="schools-list-expand-collapse"
                    type={pageType}
                    name={name}
                    disclosure={[opened, { toggle, close, open }]}
                  >
                    {sortedPrimarySchools.slice(3).map((data, index) => {
                      const isInCatchment = isProperty && props.data.schools?.includes(data.name);
                      return (
                        <Stack key={data.name} gap="sm" pb="sm">
                          {index < sortedPrimarySchools.slice(3).length && (
                            <Divider orientation="horizontal" />
                          )}
                          <EducationCard
                            key={data.name}
                            data={{
                              name: data.name,
                              years: data.years,
                              type: data.type || '',
                            }}
                            isInCatchment={isInCatchment}
                          />
                        </Stack>
                      );
                    })}
                  </Collapse>
                )}
              </Stack>
            </TabsPanel>
            <TabsPanel value="secondary" px="sm" mt="md">
              <Stack gap="sm">
                {sortedSecondarySchools.slice(0, 3).map((data, index) => {
                  const isInCatchment = isProperty && props.data.schools?.includes(data.name);
                  return (
                    <Stack key={data.name} gap="sm">
                      <EducationCard
                        data={{
                          name: data.name,
                          years: data.years,
                          type: data.type || '',
                        }}
                        isInCatchment={isInCatchment}
                      />
                      {index === 2 ||
                        (index < sortedSecondarySchools.length - 1 && (
                          <Divider orientation="horizontal" />
                        ))}
                    </Stack>
                  );
                })}
                {sortedSecondarySchools.length > 3 && (
                  <Collapse
                    in={false}
                    expandText={'See more'}
                    collapseText="See less"
                    data-testid="schools-list-expand-collapse"
                    type={pageType}
                    name={name}
                    disclosure={[opened, { toggle, close, open }]}
                  >
                    {sortedSecondarySchools.slice(3).map((data, index) => {
                      const isInCatchment = isProperty && props.data.schools?.includes(data.name);
                      return (
                        <Stack key={data.name} gap="sm" pb="sm">
                          {index < sortedSecondarySchools.slice(3).length && (
                            <Divider orientation="horizontal" />
                          )}
                          <EducationCard
                            key={data.name}
                            data={{
                              name: data.name,
                              years: data.years,
                              type: data.type || '',
                            }}
                            isInCatchment={isInCatchment}
                          />
                        </Stack>
                      );
                    })}
                  </Collapse>
                )}
              </Stack>
            </TabsPanel>
            <TabsPanel value="university" px="sm" mt="md">
              <Stack gap="sm">
                {universities.slice(0, 3).map((data, index) => (
                  <Stack key={data.name} gap="sm">
                    <Text fw={700}>{data.name}</Text>
                    {index === 2 ||
                      (index < universities.length - 1 && <Divider orientation="horizontal" />)}
                  </Stack>
                ))}
                {universities.length > 3 && (
                  <Collapse
                    in={false}
                    expandText={'See more'}
                    collapseText="See less"
                    data-testid="schools-list-expand-collapse"
                    type={pageType}
                    name={name}
                    disclosure={[opened, { toggle, close, open }]}
                  >
                    {universities.slice(3).map((data, index) => (
                      <Stack key={data.name} gap="sm" pb="sm">
                        {index < universities.slice(3).length && (
                          <Divider orientation="horizontal" />
                        )}
                        <Text fw={700}>{data.name}</Text>
                      </Stack>
                    ))}
                  </Collapse>
                )}
              </Stack>
            </TabsPanel>
          </TabsWithTracking>
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
