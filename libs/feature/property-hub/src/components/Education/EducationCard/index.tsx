import { Badge, Divider, Group, Stack, Text } from '@mantine/core';

export default function EducationCard({
  data,
  isInCatchment,
}: {
  data: {
    name: string;
    years: string;
    type: string;
  };
  isInCatchment: boolean;
}) {
  const { name, years, type } = data;
  return (
    <Stack gap="sm">
      <Stack gap="xxs">
        <Text fw={700}>{name}</Text>
        <Group gap="sm">
          <Text>{years}</Text>
          {type && (
            <>
              <Divider orientation="vertical" />
              <Text>{type}</Text>
            </>
          )}
          {isInCatchment && (
            <Badge size="xs" variant="light-green">
              In catchment
            </Badge>
          )}
        </Group>
      </Stack>
    </Stack>
  );
}
