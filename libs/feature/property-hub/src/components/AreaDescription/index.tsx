'use client';

import { Box, Container, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useFlags } from 'launchdarkly-react-client-sdk';

import { Council, State, Suburb } from '@gp/data-access/property-hub';
import { Collapse, SectionHeader } from '@gp/ui/components';
import { formatNumber } from '@gp/util/intl';

import { getDemoData } from '../AreaOverview';

import classes from '../PropertyDescription/style.module.css';

export default function AreaDescription(props: State | Council | Suburb) {
  const { gpAreaDescription } = useFlags();
  const isUniversity = props.data.universities.length > 0;
  const name = props.name;
  const [opened, { toggle, close, open }] = useDisclosure(true);
  const top3Jobs = `${props.data.occupations
    .slice(0, 3)
    .map((occ, i) => (i === 2 ? `and ${occ[0].toLowerCase()}` : occ[0].toLowerCase()))
    .join(', ')}`;

  const {
    population,
    mostCommonHousehold,
    sortedIncomeGroups,
    mostCommonPropertyType,
    mostCommonOccupant,
  } = getDemoData(props);

  const educationDescription = () => {
    return `
      ${
        props.data.primarySchools.length !== 0 && props.data.secondarySchools.length !== 0
          ? `${name} also has a number of primary and secondary schools ${
              isUniversity ? 'as well as universities ' : ''
            }nearby, including ${props.data.primarySchools[0].name} (primary)${
              isUniversity ? ', ' : ' and '
            } ${props.data.secondarySchools[0].name} (secondary)${
              isUniversity ? ` and ${props.data.universities[0].name}` : ''
            }. `
          : ''
      }
      ${
        props.data.primarySchools.length === 1 && props.data.secondarySchools.length === 0
          ? `Schooling and education options in ${name} include ${props.data.primarySchools[0].name}, catering for K-Y6.`
          : ''
      }
      ${
        props.data.primarySchools.length === 0 && props.data.secondarySchools.length === 1
          ? `Schooling and education options in ${name} include ${props.data.secondarySchools[0].name}, catering for Yr 7 - Yr 12.`
          : ''
      }
      ${
        props.data.primarySchools.length > 1 && props.data.secondarySchools.length === 0
          ? `${name} also has a number of primary schools nearby, including ${props.data.primarySchools[0].name} and ${props.data.primarySchools[1].name}. `
          : ''
      }
      ${
        props.data.primarySchools.length === 0 && props.data.secondarySchools.length > 1
          ? `${name} also has a number of secondary schools nearby, including ${props.data.secondarySchools[0].name} and ${props.data.secondarySchools[1].name}. `
          : ''
      }
    `;
  };
  const saleDescription = () => {
    return (
      <Text>
        {props.data.propertiesAvailableLastMonth && props.data.soldLast12Months
          ? `Last month ${name} had ${formatNumber(
              props.data.propertiesAvailableLastMonth
            )} properties on sale, with ${formatNumber(
              props.data.soldLast12Months
            )} properties being sold in the past 12 months. `
          : null}
        {props.data.houseMedianPrice && props.data.housePriceChangeLast12Months
          ? `The median property sale price over the last 12 months for houses has been $${formatNumber(
              props.data.houseMedianPrice
            )}, which has ${
              props.data.housePriceChangeLast12Months >= 0 ? 'increased' : 'decreased'
            } by ${Math.abs(props.data.housePriceChangeLast12Months)}%. `
          : null}
        {props.data.unitMedianPrice && props.data.unitPriceChangeLast12Months
          ? `The median sale price for units has been around $${formatNumber(
              props.data.unitMedianPrice
            )} and has ${
              props.data.unitPriceChangeLast12Months >= 0 ? 'increased' : 'decreased'
            } by ${Math.abs(props.data.unitPriceChangeLast12Months)}%. `
          : null}
        {props.data.houseMedianRent && props.data.houseRentChangeLast12Months
          ? `If you are looking to rent or are interested in purchasing an investment property, the median weekly rent for a house in ${name} is around $${formatNumber(
              props.data.houseMedianRent
            )} and has ${
              props.data.houseRentChangeLast12Months >= 0 ? 'increased' : 'decreased'
            } by ${Math.abs(props.data.houseRentChangeLast12Months)}% over the past 12 months. `
          : null}
        {props.data.unitMedianRent && props.data.unitRentChangeLast12Months
          ? `Meanwhile, the median weekly rent for a unit is around $${formatNumber(
              props.data.unitMedianRent
            )} and has ${
              props.data.unitRentChangeLast12Months >= 0 ? 'increased' : 'decreased'
            } by ${Math.abs(props.data.unitRentChangeLast12Months)}%. `
          : null}
      </Text>
    );
  };
  const suburbDescription = () => {
    return (
      <Stack gap="17">
        {saleDescription()}
        <Text>
          {population && mostCommonHousehold && mostCommonPropertyType && mostCommonOccupant
            ? `There are around ${formatNumber(
                population
              )} residents living in ${name}. They are predominantly made up of ${mostCommonHousehold.toLowerCase()}, living in ${mostCommonPropertyType.toLowerCase()} with the majority being ${mostCommonOccupant.toLowerCase()}. `
            : null}
          {props.data.occupations?.length > 0
            ? `The most common occupations for locals are ${top3Jobs}. `
            : null}
          {educationDescription()}
        </Text>
      </Stack>
    );
  };
  const stateDescription = () => {
    return (
      <Stack gap="17">
        <Text>
          {population && mostCommonHousehold && mostCommonPropertyType
            ? `There are around ${formatNumber(
                population
              )} people living in ${name}, most residents are ${mostCommonHousehold.toLowerCase()}, predominantly living in ${mostCommonPropertyType.toLowerCase()}. `
            : null}
          {props.data.occupations?.length > 0 && sortedIncomeGroups.length !== 0
            ? `The most common occupations for locals are ${top3Jobs}, and the average income band for majority of residents sits between ${sortedIncomeGroups[0].name.toLowerCase()}. `
            : null}
        </Text>
        {saleDescription()}
      </Stack>
    );
  };

  return (
    gpAreaDescription && (
      <Container fluid color="white" p={{ base: 'md', md: 'lg' }}>
        <Stack gap="md">
          <SectionHeader
            title={`${'state' in props ? 'Suburb' : 'State'} snapshot`}
            order={3}
            size="h4"
            description=""
          />
          <Box>
            {!opened && (
              <Box
                h="72px"
                style={{
                  WebkitLineClamp: 3,
                }}
                className={classes.clamp}
              >
                {'state' in props ? suburbDescription() : stateDescription()}
              </Box>
            )}
            <Collapse
              in={false}
              expandText="See more"
              collapseText="See less"
              data-testid="area-description-expand-collapse"
              type={'state' in props ? 'Suburb' : 'State'}
              name={`${'state' in props ? 'Suburb' : 'State'} - ${name}`}
              position="Area Description"
              disclosure={[opened, { toggle, close, open }]}
            >
              {'state' in props ? suburbDescription() : stateDescription()}
            </Collapse>
          </Box>
        </Stack>
      </Container>
    )
  );
}
