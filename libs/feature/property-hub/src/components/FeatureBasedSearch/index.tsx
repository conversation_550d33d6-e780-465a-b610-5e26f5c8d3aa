'use client';
import {
  <PERSON><PERSON><PERSON>,
  Center,
  Flex,
  Stack,
  TabsList,
  TabsPanel,
  TabsTab,
  Text,
  Title,
} from '@mantine/core';
import { useSessionStorage } from '@mantine/hooks';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { useEffect, useMemo, useState } from 'react';

import { SEARCH_RESULT_TYPE } from '@gp/data-access/property-hub';
import { TabsWithTracking } from '@gp/ui/components';

import AreaSearchBar from '../AreaSearchBar';
import { Option } from '../ListingSearchBar';
import SearchBarWithHistory from '../ListingSearchBar/SearchBarWithHistory';
import { ListingSearchBarV2 } from '../ListingSearchBar/v2';

import classes from './style.module.css';

export default function FeatureBasedSearch({ defaultTab }: { defaultTab: string }) {
  const { gpListingSearchForSold, gpPropertyTabsExperiment } = useFlags();
  const [selectedSoldSuburbs, setSelectedSoldSuburbs] = useState<Option[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(defaultTab ? defaultTab : 'buy');

  //Tab Labels Experiment. Three states: "variant", "control", or undefined/off (defaults to control).
  const isVariant = gpPropertyTabsExperiment === 'variant';
  const variantId = isVariant ? 'variant' : 'control';

  const tabLabels = useMemo(
    () =>
      isVariant
        ? { buy: 'For Sale', sold: 'Sold', address: 'Property Report', suburb: 'Suburb Report' }
        : { buy: 'Buy', sold: 'Sold', address: 'Address', suburb: 'Suburb' },
    [isVariant]
  );

  // reset exclusive listing filter when user goes back to property page
  const [, , removeShowExclusiveListingsFilter] = useSessionStorage({
    key: 'GP_SHOW_EXCLUSIVE_LISTING_FILTER',
  });
  useEffect(() => {
    removeShowExclusiveListingsFilter();
  }, [removeShowExclusiveListingsFilter]);

  return (
    <Flex className={classes.heroContainer} py={{ base: 0, md: 'xxl' }}>
      <BackgroundImage
        pos={'relative'}
        bottom={0}
        left={0}
        src="/growth-product-assets/property-hub/property-search-hero.avif"
        className={classes.heroBackground}
      >
        <Center p={{ base: 'sm', sm: 'md', md: 'lg' }} h="100%">
          <Stack gap="sm" w="100%" maw={848} mx={{ base: 'sm', md: 0 }}>
            <Title order={1} size="h2" ta="center" c="white" fw={700}>
              Search properties, explore suburbs.
            </Title>
            <Text size="xl" ta="center" c="white" fw={700}>
              Find your next home with Aussie.
            </Text>
            <TabsWithTracking
              defaultValue={defaultTab ? defaultTab : 'buy'}
              classNames={{
                root: classes.tabRoot,
                list: classes.tabList,
              }}
              trackingProps={{
                variantId,
                tabNameDisplayed: tabLabels?.[activeTab],
                tabSelected: tabLabels?.[activeTab],
              }}
              onChange={setActiveTab}
            >
              <TabsList classNames={{ list: classes.list }}>
                <TabsTab
                  value="buy"
                  data-testid="buy-toggle-in-market-trend"
                  w={{ base: gpListingSearchForSold ? '20%' : '30%', xs: 130 }}
                  h={52}
                  classNames={{ tab: classes.tabTab, tabLabel: classes.tabLabel }}
                >
                  {tabLabels.buy}
                </TabsTab>
                {gpListingSearchForSold && (
                  <TabsTab
                    value="sold"
                    data-testid="research-toggle-in-market-trend"
                    w={{ base: '20%', xs: 130 }}
                    h={52}
                    classNames={{ tab: classes.tabTab, tabLabel: classes.tabLabel }}
                  >
                    {tabLabels.sold}
                  </TabsTab>
                )}
                <TabsTab
                  value="address"
                  data-testid="research-toggle-in-market-trend"
                  w={{ base: gpListingSearchForSold ? '20%' : '30%', xs: 130 }}
                  h={52}
                  classNames={{ tab: classes.tabTab, tabLabel: classes.tabLabel }}
                >
                  {tabLabels.address}
                </TabsTab>
                <TabsTab
                  value="suburb"
                  data-testid="research-toggle-in-market-trend"
                  w={{ base: gpListingSearchForSold ? '20%' : '30%', xs: 130 }}
                  h={52}
                  classNames={{ tab: classes.tabTab, tabLabel: classes.tabLabel }}
                >
                  {tabLabels.suburb}
                </TabsTab>
              </TabsList>
              <TabsPanel value="buy" py="sm" px="md" mt={0}>
                <ListingSearchBarV2 />
              </TabsPanel>
              {gpListingSearchForSold && (
                <TabsPanel value="sold" py="sm" px="md" mt={0}>
                  <SearchBarWithHistory
                    onChange={(v) => setSelectedSoldSuburbs(v)}
                    controlledValue={selectedSoldSuburbs} //need to update submit action for selected suburbs when SOLD api is ready
                  />
                </TabsPanel>
              )}
              <TabsPanel py="sm" px="md" mt={0} value="address">
                <AreaSearchBar withBetaBadge={false} searchType={SEARCH_RESULT_TYPE.ADDRESS} />
              </TabsPanel>
              <TabsPanel py="sm" px="md" mt={0} value="suburb">
                <AreaSearchBar withBetaBadge={false} searchType={SEARCH_RESULT_TYPE.SUBURB} />
              </TabsPanel>
            </TabsWithTracking>
          </Stack>
        </Center>
      </BackgroundImage>
    </Flex>
  );
}
