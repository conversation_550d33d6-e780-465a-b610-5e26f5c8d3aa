'use client';

import { CarouselSlide } from '@mantine/carousel';
import { Box, Group, Stack, Text } from '@mantine/core';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { useRouter } from 'next/navigation';

import { SEARCH_CATEGORY } from '@gp/data-access/property-hub';
import { Carousel, SectionHeader } from '@gp/ui/components';

import { useSearchHistory } from '../../hooks/useSearchHistory';

import classes from './style.module.css';

export default function RecentSearches() {
  const { gpSavedSearchHistory } = useFlags();
  const { searchHistory } = useSearchHistory();
  if (searchHistory.length === 0) return null;
  return gpSavedSearchHistory ? (
    <Stack gap="sm">
      <SectionHeader order={1} fz={19.25} fw={700} title="Recent Searches" />
      <Carousel
        withIndicators
        variant="default"
        controlPosition="bottom"
        slideGap="sm"
        slideSize={{ xs: 65, sm: 46, md: 33, lg: 25 }}
      >
        {searchHistory.map((search, idx) => (
          <CarouselSlide key={idx}>
            <RecentSearchCard
              title={search.label || ''}
              queryString={search.queryString || ''}
              category={search.searchCategory || ''}
            />
          </CarouselSlide>
        ))}
      </Carousel>
    </Stack>
  ) : null;
}

const RecentSearchCard = ({
  title,
  queryString,
  category,
}: {
  title: string;
  queryString: string;
  category: string;
}) => {
  const router = useRouter();
  const params = new URLSearchParams(queryString);
  const parts: string[] = [];
  const parseNumber = (val: string | null): number | null => (val ? Number(val) : null);

  const formatMoney = (num: number): string => {
    if (num >= 1_000_000) return `$${(num / 1_000_000).toFixed(1)}m`;
    if (num >= 1_000) return `$${(num / 1_000).toFixed(0)}k`;
    return `$${num}`;
  };

  const formatRange = (
    min: number | null,
    max: number | null,
    label: string,
    labelPlural = ''
  ): string | null => {
    if (!labelPlural) {
      labelPlural = label;
    }
    if (min && max && min === max) return `${min} ${min > 1 ? labelPlural : label}`;
    if (min && max) return `${min}–${max} ${labelPlural}`;
    if (min) return `${min}+ ${min > 1 ? labelPlural : label}`;
    if (max) return `Up to ${max} ${max > 1 ? labelPlural : label}`;
    return null;
  };

  // Price
  const minPrice = parseNumber(params.get('minPrice'));
  const maxPrice = parseNumber(params.get('maxPrice'));
  if (minPrice !== null && maxPrice !== null) {
    parts.push(`${formatMoney(minPrice)} – ${formatMoney(maxPrice)}`);
  } else if (minPrice !== null) {
    parts.push(`From ${formatMoney(minPrice)}`);
  } else if (maxPrice !== null) {
    parts.push(`Up to ${formatMoney(maxPrice)}`);
  }

  // Bedrooms
  const bedrooms = formatRange(
    parseNumber(params.get('minBedrooms')),
    parseNumber(params.get('maxBedrooms')),
    'bedroom',
    'bedrooms'
  );
  if (bedrooms) parts.push(bedrooms);

  // Bathrooms
  const bathrooms = formatRange(
    parseNumber(params.get('minBathrooms')),
    parseNumber(params.get('maxBathrooms')),
    'bathroom',
    'bathrooms'
  );
  if (bathrooms) parts.push(bathrooms);

  // Car spaces
  const carSpaces = formatRange(
    parseNumber(params.get('minCarSpaces')),
    parseNumber(params.get('maxCarSpaces')),
    'car space',
    'car spaces'
  );
  if (carSpaces) parts.push(carSpaces);

  // Land size
  const landSize = formatRange(
    parseNumber(params.get('minLandSize')),
    parseNumber(params.get('maxLandSize')),
    'm²'
  );
  if (landSize) parts.push(landSize);

  const formattedQuery = parts.join(', ');
  const type =
    category !== SEARCH_CATEGORY.BUY && category !== SEARCH_CATEGORY.SOLD
      ? category === SEARCH_CATEGORY.RESEARCH
        ? 'Address'
        : category
      : params.get('type') || null;

  // Determine the navigation URL based on the search category and queryString
  const handleClick = () => {
    if (category === SEARCH_CATEGORY.BUY || category === SEARCH_CATEGORY.SOLD) {
      router.push(`/property/results/?${queryString}`);
    } else {
      router.push(`/property/${queryString}`);
    }
  };

  return (
    <Box
      bg="white"
      p={{ base: 'sm' }}
      style={{
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'box-shadow 0.2s ease, transform 0.2s ease',
      }}
      className={classes['recent-search-card']}
      w="324px"
      h={128}
      onClick={handleClick}
    >
      <Stack gap={8} justify="space-between" h="100%">
        <Text
          fz={19.25}
          fw={700}
          style={{
            textOverflow: 'ellipsis',
            textWrap: 'nowrap',
            wordBreak: 'break-all',
            overflow: 'hidden',
          }}
        >
          {title}
        </Text>
        <Text
          fz={15.75}
          fw={400}
          style={{
            textOverflow: 'ellipsis',
            textWrap: 'nowrap',
            wordBreak: 'break-all',
            overflow: 'hidden',
          }}
        >
          {category !== SEARCH_CATEGORY.RESEARCH && formattedQuery}
        </Text>
        <Group gap={8} justify="start">
          {category && (
            <Text
              fz={12.25}
              fw={700}
              px={12}
              py={4}
              bg="grape.2"
              style={{ borderRadius: '8px', textTransform: 'capitalize' }}
            >
              {category === SEARCH_CATEGORY.SUBURB ||
              category === SEARCH_CATEGORY.COUNCIL ||
              category === SEARCH_CATEGORY.STATE ||
              category === SEARCH_CATEGORY.ADDRESS ||
              category === SEARCH_CATEGORY.RESEARCH
                ? SEARCH_CATEGORY.RESEARCH
                : category === SEARCH_CATEGORY.SOLD
                ? SEARCH_CATEGORY.SOLD
                : SEARCH_CATEGORY.BUY}
            </Text>
          )}
          {type && (
            <Text
              fz={12.25}
              fw={700}
              px={12}
              py={4}
              bg="gray.1"
              style={{ borderRadius: '8px', textTransform: 'capitalize' }}
            >
              {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
            </Text>
          )}
        </Group>
      </Stack>
    </Box>
  );
};
