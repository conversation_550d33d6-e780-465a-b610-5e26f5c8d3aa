import { Container, Divider, Flex, Stack, Text, Title } from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import { AppDownloadButtons, Image } from '@gp/ui/components';

export default function AppDownloadPanelV2(props: Council | Property | State | Suburb) {
  const position =
    'address' in props
      ? 'Property Page'
      : 'councils' in props
      ? 'State Page'
      : 'suburbs' in props
      ? 'Council Page'
      : 'Suburb Page';

  const appleStoreLink = new URL('https://apps.apple.com/app/apple-store/id6451372662');
  appleStoreLink.searchParams.set('pt', '125732560');
  appleStoreLink.searchParams.set(
    'ct',
    position === 'Property Page' ? 'property_report_page' : 'area_report_page'
  );
  appleStoreLink.searchParams.set('mt', '8');

  const googlePlayLink = new URL('https://play.google.com/store/apps/details?id=au.com.aussie.app');
  googlePlayLink.searchParams.set(
    'referrer',
    new URLSearchParams({
      utm_source: 'website',
      utm_medium: 'landing_page',
      utm_campaign: position === 'Property Page' ? 'property_report_page' : 'area_report_page',
      utm_content: position === 'Property Page' ? 'property_report' : 'area_report',
    }).toString()
  );

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex
          direction={{ base: 'column' }}
          align={{ base: 'flex-start' }}
          justify={{ base: 'stretch' }}
          maw={960}
          gap={{ base: 'md' }}
        >
          <Flex gap={{ base: 'sm', md: 'md' }} direction="row" align="center">
            <Image
              src="property-hub/app-download-panel-image.svg"
              alt="Aussie mobile app icon"
              radius={6}
            />
            <Stack gap="xxs" miw={{ base: undefined, lg: 'max-content' }}>
              <Title order={3} fw={700} fz={{ base: 'md', md: 'lg' }} lh="xs">
                Get the Aussie app
              </Title>
              <Text size="sm" w="fit-content">
                Unlock property insights, track your equity and maximise home ownership with Aussie.
              </Text>
            </Stack>
          </Flex>
          <AppDownloadButtons
            appleStoreLink={appleStoreLink.toString()}
            googlePlayLink={googlePlayLink.toString()}
            position={position}
            gap="xs"
            rowGap="xxs"
            columnGap="sm"
            direction={{ base: 'row' }}
            buttonWidth={130}
          />
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
