'use client';

import { Container, Divider, Flex, Paper, Stack, Text, Title } from '@mantine/core';

import type { Council, Property, Suburb } from '@gp/data-access/property-hub';
import { AnchorWithTracking, SectionHeader } from '@gp/ui/components';

export default function WiderViewV2(props: Council | Suburb | Property) {
  const isProperty = 'address' in props;
  const state = isProperty ? props.suburb.state : props.state;
  const council = isProperty
    ? props.suburb.council
    : 'surroundingSuburbs' in props
    ? props.council
    : null;
  const suburb = isProperty ? props.suburb : null;
  const areaName = 'suburb' in props ? props.suburb.name : props.name;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            order={3}
            size="h4"
            title="Get a wider view"
            description={`Widen your research to learn about the ${[
              suburb && 'suburb',
              council && 'council',
              'state',
            ]
              .filter(Boolean)
              .join(', ')
              .replace(/,([^,]*)$/, ' or$1')} that ${
              isProperty ? 'this property' : areaName
            } belongs to.`}
            data-testid="wider-view-header"
          />
          <Flex
            direction={{ base: 'column', sm: 'row' }}
            gap={{ base: 'sm', md: 'lg' }}
            wrap="wrap"
          >
            {!!suburb && (
              <Paper w={{ base: '100%', sm: 295 }} p="md">
                <AnchorWithTracking
                  label={suburb.name}
                  useNextLink
                  href={`/property/${suburb.slug}/`}
                  data-testid={`wider-view-${suburb.slug.replace(/\//g, '-')}`}
                  position="Get a wider view"
                  name={`Property - ${suburb.name}`}
                  linkType={'LinkToSurroundings'}
                >
                  <Title order={4} size="h5" fw={700}>
                    {suburb.name}
                  </Title>
                </AnchorWithTracking>
                <Text>View the suburb data</Text>
              </Paper>
            )}
            {!!council && (
              <Paper w={{ base: '100%', sm: 295 }} p="md">
                <AnchorWithTracking
                  label={council.name}
                  useNextLink
                  href={`/property/${council.slug}/`}
                  data-testid={`wider-view-${council.slug.replace(/\//g, '-')}`}
                  position="Get a wider view"
                  name={`Property - ${council.name}`}
                  linkType={'LinkToSurroundings'}
                >
                  <Title order={4} size="h5" fw={700}>
                    {council.name}
                  </Title>
                </AnchorWithTracking>
                <Text>View the council data</Text>
              </Paper>
            )}
            <Paper w={{ base: '100%', sm: 295 }} p="md">
              <AnchorWithTracking
                label={state.name}
                useNextLink
                href={`/property/${state.slug}/`}
                data-testid={`wider-view-state-${state.slug}`}
                position="Get a wider view"
                name={`Property - ${state.name}`}
                linkType={'LinkToSurroundings'}
              >
                <Title order={4} size="h5" fw={700}>
                  {state.name}
                </Title>
              </AnchorWithTracking>
              <Text>Get a view of the whole state</Text>
            </Paper>
          </Flex>
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
