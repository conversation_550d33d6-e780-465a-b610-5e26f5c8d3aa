'use client';

import { Container, Divider, Flex, Group, Stack } from '@mantine/core';

import type { Property } from '@gp/data-access/property-hub';
import { LinkButtonWithTracking, SectionHeader } from '@gp/ui/components';
import { InsertDriveFile } from '@gp/ui/icons';

export default function PriceGuideStatementV2(property: Property) {
  const { forSale, informationStatement } = property.data;

  if (!forSale || !informationStatement) return null;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader title="Price guide" order={3} size="h4" />
          <Group>
            <LinkButtonWithTracking
              size="sm"
              leftSection={<InsertDriveFile size={20} />}
              label="Statement of Information"
              variant="secondary"
              fullWidth={false}
              href={informationStatement}
              target="_blank"
              isExternalLink
            />
          </Group>
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
