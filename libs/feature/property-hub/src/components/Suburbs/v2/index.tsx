import { Container, Divider, Flex, SimpleGrid, Stack, Text } from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import { Collapse, SectionHeader } from '@gp/ui/components';

import SuburbCard from '../SuburbCard';

import classes from './style.module.css';

export default function SuburbsV2(props: State | Council | Suburb | Property) {
  const inCouncilOrState = 'suburbs' in props;
  const isProperty = 'address' in props;
  const isCouncil = 'state' in props && 'suburbs' in props;
  const isSuburb = 'state' in props && 'surroundingSuburbs' in props;

  const dataPageType = isProperty
    ? 'Property'
    : isSuburb
    ? 'Suburb'
    : isCouncil
    ? 'Council'
    : 'State';

  const suburbs = (() => {
    let result;
    if ('suburbs' in props) {
      // State or Council
      result = props.suburbs;
    } else if ('surroundingSuburbs' in props) {
      // Suburb
      result = props.surroundingSuburbs;
    } else if ('suburb' in props) {
      // Property
      result = props.suburb.surroundingSuburbs;
    }
    return result?.sort((a, b) => a.name.localeCompare(b.name));
  })();

  if (!suburbs || suburbs.length === 0) {
    return null;
  }

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            order={3}
            size="h4"
            title={
              inCouncilOrState ? `Explore suburbs in ${props.name}` : `Explore surrounding suburbs`
            }
            description={
              inCouncilOrState
                ? `Learn about suburbs in ${props.name} to see if they are suited to your budget or lifestyle.`
                : `Learn about neighbouring suburbs to see if they are better suited to your budget or lifestyle.`
            }
            info={
              inCouncilOrState ? (
                <Stack gap="md" p="xs">
                  <Text>
                    This section shows all suburbs located in <strong>{props.name}</strong>, listed
                    in alphabetical order.
                  </Text>
                  <Text>
                    This may help you identify new areas that align more with your budget as well as
                    comparing the median prices of areas near each other.
                  </Text>
                  <Text>Explore each to identify more potential opportunities.</Text>
                </Stack>
              ) : (
                <Stack gap="md" p="xs">
                  <Text>This section shows you areas close by.</Text>
                  <Text>
                    This may help you identify new areas that align more with your budget as well as
                    comparing the median prices of areas near each other. Get a small view into the
                    wider property market to help you explore more areas and more opportunities.
                  </Text>
                </Stack>
              )
            }
            data-testid="suburbs-list-header"
          />
          <Stack gap="sm">
            <SimpleGrid cols={{ base: 1, md: 2 }} spacing="sm">
              {suburbs.slice(0, 6).map((suburb) => (
                <SuburbCard
                  key={suburb.slug}
                  {...suburb}
                  data-testid={`suburb-${suburb.slug}`}
                  pageType={dataPageType}
                />
              ))}
            </SimpleGrid>
            {suburbs.length > 6 && (
              <Collapse
                in={false}
                expandText={`Show all ${suburbs.length} suburbs`}
                collapseText="Show less suburbs"
                data-testid="suburbs-list-expand-collapse"
                type={dataPageType}
              >
                <SimpleGrid cols={{ base: 1, md: 2 }} spacing="sm">
                  {suburbs.slice(6).map((suburb) => (
                    <SuburbCard
                      key={suburb.slug}
                      {...suburb}
                      data-testid={`suburb-${suburb.slug}`}
                      pageType={dataPageType}
                    />
                  ))}
                </SimpleGrid>
              </Collapse>
            )}
          </Stack>
        </Flex>
        <Divider color="gray.1" className={classes.showOrHideDivider} />
      </Stack>
    </Container>
  );
}
