'use client';

import { Container, Divider, Stack } from '@mantine/core';
import { Status, useSession } from '@lendi/lala-react';

import type { Property } from '@gp/data-access/property-hub';

import GoogleMap from './GoogleMap';

export default function GoogleMapBox(property: Property) {
  const { location } = property;
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;

  if (!isAuthenticated || !location) {
    return null;
  }

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <GoogleMap view="map" location={{ lng: location[0], lat: location[1] }} />
        <Divider color="gray.1" mt={{ base: 'xs', md: 'lg' }} />
      </Stack>
    </Container>
  );
}
