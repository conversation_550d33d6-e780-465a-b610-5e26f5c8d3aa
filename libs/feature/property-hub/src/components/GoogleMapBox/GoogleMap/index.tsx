'use client';

import { useEffect, useRef } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { Box } from '@mantine/core';

import { getEnvVar } from '@gp/util/data-service';

const DEFAULT_ZOOM = 18;

type GoogleMapProps = {
  view: 'map' | 'street';
  location: {
    lat: number;
    lng: number;
  };
};

// This component works in bespoke env. If we decide to use googlemap, we need to configure mapId in the googlemap and recover this component is used..
export default function GoogleMap({ view, location }: GoogleMapProps) {
  const elementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const googleMapApiLoader = new Loader({
      apiKey: getEnvVar('GP_GOOGLE_MAPS_API_KEY'),
      version: 'weekly',
      libraries: ['geometry', 'places', 'visualization'],
    });

    const loadMap = async () => {
      const { Map } = await googleMapApiLoader.importLibrary('maps');
      const { AdvancedMarkerElement } = await googleMapApiLoader.importLibrary('marker');

      const map = new Map(elementRef.current, {
        center: location,
        zoom: DEFAULT_ZOOM,
        mapId: getEnvVar('GP_GOOGLE_MAPS_ID'),
      });
      new AdvancedMarkerElement({ map, position: location });
    };

    const loadStreetView = async () => {
      const { StreetViewPanorama } = await googleMapApiLoader.importLibrary('streetView');
      new StreetViewPanorama(elementRef.current, {
        position: location,
        pov: { heading: 34, pitch: 10 },
      });
    };

    if (view === 'map') {
      loadMap().catch((error) => console.error('Failed to load Google Maps', error));
    } else if (view === 'street') {
      loadStreetView().catch((error) => console.error('Failed to load Street View:', error));
    }
  }, [view, location]);

  return (
    <Box
      ref={elementRef}
      h={{ base: 250, xs: 300, sm: 420 }}
      style={{ borderRadius: '0 0 6px 6px' }}
    />
  );
}
