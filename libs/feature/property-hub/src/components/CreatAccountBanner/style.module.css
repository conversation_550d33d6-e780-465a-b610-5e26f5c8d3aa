.createAccountBanner {
  &:focus-visible,
  &:focus {
    outline: rem(1px) auto var(--mantine-color-grape-9);
    border-color: transparent;
  }
  &__container {
    background-color: var(--mantine-color-grape-2);
    &:focus-visible,
    &:focus-within,
    &:focus {
      outline: rem(1px) auto var(--mantine-color-grape-9);
      border-color: transparent;
    }
  }
  &__mask {
    width: 100%;
    background-color: var(--mantine-color-grape-2);
    transition: clip-path 0.5s ease-out;
    position: relative;
    @media (min-width: $mantine-breakpoint-sm) {
      background-color: var(--mantine-color-grape-9);
      clip-path: ellipse(50% 200% at 20% 29%);
    }
  }
  &:hover,
  &:focus {
    .createAccountBanner__mask {
      clip-path: ellipse(100% 100% at 45% 17%);
    }
    .createAccountBanner__mobile_cta {
      background-color: var(--mantine-color-white);
    }
  }
  &__cta {
    display: none;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    pointer-events: auto;
    &_white {
      background-color: white !important;
      color: var(--mantine-color-grape-9);
      &:hover {
        background-color: white;
      }
    }
    @media (min-width: $mantine-breakpoint-sm) {
      display: block;
    }
  }
  &__mobile_cta {
    display: block;
    background: white;
    border-radius: 24px;
    padding: 12px;
    width: 48px;
    height: 48px;
    @media (min-width: $mantine-breakpoint-sm) {
      display: none;
    }
  }
}
