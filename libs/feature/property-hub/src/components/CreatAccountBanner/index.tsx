'use client';
import { useEffect, useState } from 'react';
import { Box, Flex, Paper, Stack, Text, Title } from '@mantine/core';

import { AnchorWithTracking, ButtonWithTracking } from '@gp/ui/components';
import { ArrowForward } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import classes from './style.module.css';

export default function CreateAccountBanner() {
  const [link, setLink] = useState('');

  useEffect(() => {
    const realLink = embedMarketingTrackingParams(
      `/sign-in/${generateReturnURL([`${window.location.pathname}${window.location.search}`])}`
    );
    setLink(realLink);
  }, []);

  return (
    <Paper style={{ position: 'relative' }}>
      {link && (
        <AnchorWithTracking
          href={link}
          label=""
          style={{ textDecoration: 'none' }}
          className={classes.createAccountBanner}
        >
          <ButtonWithTracking
            className={classes.createAccountBanner__cta}
            label="Create Account"
            mr="lg"
          >
            Create Account
          </ButtonWithTracking>
          <Paper className={classes.createAccountBanner__container}>
            <Paper className={classes.createAccountBanner__mask}>
              <Flex>
                <Stack
                  gap="8"
                  p={{ base: '16px', lg: '16px 32px' }}
                  w={{ base: '100%', sm: '70%' }}
                >
                  <Title size="h4" order={2} c={{ base: '#1D1D1D', sm: '#FFF' }}>
                    Create your account to unlock 25,000 more properties
                  </Title>
                  <Flex>
                    <Text miw="50%" c={{ base: '#1D1D1D', sm: '#FFF' }}>
                      Get access to off-market & pre-market properties exclusive to Aussie;
                      shortlist your favourite homes, and more.
                    </Text>
                    <Box className={classes.createAccountBanner__mobile_cta} ml="xs">
                      <ArrowForward size={24} />
                    </Box>
                  </Flex>
                </Stack>
              </Flex>
              <ButtonWithTracking
                className={`${classes.createAccountBanner__cta} ${classes.createAccountBanner__cta_white}`}
                label="Create Account"
                mr="lg"
              >
                Create Account
              </ButtonWithTracking>
            </Paper>
          </Paper>
        </AnchorWithTracking>
      )}
    </Paper>
  );
}
