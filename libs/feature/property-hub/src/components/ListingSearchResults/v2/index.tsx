'use client';

import { ReactNode, useEffect, useState } from 'react';
import { datadogRum } from '@datadog/browser-rum';
import {
  Anchor,
  Box,
  Breadcrumbs,
  Container,
  Flex,
  Grid,
  Group,
  Pagination,
  Paper,
  Stack,
  Text,
} from '@mantine/core';
import { useSessionStorage } from '@mantine/hooks';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { usePathname, useSearchParams } from 'next/navigation';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import {
  getPropertyHubAPIs,
  parseListingSearchParams,
  PropertiesForSaleSearchResult,
  SearchFilter,
} from '@gp/data-access/property-hub';
import { AnchorWithTracking, Loader, SegmentedControlWithTracking } from '@gp/ui/components';
import { Check } from '@gp/ui/icons';
import { formatLargeNumber } from '@gp/util/intl';

import CreateAccountBanner from '../../CreatAccountBanner';
import { Option } from '../../ListingSearchBar';
import { getFilterNumber, ListingSearchBarV2 } from '../../ListingSearchBar/v2';
import ForSalePropertyCardV2 from '../../PropertyCard/ForSalePropertyCard/v2';

const SEARCH_LIMIT = 24;

export function getFilterCount(urlParamsFilter: SearchFilter) {
  // Property type fields
  const propertyTypeFields = [
    'house',
    'unit',
    'townhouse',
    'apartment',
    'villa',
    'retirementLiving',
    'land',
    'acreage',
    'rural',
    'blockOfUnits',
    'other',
  ];

  // Check if any property types are selected (not "All Types")
  const hasPropertyTypeFilter = propertyTypeFields.some(
    (field) => urlParamsFilter[field as keyof SearchFilter] === true
  );

  // Count non-property-type filters
  const otherFilters = Object.entries(urlParamsFilter).filter(([key, value]) => {
    // Skip property type fields and areas
    if (propertyTypeFields.includes(key) || key === 'areas') {
      return false;
    }
    return value === true || (typeof value === 'number' && value > 0);
  }).length;

  // Add 1 for property types if any are selected, otherwise 0
  return otherFilters + (hasPropertyTypeFilter ? 1 : 0);
}

export default function ListingSearchResultsV2({
  defaultAreaOptions,
  searchResult,
  isServerAuthed,
}: {
  defaultAreaOptions: Option[];
  searchResult: PropertiesForSaleSearchResult;
  isServerAuthed: boolean;
}) {
  const { status, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { gpExclusiveListingOnly } = useFlags();
  const [total, setTotal] = useState(searchResult.total || 0);
  const [totalPagination, setTotalPagination] = useState(
    searchResult.totalPage === undefined ? 1 : Math.ceil((total || 0) / SEARCH_LIMIT)
  );
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const urlParamsFilter = parseListingSearchParams(searchParams);
  const activePage = urlParamsFilter.page || 1;
  const [exclusiveListingOnly, setExclusiveListingOnly] = useState<boolean>(
    urlParamsFilter.exclusiveListingOnly ?? false
  );
  const [isInitialized, setIsInitialized] = useState(false);
  const filterNumber = getFilterNumber(urlParamsFilter);

  const [showExclusiveListingsFilter, setShowExclusiveListingsFilter] = useSessionStorage({
    key: 'GP_SHOW_EXCLUSIVE_LISTING_FILTER',
    getInitialValueInEffect: false,
    defaultValue: false,
  });
  const { getPropertiesForSale } = getPropertyHubAPIs(token);

  const { data: authPropertyResults, isLoading: isLoadingPropertyResults } = useSWRImmutable(
    `${isServerAuthed}-${searchParams}`,
    async () =>
      isServerAuthed
        ? await getPropertiesForSale(
            defaultAreaOptions.map((o) => o.value),
            urlParamsFilter,
            activePage,
            SEARCH_LIMIT
          )
        : Promise.resolve(undefined)
  );
  useEffect(() => {
    if (!showExclusiveListingsFilter) {
      setShowExclusiveListingsFilter(urlParamsFilter.exclusiveListingOnly ?? false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const propertyResults = isServerAuthed ? authPropertyResults : searchResult;

  useEffect(() => {
    setTotal(propertyResults?.total || 0);
    setTotalPagination(Math.ceil((propertyResults?.total || 0) / SEARCH_LIMIT));
    if (
      isServerAuthed
        ? !isLoadingPropertyResults && authPropertyResults && authPropertyResults.total === 0
        : searchResult.total === 0
    ) {
      datadogRum.addAction('No Properties found', {
        suburb: defaultAreaOptions.map((o) => o.label).join(', '),
        isAuthenticated,
      });
    }
    if (isServerAuthed && propertyResults && !isLoadingPropertyResults) {
      if (
        urlParamsFilter.page &&
        propertyResults?.total < (urlParamsFilter.page - 1) * SEARCH_LIMIT
      ) {
        const newSearchParam = new URLSearchParams(searchParams);
        newSearchParam.delete('page');
        window.location.href = `/property/results/?${newSearchParam}`;
      } else {
        setIsInitialized(true);
      }
    }
  }, [
    authPropertyResults,
    defaultAreaOptions,
    isAuthenticated,
    isLoadingPropertyResults,
    isServerAuthed,
    propertyResults,
    searchParams,
    searchResult.total,
    urlParamsFilter.page,
  ]);

  const numberOfPropertiesStart = SEARCH_LIMIT * (activePage - 1) + 1;
  const numberOfPropertiesEnd = Math.min(total, SEARCH_LIMIT * activePage);

  if (isServerAuthed && !isInitialized) return <Loader />;
  return (
    <Stack gap={0}>
      <Paper
        pt="sm"
        pb="lg"
        shadow="xs"
        radius="0"
        style={{ position: 'sticky', top: '69px', zIndex: '99' }}
      >
        <Container size="responsive">
          <Stack gap="sm">
            <Breadcrumbs separator=">" separatorMargin="xxs">
              <Anchor href="/" size="sm" data-testid="breadcrumbs-home">
                Aussie
              </Anchor>
              <Anchor href="/property/" size="sm" data-testid="breadcrumbs-home">
                Property
              </Anchor>
              <Text size="sm" c={'gray.7'}>
                Results
              </Text>
            </Breadcrumbs>
            <Flex align="center" justify="flex-start" flex="1" gap="xs">
              <Box flex="1 1 80%">
                <ListingSearchBarV2
                  defaultAreaOptions={defaultAreaOptions}
                  containerStyle={{ backgroundColor: '#F2F2F0' }}
                />
              </Box>
            </Flex>
          </Stack>
        </Container>
      </Paper>
      <Container py={{ base: 'sm', sm: 'md' }} fluid style={{ flex: 1, backgroundColor: 'white' }}>
        <Container px={0} size="responsive">
          <Stack gap="xs">
            {total !== 0 && (
              <Text size="md" c="gray.7">
                {numberOfPropertiesStart} - {numberOfPropertiesEnd} of {total} Properties
              </Text>
            )}
            {showExclusiveListingsFilter && gpExclusiveListingOnly && (
              <SegmentedControlWithTracking
                w="340"
                value={exclusiveListingOnly ? 'exclusive' : 'all'}
                onChange={() => {
                  setExclusiveListingOnly(!exclusiveListingOnly);
                  const searchParams = new URLSearchParams(window.location.search);
                  if (!exclusiveListingOnly) {
                    searchParams.set('exclusiveListingOnly', 'true');
                    const newParams = new URLSearchParams(searchParams);
                    newParams.delete('page');
                    window.location.href = `${pathname}?${newParams}`;
                  } else {
                    searchParams.delete('exclusiveListingOnly');
                  }
                  window.history.replaceState(
                    null,
                    '',
                    `${window.location.pathname}?${searchParams.toString()}`
                  );
                }}
                data={[
                  { label: 'Exclusive Only', value: 'exclusive' },
                  { label: 'All Properties', value: 'all' },
                ]}
              />
            )}
          </Stack>
          {propertyResults && total !== 0 ? (
            <Grid mt="lg">
              {propertyResults.propertyList.reduce<ReactNode[]>((acc, property, index) => {
                acc.push(
                  <Grid.Col span={{ base: 12, xs: 12, sm: 6, md: 4 }} key={property.id}>
                    <Flex justify="center">
                      <ForSalePropertyCardV2 property={property} isCarousel={true} />
                    </Flex>
                  </Grid.Col>
                );

                // Show mobile banner after 2nd property (index 1)
                if (index === 1 && !isAuthenticated) {
                  acc.push(
                    <Grid.Col span={12} hiddenFrom="sm" key="banner-mobile">
                      <CreateAccountBanner />
                    </Grid.Col>
                  );
                }

                // Show desktop banner after 6th property (index 5), if we have at least 6 properties
                if (index === 5 && !isAuthenticated && propertyResults.propertyList.length >= 6) {
                  acc.push(
                    <Grid.Col span={12} visibleFrom="sm" key="banner-desktop">
                      <CreateAccountBanner />
                    </Grid.Col>
                  );
                }

                // If we have less than 6 properties, show desktop and mobile banner after the last property
                if (
                  index === propertyResults.propertyList.length - 1 &&
                  !isAuthenticated &&
                  propertyResults.propertyList.length < 6
                ) {
                  acc.push(
                    <>
                      <Grid.Col span={12} hiddenFrom="sm" key="banner-mobile">
                        <CreateAccountBanner />
                      </Grid.Col>
                      <Grid.Col span={12} visibleFrom="sm" key="banner-desktop-fallback">
                        <CreateAccountBanner />
                      </Grid.Col>
                    </>
                  );
                }

                return acc;
              }, [])}
            </Grid>
          ) : (
            <Stack py={{ base: 'xl', md: 'xxl' }} px="sm">
              {!isAuthenticated && <CreateAccountBanner />}
              <Stack gap="xxxs">
                <Text size="md" fw={700} ta="center">
                  No properties found for:
                </Text>
                <Text size="md" fw={700} ta="center">
                  {`${defaultAreaOptions.length} location${
                    defaultAreaOptions.length === 1 ? '' : 's'
                  }`}
                  {urlParamsFilter.minPrice && urlParamsFilter.maxPrice
                    ? `, between $${formatLargeNumber(
                        urlParamsFilter.minPrice
                      )} - $${formatLargeNumber(urlParamsFilter.maxPrice)}`
                    : ''}
                  {` with ${filterNumber} other filter${filterNumber === 1 ? '' : 's'}.`}
                </Text>
              </Stack>
              <Stack align="center" gap="xxxs">
                <Text size="md" c="gray.7" ta="center">
                  Try adjusting your search criteria or adding new suburbs to explore more options.
                </Text>
                {defaultAreaOptions.length > 0 && (
                  <Text size="md" c="gray.7" ta="center">
                    {`To learn more about ${
                      defaultAreaOptions[0]?.label.split(',')[0]
                    }, view the area report `}
                    <AnchorWithTracking
                      href={`/property/${defaultAreaOptions[0]?.value}`}
                      label={'here'}
                      useNextLink
                      position={`No properties found - view suburb profile`}
                      fw={700}
                      linkType={'LinkToPage'}
                      name={'View suburb profile'}
                    />
                  </Text>
                )}
              </Stack>
            </Stack>
          )}
          {totalPagination > 1 && (
            <Flex justify="center">
              <Pagination
                total={totalPagination}
                value={activePage}
                getItemProps={(page) => {
                  const newParams = new URLSearchParams(searchParams);
                  newParams.set('page', page.toString());
                  return {
                    component: 'a',
                    href: `${pathname}?${newParams}`,
                  };
                }}
                getControlProps={(control) => {
                  const newParams = new URLSearchParams(searchParams);
                  const currentPage = activePage;

                  if (control === 'next') {
                    newParams.set('page', (currentPage + 1).toString());
                    return { component: 'a', href: `${pathname}?${newParams}` };
                  }
                  if (control === 'previous') {
                    newParams.set('page', (currentPage - 1).toString());
                    return { component: 'a', href: `${pathname}?${newParams}` };
                  }
                  return {};
                }}
                mt="lg"
              />
            </Flex>
          )}
          {urlParamsFilter.exclusiveListingOnly && activePage === totalPagination && (
            <Flex justify="center">
              <Group mt="md">
                <Stack justify="center" gap="24" align="center" px="lg" py="lg">
                  <Check size={48} fill="#4B1F68" style={{ verticalAlign: 'text-bottom' }} />
                  <Stack gap="16" align="center">
                    <Text ta="center" size="lg" fw="bold">
                      You've seen all {total} exclusive properties within{' '}
                      {defaultAreaOptions.map((option) => option.label).join(', ')}
                    </Text>
                    <Text ta="center" size="lg">
                      Adjust your filters or search criteria to see more results
                    </Text>
                  </Stack>
                </Stack>
              </Group>
            </Flex>
          )}
        </Container>
      </Container>
    </Stack>
  );
}
