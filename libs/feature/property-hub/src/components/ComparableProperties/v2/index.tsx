import { Container, Divider, Stack, Text } from '@mantine/core';
import { Brand } from '@lendi/lala-utils';

import { getListedProperties, getSoldProperties, SUGGESTION_TYPE } from '@gp/data-access/corelogic';
import type { Property } from '@gp/data-access/property-hub';
import { setCoreLogicPropertiesWithSlug } from '@gp/database/property-hub';
import { getLDSingleKindContext, getServerFlag } from '@gp/shared/server-launchdarkly';
import { SectionHeader } from '@gp/ui/components';

import PropertyList from '../PropertyList';

export default async function ComparablePropertiesV2(props: Property) {
  const { id, address, corelogicId, data } = props;
  const { name, state, postcode } = props.suburb;
  const { type, bedrooms, bathrooms, carSpaces } = data;
  const areaType = SUGGESTION_TYPE.SUBURB;
  const areaName = `${name} ${state.slug.toUpperCase()} ${postcode}`;
  const useNewPropertyTable = await getServerFlag(
    'use-new-property-table',
    getLDSingleKindContext(Brand.Aussie),
    false
  );
  const [sold, listed] = await Promise.all([
    setCoreLogicPropertiesWithSlug(
      await getSoldProperties(areaType, areaName, { type, bedrooms, bathrooms, carSpaces }),
      undefined,
      useNewPropertyTable
    ),
    setCoreLogicPropertiesWithSlug(
      await getListedProperties(areaType, areaName, { type, bedrooms, bathrooms, carSpaces }),
      undefined,
      useNewPropertyTable
    ),
  ]);

  const soldProperties = sold.filter(({ id }) => id !== parseInt(corelogicId || ''));
  const listedProperties = listed.filter(({ id }) => id !== parseInt(corelogicId || ''));

  if (soldProperties.length === 0 && listedProperties.length === 0) {
    return null;
  }

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Stack gap="md">
          <SectionHeader
            order={3}
            size="h4"
            title="Comparable properties"
            description="See what similar properties in the area are selling and listed for. "
            info={
              <Stack gap="md" p="xs">
                <Text>
                  This section displays similar properties in the area that were recently sold, as
                  well as currently listed.
                </Text>
                <Text>
                  Compare the property you're interested in, with the listed prices and recent sales
                  of similar properties to gauge if this area aligns with your preferences and
                  budget.
                </Text>
              </Stack>
            }
          />
          <PropertyList
            propertyId={id}
            propertyAddress={address}
            soldProperties={soldProperties}
            listedProperties={listedProperties}
            query={{ type, bedrooms, bathrooms, carSpaces }}
          />
        </Stack>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
