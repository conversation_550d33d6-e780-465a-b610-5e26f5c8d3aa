import { Container, Divider, Stack } from '@mantine/core';

import { getBestOffer, PRIMARY_PURPOSE, SORT_BY } from '@gp/data-access/home-loan-health-check';
import { Property } from '@gp/data-access/property-hub';
import { SectionHeader } from '@gp/ui/components';

import Estimation from '../Estimation';

export default async function RepaymentsV2(property: Property) {
  const estimatedValue = property.data.estimatedValue;
  if (!estimatedValue) return null;

  const { lowestRate, lowestRepayment, comparisonRate } = await getBestOffer(
    estimatedValue.estimate * 0.7,
    30,
    PRIMARY_PURPOSE.OWNER_OCCUPIED,
    SORT_BY.LOWEST_INTEREST_RATE,
    estimatedValue.estimate
  );

  if (!lowestRate || !lowestRepayment) {
    return null;
  }

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <SectionHeader
          title="Estimated repayments"
          order={3}
          size="h4"
          description="Pair this estimate with your monthly expenses to see if the property fits your budget. Chat with an Aussie broker for personalised advice."
        />
        <Estimation
          lowestRate={lowestRate}
          lowestRepayment={lowestRepayment}
          comparisonRate={comparisonRate}
        />
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
