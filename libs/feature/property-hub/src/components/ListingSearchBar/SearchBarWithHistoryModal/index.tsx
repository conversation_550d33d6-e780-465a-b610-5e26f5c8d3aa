import { useContext, useState } from 'react';
import { ActionIcon, Box, Flex, FocusTrap, ScrollArea } from '@mantine/core';
import { useMediaQuery, useViewportSize } from '@mantine/hooks';
import { AuthenticationStatus, EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import {
  CustomerPropertySearch,
  SEARCH_CATEGORY,
  SEARCH_TYPE,
  SearchFilter,
  stringifyListingSearchParams,
} from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { ButtonWithTracking, ModalWithTracking, Option } from '@gp/ui/components';
import { Close, Filter } from '@gp/ui/icons';

import { useSearchHistory } from '../../../hooks/useSearchHistory';
import FilterModal from '../FilterModal';
import SearchBarWithHistory from '../SearchBarWithHistory';
import { getFilterNumber } from '../v2';

import classes from '../../FeatureBasedSearch/style.module.css';

export interface SearchBarWithHistoryProps {
  disclosureForSearchBar: [boolean, { openedSearchBar: boolean; closeSearchBar: () => void }];
  disclosureForFilterModal: [
    boolean,
    { openedFilterModal: boolean; openFilterModal: () => void; closeFilterModal: () => void }
  ];
  searchFilter: Omit<SearchFilter, 'areas'>;
  setSearchFilter: React.Dispatch<React.SetStateAction<Omit<SearchFilter, 'areas'>>>;
  onChange?: (option: Option[]) => void;
  controlledValue?: Option[];
}

const SearchBarWithHistoryModal = ({
  searchFilter,
  setSearchFilter,
  disclosureForSearchBar,
  disclosureForFilterModal,
  onChange,
  controlledValue,
}: SearchBarWithHistoryProps) => {
  const [openedSearchBar, { closeSearchBar }] = disclosureForSearchBar;
  const [openedFilterModal, { openFilterModal, closeFilterModal }] = disclosureForFilterModal;
  const [selectedBuySuburbs, setSelectedBuySuburbs] = useState<Option[]>([]);
  const { addSearchHistory } = useSearchHistory();
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const { height } = useViewportSize();
  const isMobile = useMediaQuery('(max-width: 36em)');
  const [active] = useState(true);

  const { analytics } = useContext(LDContext);

  //areas=nsw%2Feastwood-2122&includeSurroundingSuburbs=true&type=HOUSE&bedrooms=2&bathrooms=2&carSpaces=2&minLandSize=500&maxLandSize=1000
  const generateFilterApiForBuy = async () => {
    const params = Object.entries(searchFilter);

    // Property type boolean fields that should be preserved even if false
    const propertyTypeFields = [
      'house',
      'unit',
      'townhouse',
      'apartment',
      'villa',
      'retirementLiving',
      'land',
      'acreage',
      'rural',
      'blockOfUnits',
      'other',
    ];

    const directParam = params.filter((entry) => {
      const [key, value] = entry;
      // Keep property type booleans (even if false) and other truthy/zero values
      return propertyTypeFields.includes(key) || !!value || value === 0;
    });

    const filteredQuery = Object.fromEntries(directParam);
    const areas =
      selectedBuySuburbs.length > 0
        ? selectedBuySuburbs.map((o) => o.value)
        : controlledValue && controlledValue.length > 0
        ? controlledValue.map((o) => o.value)
        : ['nsw/sydney-2000'];

    //queryString ensures a consistent parameter sequence in the URL and serves as a unique identifier per customerId to prevent duplicates in the table.
    const queryString = stringifyListingSearchParams({
      ...filteredQuery,
      includeSurroundingSuburbs: searchFilter.includeSurroundingSuburbs === false ? false : true,
      areas,
    });
    const resultPageUrl = `/property/results${queryString ? `?${queryString}` : ''}`;
    // prepared for future use
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferredFilters', queryString);
    }
    const label =
      controlledValue && controlledValue.length > 1
        ? `${controlledValue[0].label} & ${controlledValue.length - 1} more`
        : controlledValue && controlledValue.length > 0
        ? `${controlledValue[0].label}`
        : selectedBuySuburbs.length > 1
        ? `${selectedBuySuburbs[0].label} & ${selectedBuySuburbs.length - 1} more`
        : selectedBuySuburbs[0]?.label && selectedBuySuburbs[0]?.label.length > 0
        ? selectedBuySuburbs[0]?.label
        : 'Sydney, NSW 2000';

    const propertySearchCriteria = {
      label,
      queryString,
      searchType: SEARCH_TYPE.LISTED,
      searchCategory: SEARCH_CATEGORY.BUY,
    } as CustomerPropertySearch;

    addSearchHistory(propertySearchCriteria);

    window.location.href = resultPageUrl;
  };

  const handleFilterClick = () => {
    openFilterModal();
    analytics?.trackEvent({
      event_name: 'Filters Button Clicked',
      category: EventCategory.AUSSIE_HOMES,
      text: 'Filters',
      position: 'Property Search Bar Modal',
      authenticationStatus,
    });
  };

  const handleSearchClick = () => {
    generateFilterApiForBuy();
    analytics?.trackEvent({
      event_name: 'Search Button Clicked',
      category: EventCategory.AUSSIE_HOMES,
      text: 'Search',
      position: 'Property Search Bar Modal',
      authenticationStatus,
    });
  };

  const handleResetClick = () => {
    setSelectedBuySuburbs([]);
    onChange && onChange([]);
    analytics?.trackEvent({
      event_name: 'Reset Button Clicked',
      category: EventCategory.AUSSIE_HOMES,
      text: 'Reset',
      position: 'Property Search Bar Modal',
      authenticationStatus,
    });
  };

  const filterNumber = getFilterNumber(searchFilter);

  const disableButton = controlledValue
    ? controlledValue?.length <= 0
    : selectedBuySuburbs?.length <= 0;

  return isMobile ? (
    <ModalWithTracking
      opened={openedSearchBar}
      name={'Search Bar'}
      onClose={closeSearchBar}
      fullScreen
      scrollAreaComponent={ScrollArea.Autosize}
      withCloseButton={false}
      transitionProps={{ transition: 'fade', duration: 200 }}
      footer={
        <Flex justify="flex-end" gap="md">
          <ButtonWithTracking
            flex={1}
            variant="transparent"
            label="Reset"
            fz="lg"
            c={disableButton ? 'gray.5' : 'grape.8'}
            maw="fit-content"
            miw="fit-content"
            onClick={handleResetClick}
          />
          <ButtonWithTracking
            flex={1}
            variant="primary"
            label="Search"
            fz="lg"
            maw="fit-content"
            miw="fit-content"
            onClick={handleSearchClick}
          />
        </Flex>
      }
    >
      <Flex gap="xxs" justify="space-between" p="md" h={height + 100} align="flex-start">
        <ActionIcon
          variant="transparent"
          size={24}
          radius="xl"
          mt={10}
          c="grape.8"
          onClick={closeSearchBar}
        >
          <Close size={24} />
        </ActionIcon>
        <FocusTrap active={active}>
          <Box w="100%" h="auto">
            <SearchBarWithHistory
              onChange={(v) => {
                onChange && onChange(v);
                setSelectedBuySuburbs(v);
              }}
              controlledValue={controlledValue}
              data-autofocus
            />
          </Box>
        </FocusTrap>
        <Flex>
          <ActionIcon
            variant="outline"
            size={50}
            p="xs"
            radius="xl"
            c="grape.8"
            onClick={handleFilterClick}
            className={classes.filterIconButton}
            data-filter-number={filterNumber > 0 ? filterNumber : undefined}
          >
            <Filter size={50} />
          </ActionIcon>
        </Flex>
        <FilterModal
          title="Search"
          disclosure={[openedFilterModal, { openedFilterModal, closeFilterModal }]}
          searchFilter={searchFilter}
          setSearchFilter={setSearchFilter}
        />
      </Flex>
    </ModalWithTracking>
  ) : null;
};

export default SearchBarWithHistoryModal;
