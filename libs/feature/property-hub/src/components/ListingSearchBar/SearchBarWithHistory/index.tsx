import { useCallback, useContext } from 'react';
import { Flex, MantineStyleProp, Stack } from '@mantine/core';
import { useScrollIntoView, useViewportSize } from '@mantine/hooks';
import { AuthenticationStatus, EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import {
  getPropertyHubAPIs,
  SEARCH_RESULT_TYPE,
  type SearchResult,
} from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { MultSelectAutocomplete, Option } from '@gp/ui/components';
import { Plus, Search } from '@gp/ui/icons';

// import { useSearchHistory } from '../../../hooks/useSearchHistory';
import { trendingSuburbs } from './constants';

import classes from './style.module.css';

const searchCache: Record<string, SearchResult[]> = {};

const getSearchResult = async (searchQuery?: string) => {
  const { search } = getPropertyHubAPIs();
  let result: SearchResult[];

  if (!searchQuery || searchQuery.length < 3) {
    result = [];
  } else if (searchQuery in searchCache) {
    result = searchCache[searchQuery];
  } else {
    result = await search(searchQuery, undefined, [SEARCH_RESULT_TYPE.SUBURB]);
    searchCache[searchQuery] = result;
  }

  //Adding id is due to we need propertyId to redirect to relationship page for conversion direct auth
  return result.map(({ id, name, slug }) => ({
    id,
    label: name,
    value: slug,
    icon: <Plus size={16} color="gray.9" />,
  }));
};

export interface SearchBarWithHistoryProps {
  autoScrollOffset?: number;
  onClick?: () => void;
  onChange?: (option: Option[]) => void;
  defaultOptions?: Option[];
  controlledValue?: Option[];
  containerStyle?: MantineStyleProp;
  isContractExpand?: boolean;
}

const SearchBarWithHistory = ({
  autoScrollOffset = 120,
  defaultOptions,
  controlledValue,
  onClick,
  onChange,
  containerStyle,
  isContractExpand = false,
}: SearchBarWithHistoryProps) => {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const { width } = useViewportSize();
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLInputElement>({
    offset: autoScrollOffset,
    duration: 300,
  });
  const isMobile = width < 576;
  // const { searchHistory } = useSearchHistory();

  // const userSearchHistory = searchHistory.map((search) => ({
  //   label: search.label || '',
  //   value: search.queryString || '',
  //   icon: <Plus size={16} color="gray.9" />,
  // }));

  // Equivalent to getSearchResult, with building a map of slug to property id, for later lookups
  const getSearchOptions = useCallback(async (search?: string) => {
    const result = await getSearchResult(search);
    return result;
  }, []);

  const hidePlaceholder = controlledValue && controlledValue?.map(({ value }) => value)?.length > 0;
  const showLimitHitMessage =
    controlledValue && controlledValue?.map(({ value }) => value)?.length >= 10;
  const { analytics } = useContext(LDContext);

  return (
    <Stack gap="xxs" className={classes.searchBar} style={containerStyle}>
      <MultSelectAutocomplete
        data-testid="search-field-autocomplete"
        id="suburb-search-bar-with-history"
        variant="unstyled"
        ref={targetRef}
        flex={1}
        options={getSearchOptions}
        leftSection={
          <Flex style={{ alignSelf: 'flex-start' }} pt="xs">
            <Search />
          </Flex>
        }
        placeholder={
          showLimitHitMessage ? 'Limit is reached' : hidePlaceholder ? '' : 'Search for a suburb'
        }
        minSearchLength={3}
        onClickCapture={onClick}
        defaultOptions={defaultOptions}
        directNavigateOptions={trendingSuburbs}
        directNavigateLabel="Trending suburbs"
        isContractExpand={isContractExpand}
        controlledValue={controlledValue}
        title="Suburb"
        noResults={{
          value: 'No result',
          label: 'No results found',
          description: showLimitHitMessage ? 'Limit is reached' : 'Please search for a suburb',
        }}
        onChange={(value) => onChange && onChange(value || [])}
        onFocus={() => isMobile && scrollIntoView()}
        onClick={() =>
          analytics?.trackEvent({
            event_name: 'Search Bar Clicked',
            category: EventCategory.AUSSIE_HOMES,
            text: 'Search',
            position: 'Property Search Bar With History',
            authenticationStatus,
          })
        }
      />
    </Stack>
  );
};

export default SearchBarWithHistory;
