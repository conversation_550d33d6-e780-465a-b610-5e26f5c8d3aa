'use client';
import { useContext, useEffect, useState } from 'react';
import { ActionIcon, Box, Flex, MantineStyleProp } from '@mantine/core';
import { useDisclosure, useMediaQuery, useSessionStorage } from '@mantine/hooks';
import { useSearchParams } from 'next/navigation';
import { AuthenticationStatus, EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import {
  CustomerPropertySearch,
  parseListingSearchParams,
  SEARCH_CATEGORY,
  SEARCH_TYPE,
  SearchFilter,
  stringifyListingSearchParams,
} from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { ButtonWithTracking } from '@gp/ui/components';
import { Filter } from '@gp/ui/icons';

import { useSearchHistory } from '../../hooks/useSearchHistory';
import { Option } from '../ListingSearchBar';
import { emptyFilter } from '../ListingSearchBar/constants';
import FilterModal from '../ListingSearchBar/FilterModal';
import SearchBarWithHistory from '../ListingSearchBar/SearchBarWithHistory';
import SearchBarWithHistoryModal from '../ListingSearchBar/SearchBarWithHistoryModal';

import classes from '../FeatureBasedSearch/style.module.css';

const shouldFilterNumberCount = (value: boolean | number | undefined | null) => {
  return value === true || value === 0 || (!!value && (value as number) >= 0);
};

export const getFilterNumber = (searchFilter: Omit<SearchFilter, 'areas'>) => {
  let number = 0;
  if (!searchFilter) {
    return number;
  }
  if (!searchFilter.includeSurroundingSuburbs) {
    number += 1;
  }
  if (
    shouldFilterNumberCount(searchFilter.maxBedrooms) ||
    shouldFilterNumberCount(searchFilter.minBedrooms)
  ) {
    number += 1;
  }
  if (
    shouldFilterNumberCount(searchFilter.maxBathrooms) ||
    shouldFilterNumberCount(searchFilter.minBathrooms)
  ) {
    number += 1;
  }
  if (
    shouldFilterNumberCount(searchFilter.maxCarSpaces) ||
    shouldFilterNumberCount(searchFilter.minCarSpaces)
  ) {
    number += 1;
  }
  if (
    shouldFilterNumberCount(searchFilter.maxLandSize) ||
    shouldFilterNumberCount(searchFilter.minLandSize)
  ) {
    number += 1;
  }
  if (
    shouldFilterNumberCount(searchFilter.maxPrice) ||
    shouldFilterNumberCount(searchFilter.minPrice)
  ) {
    number += 1;
  }
  // Check if any property types are selected (not "All Types")
  if (
    searchFilter.house ||
    searchFilter.unit ||
    searchFilter.townhouse ||
    searchFilter.apartment ||
    searchFilter.villa ||
    searchFilter.retirementLiving ||
    searchFilter.land ||
    searchFilter.acreage ||
    searchFilter.rural ||
    searchFilter.blockOfUnits ||
    searchFilter.other
  ) {
    number += 1;
  }
  return number;
};

export const ListingSearchBarV2 = ({
  containerStyle,
  defaultAreaOptions,
}: {
  containerStyle?: MantineStyleProp;
  defaultAreaOptions?: Option[];
}) => {
  const [openedFilterModal, { open: openFilterModal, close: closeFilterModal }] =
    useDisclosure(false);
  const [openedSearchBar, { open: openSearchBar, close: closeSearchBar }] = useDisclosure(false);

  const searchParams = useSearchParams();
  const searchParamSize = searchParams.size;
  const filtersFromParam = parseListingSearchParams(searchParams.toString());
  const [searchFilter, setSearchFilter] = useState<Omit<SearchFilter, 'areas'>>(
    searchParamSize <= 0 ? emptyFilter : filtersFromParam
  );
  const [selectedBuySuburbs, setSelectedBuySuburbs] = useState<Option[]>(defaultAreaOptions || []);

  const { addSearchHistory } = useSearchHistory();
  const { analytics } = useContext(LDContext);
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const isMobile = useMediaQuery('(max-width: 36em)');
  const isDesktop = useMediaQuery('(min-width: 62em)');

  // reset exclusive listing filter when user goes back to property page
  const [, , removeShowExclusiveListingsFilter] = useSessionStorage({
    key: 'GP_SHOW_EXCLUSIVE_LISTING_FILTER',
  });
  useEffect(() => {
    removeShowExclusiveListingsFilter();
  }, [removeShowExclusiveListingsFilter]);

  //areas=nsw%2Feastwood-2122&includeSurroundingSuburbs=true&type=HOUSE&bedrooms=2&bathrooms=2&carSpaces=2&minLandSize=500&maxLandSize=1000
  const generateFilterApiForBuy = async () => {
    const params = Object.entries(searchFilter);

    const directParam = params.filter((entry) => {
      return !!entry[1] || entry[1] === 0;
    });

    const filteredQuery = Object.fromEntries(directParam);
    const areas =
      selectedBuySuburbs.length > 0 ? selectedBuySuburbs.map((o) => o.value) : ['nsw/sydney-2000'];

    //queryString ensures a consistent parameter sequence in the URL and serves as a unique identifier per customerId to prevent duplicates in the table.
    const queryString = stringifyListingSearchParams({
      ...filteredQuery,
      includeSurroundingSuburbs: searchFilter.includeSurroundingSuburbs === false ? false : true,
      areas,
    });
    const resultPageUrl = `/property/results${queryString ? `?${queryString}` : ''}`;
    // prepared for future use
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferredFilters', queryString);
    }
    const label =
      selectedBuySuburbs.length > 1
        ? `${selectedBuySuburbs[0].label} & ${selectedBuySuburbs.length - 1} more`
        : selectedBuySuburbs[0]?.label ?? 'Sydney, NSW 2000';

    const propertySearchCriteria = {
      label,
      queryString,
      searchType: SEARCH_TYPE.LISTED,
      searchCategory: SEARCH_CATEGORY.BUY,
    } as CustomerPropertySearch;

    addSearchHistory(propertySearchCriteria);

    window.location.href = resultPageUrl;
  };

  const handleFilterClick = () => {
    openFilterModal();
    analytics?.trackEvent({
      event_name: 'Filters Button Clicked',
      category: EventCategory.AUSSIE_HOMES,
      text: 'Filters',
      position: 'Feature-based Property Search Bar',
      authenticationStatus,
    });
  };

  const handleSearchClick = () => {
    generateFilterApiForBuy();
    analytics?.trackEvent({
      event_name: 'Search Button Clicked',
      category: EventCategory.AUSSIE_HOMES,
      text: 'Search',
      position: 'Feature-based Property Search Bar',
      authenticationStatus,
    });
  };

  const filterNumber = getFilterNumber(searchFilter);
  return (
    <Flex gap="xs" justify="space-between">
      <Box w="100%">
        <SearchBarWithHistory
          onChange={(v) => {
            setSelectedBuySuburbs(v);
          }}
          controlledValue={selectedBuySuburbs}
          onClick={() => isMobile && openSearchBar()}
          containerStyle={containerStyle}
          isContractExpand={true}
        />
      </Box>
      {isMobile ? (
        <Flex>
          <ActionIcon
            variant="outline"
            size={50}
            p="xs"
            radius="xl"
            c="grape.8"
            onClick={handleFilterClick}
            className={classes.filterIconButton}
            data-filter-number={filterNumber > 0 ? filterNumber : undefined}
          >
            <Filter size={50} />
          </ActionIcon>
        </Flex>
      ) : !isDesktop && !isMobile ? (
        <Flex gap="xs">
          <ActionIcon
            variant="outline"
            size={50}
            p="xs"
            radius="xl"
            c="grape.8"
            onClick={handleFilterClick}
            className={classes.filterIconButton}
            data-filter-number={filterNumber > 0 ? filterNumber : undefined}
          >
            <Filter size={50} />
          </ActionIcon>
          <ButtonWithTracking
            flex={1}
            label="Search"
            maw="fit-content"
            miw="fit-content"
            fz="md"
            onClick={handleSearchClick}
          />
        </Flex>
      ) : (
        <Flex gap="xs">
          <ButtonWithTracking
            flex={1}
            variant="secondary"
            label="Filters"
            rightSection={<Filter size={24} />}
            maw="fit-content"
            miw="fit-content"
            data-filter-number={filterNumber > 0 ? filterNumber : undefined}
            className={classes.filterButton}
            fz="md"
            onClick={handleFilterClick}
          />
          <ButtonWithTracking
            flex={1}
            label="Search"
            maw="fit-content"
            miw="fit-content"
            fz="md"
            onClick={handleSearchClick}
          />
        </Flex>
      )}
      <FilterModal
        title="Filters"
        disclosure={[openedFilterModal, { openedFilterModal, closeFilterModal }]}
        searchFilter={searchFilter}
        setSearchFilter={setSearchFilter}
        controlledValue={selectedBuySuburbs}
      />
      <SearchBarWithHistoryModal
        disclosureForSearchBar={[openedSearchBar, { openedSearchBar, closeSearchBar }]}
        disclosureForFilterModal={[
          openedFilterModal,
          { openedFilterModal, openFilterModal, closeFilterModal },
        ]}
        searchFilter={searchFilter}
        setSearchFilter={setSearchFilter}
        onChange={setSelectedBuySuburbs}
        controlledValue={selectedBuySuburbs}
      />
    </Flex>
  );
};
