'use client';

import { Container, Divider, Flex, SimpleGrid, Stack, Text, Title } from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import { AnchorWithTracking, Collapse, SectionHeader } from '@gp/ui/components';

import AgeRanges from '../AgeRanges';
import HouseholdCompositions from '../HouseholdCompositions';
import IncomeGroups from '../IncomeGroups';
import OccupantStatus from '../OccupantStatus';
import Occupations from '../Occupations';
import PropertyTypes from '../PropertyTypes';
import Summary from '../Summary';

export default function ResidentsV2(props: Council | State | Suburb | Property) {
  const isProperty = 'address' in props;
  const isCouncil = 'state' in props && 'suburbs' in props;
  const isSuburb = 'state' in props && 'surroundingSuburbs' in props;

  const dataPageType = isProperty
    ? 'Property'
    : isSuburb
    ? 'Suburb'
    : isCouncil
    ? 'Council'
    : 'State';

  const areaName = isProperty ? props.suburb.name : props.name;
  const areaData = isProperty ? props.suburb.data : props.data;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            title={`${areaName} residents`}
            order={3}
            size="h4"
            description={
              isProperty ? (
                <>
                  Key characteristics of the residents in the area, view the full breakdown of the
                  suburb{' '}
                  <AnchorWithTracking
                    useNextLink
                    label={areaName}
                    href={`/property/${props.suburb.slug}/`}
                    fw={700}
                  >
                    here
                  </AnchorWithTracking>
                  .
                </>
              ) : (
                'Key characteristics of the residents in the area, open the breakdown to see more detail for each category.'
              )
            }
            info={
              isProperty ? (
                <Stack gap="md" p="xs">
                  <Stack gap="xxs">
                    <Title order={5}>Age range</Title>
                    <Text>
                      Shows you the most common age range in the area based on census data, and may
                      hint at the area's lifestyle or nearby amenities.
                    </Text>
                  </Stack>
                  <Stack gap="xxs">
                    <Title order={5}>Occupant status</Title>
                    <Text>
                      Shows you the most common ownership status of the properties in the area. It
                      may indicate a level of stability in the suburb, for example, long term home
                      owners or short term renters.
                    </Text>
                  </Stack>
                  <Stack gap="xxs">
                    <Title order={5}>Household profiles</Title>
                    <Text>
                      Shows you the most common make up of each household in the area, it may
                      indicate potential for socialisation opportunities, or the demand for
                      differently sized properties.
                    </Text>
                  </Stack>
                  <Stack gap="xxs">
                    <Title order={5}>Individual yearly income</Title>
                    <Text>
                      Shows the most common salary range in the area and may indicate the affluence
                      of the residents that live there.
                    </Text>
                  </Stack>
                  <Stack gap="xxs">
                    <Title order={5}>Occupations</Title>
                    <Text>
                      Shows the most common occupation type in the area, this may indicate
                      businesses or services in the area, potential networking opportunities or how
                      much you may relate to the residents around you.
                    </Text>
                  </Stack>
                  <Stack gap="xxs">
                    <Title order={5}>Property type</Title>
                    <Text>
                      Shows you the most common property type in the suburb and may indicate the
                      likelihood of you finding your ideal property type in the area.
                    </Text>
                  </Stack>
                </Stack>
              ) : undefined
            }
          />
          <Summary data={areaData} />
          {!isProperty && (
            <Collapse
              in={false}
              expandText="Open breakdown"
              collapseText="Close breakdown"
              position="Demographics Data Accordion"
              data-testid="expand-collapse-in-demographics-data"
              type={dataPageType}
            >
              <SimpleGrid cols={{ base: 1, md: 2 }} spacing="sm">
                <OccupantStatus data={areaData.occupantStatus} />
                <PropertyTypes data={areaData.propertyTypes} />
                <AgeRanges data={areaData.ageRanges} />
                <HouseholdCompositions data={areaData.householdCompositions} />
                <IncomeGroups data={areaData.incomeGroups} />
                <Occupations area={areaName} data={areaData.occupations} />
              </SimpleGrid>
            </Collapse>
          )}
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
