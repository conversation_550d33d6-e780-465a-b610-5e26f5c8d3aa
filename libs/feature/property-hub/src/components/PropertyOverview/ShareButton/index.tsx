'use client';

import { useState } from 'react';
import { ActionIcon, Group, Text, UnstyledButton } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';

import { ButtonWithTracking, Image, ModalWithTracking } from '@gp/ui/components';
import { ContentCopy, Share, SimpleCheck } from '@gp/ui/icons';

interface ShareButtonProps {
  propertyAddress: string;
  size?: number;
  iconSize?: number;
  variant?: 'default' | 'listing';
  iconOnly?: boolean;
}

export default function ShareButton({
  propertyAddress,
  size = 45,
  iconSize = 40,
  variant = 'default',
  iconOnly = false,
}: ShareButtonProps) {
  const { width } = useViewportSize();
  const [showShareModal, setShowShareModal] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);

  const handleShare = async () => {
    if (width <= 576 && navigator.share) {
      try {
        await navigator.share({
          title: 'Share this property',
          text: `Check out this property: ${propertyAddress}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Share failed:', error);
      }
    } else {
      setShowShareModal(true);
    }
  };

  const handleCopyLink = () => {
    try {
      navigator.clipboard.writeText(window.location.href);
      setCopiedLink(true);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  return (
    <>
      {variant !== 'listing' ? (
        <ActionIcon variant="transparent" size={size} onClick={handleShare}>
          <Image
            src={'property-hub/interaction-button.svg'}
            height={iconSize}
            width={iconSize}
            alt="Share"
          />
        </ActionIcon>
      ) : (
        <UnstyledButton variant="transparent" onClick={handleShare}>
          <Group gap="3">
            <Share fill="transparent" color="#4B1F68" size={24} />
            {!iconOnly && (
              <Text component="span" c="#4B1F68">
                Share
              </Text>
            )}
          </Group>
        </UnstyledButton>
      )}

      {showShareModal && (
        <ModalWithTracking
          name="Share this property"
          opened={showShareModal}
          centered
          size={600}
          onClose={() => {
            setShowShareModal(false);
            setCopiedLink(false);
          }}
        >
          <ButtonWithTracking
            variant="transparent"
            label="Copy link to share"
            onClick={handleCopyLink}
            leftSection={copiedLink ? <SimpleCheck /> : <ContentCopy />}
            name={`Property - ${propertyAddress}`}
            type="button"
            position={'Property Overview'}
          />
        </ModalWithTracking>
      )}
    </>
  );
}
