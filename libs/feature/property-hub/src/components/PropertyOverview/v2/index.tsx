import { Flex } from '@mantine/core';

import type { Property } from '@gp/data-access/property-hub';

import PropertyGalleryV2 from '../PropertyGallery/v2';
import PropertySummary from '../PropertySummary';

export default async function PropertyOverviewV2(property: Property) {
  return (
    <Flex justify="start" w="100%" gap="sm" direction="column">
      <PropertyGalleryV2 {...property} />
      <PropertySummary {...property} />
    </Flex>
  );
}
