'use client';

import { CarouselSlide } from '@mantine/carousel';
import { Box, Center, Flex, Grid, Stack, Text, Title } from '@mantine/core';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { getPropertyHubAPIs, type Property } from '@gp/data-access/property-hub';
import { Carousel, Image } from '@gp/ui/components';

import PropertyTrack from '../../../PropertyTrack';
import ShareButton from '../../ShareButton';

import classes from './style.module.css';

const CAROUSEL_CONFIG = {
  slideSize: 100,
  loop: true,
  h: { base: 400 },
  style: { borderRadius: '6px' },
};

// No Image Available component
function NoImageAvailable() {
  return (
    <Center
      w="100%"
      h={{ base: 250, xs: 300, sm: 400 }}
      bg="#F2F2F0"
      style={{ borderRadius: '6px' }}
    >
      <Text c="gray.7" fw={700}>
        No images available
      </Text>
    </Center>
  );
}

function PropertyCarousel({
  photos,
  floorPlans,
  altText,
  type,
}: {
  photos: string[];
  floorPlans: string[];
  altText: string;
  type: 'small' | 'large';
}) {
  return (
    <Grid.Col span={{ base: 12, md: 9, Lg: 9, xl: type === 'small' ? 8 : 7 }}>
      <Carousel {...CAROUSEL_CONFIG} controlPosition="center">
        {photos.map((url, index) => (
          <CarouselSlide key={`slide-${index}`}>
            <Image src={url} alt={altText} fit="cover" loading="lazy" radius="6px" h="100%" />
          </CarouselSlide>
        ))}
        {floorPlans.length > 0 && (
          <CarouselSlide key="slide-floorplan" bg="#F2F2F0">
            <Image
              src={floorPlans[0]}
              alt={`Floor plan for ${altText}`}
              fit="contain"
              loading="lazy"
              radius="6px"
              h="100%"
            />
          </CarouselSlide>
        )}
      </Carousel>
    </Grid.Col>
  );
}

// Reusable gallery component for 2-3 photos
function SmallGallery({
  photos,
  floorPlans,
  altText,
}: {
  photos: string[];
  floorPlans: string[];
  altText: string;
}) {
  return (
    <Grid.Col span={{ base: 0, md: 3, Lg: 3, xl: 4 }}>
      <Box className={classes.gallery__small} visibleFrom="md">
        <Image src={photos[0]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />
        {floorPlans.length > 0 ? (
          <Image
            src={floorPlans[0]}
            alt={`Floor plan for ${altText}`}
            fit="contain"
            loading="lazy"
            radius="6px"
            h="100%"
          />
        ) : photos.length > 1 ? (
          <Image src={photos[1]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />
        ) : null}
      </Box>
    </Grid.Col>
  );
}

// Reusable gallery component for 4+ photos
function LargeGallery({
  photos,
  floorPlans,
  altText,
}: {
  photos: string[];
  floorPlans: string[];
  altText: string;
}) {
  return (
    <Grid.Col span={{ base: 0, md: 3, Lg: 3, xl: 5 }}>
      <Box className={classes.gallery} visibleFrom="md">
        {/* First column - stacked vertically */}
        <Image src={photos[0]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />
        <Image src={photos[1]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />

        {/* Second column - stacked vertically */}
        <Image src={photos[2]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />
        {floorPlans.length > 0 ? (
          <Image
            src={floorPlans[0]}
            alt={`Floor plan for ${altText}`}
            fit="contain"
            loading="lazy"
            radius="6px"
            h="100%"
          />
        ) : photos.length > 3 ? (
          <Image src={photos[3]} alt={altText} fit="cover" loading="lazy" radius="6px" h="196px" />
        ) : null}
      </Box>
    </Grid.Col>
  );
}

// Single photo carousel
function SinglePhotoCarousel({ photo, altText }: { photo: string; altText: string }) {
  return (
    <Box flex="1">
      <Carousel {...CAROUSEL_CONFIG}>
        <CarouselSlide>
          <Image src={photo} alt={altText} fit="cover" loading="lazy" radius="6px" h="100%" />
        </CarouselSlide>
      </Carousel>
    </Box>
  );
}

export default function PropertyGalleryV2(property: Property) {
  const { id, data, corelogicId } = property;
  const { status, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { getPropertyImages } = getPropertyHubAPIs(token);
  const { primaryAddress, photo } = data;

  const { data: imagery, isLoading } = useSWRImmutable(
    isAuthenticated && token && corelogicId ? `${corelogicId}-property-images` : null,
    async () => await getPropertyImages(corelogicId)
  );

  const photos =
    imagery?.photos ||
    (data.photos && data.photos.length > 0 ? data.photos : undefined) ||
    (data.photo ? [data.photo] : []);
  const floorPlans = imagery?.floorPlans || [];
  const propertyType = data.type?.toLowerCase() === 'house' ? 'house' : 'unit';
  const altText = `${data.bedrooms ?? 0}-bedroom ${propertyType} at ${property.address}, ${
    property.suburb.council?.name || ''
  }, ${property.suburb.state.name}`;

  const renderGallery = () => {
    // Show no image available if explicitly set or if no photos exist
    if (!isLoading && !photos.length) {
      return <NoImageAvailable />;
    }

    if (photos.length === 1) {
      return <SinglePhotoCarousel photo={photos[0]} altText={altText} />;
    }

    if (photos.length && photos.length <= 3) {
      return (
        <>
          <PropertyCarousel
            photos={photos}
            floorPlans={floorPlans}
            altText={altText}
            type="small"
          />
          <SmallGallery photos={photos} floorPlans={floorPlans} altText={altText} />
        </>
      );
    }

    return (
      <>
        <PropertyCarousel photos={photos} floorPlans={floorPlans} altText={altText} type="large" />
        <LargeGallery photos={photos} floorPlans={floorPlans} altText={altText} />
      </>
    );
  };

  return (
    <Stack>
      <Flex justify="space-between" align="center" visibleFrom="sm">
        <Title order={1} size="h4">
          {data.primaryAddress}
        </Title>
        <Flex gap="xs">
          <ShareButton propertyAddress={primaryAddress || ''} variant="listing" />
          <PropertyTrack
            propertyId={id.toString()}
            propertyAddress={primaryAddress || ''}
            variant="listing"
            photo={photo}
            iconOnly
          />
        </Flex>
      </Flex>
      <Grid gutter="6">{renderGallery()}</Grid>
    </Stack>
  );
}
