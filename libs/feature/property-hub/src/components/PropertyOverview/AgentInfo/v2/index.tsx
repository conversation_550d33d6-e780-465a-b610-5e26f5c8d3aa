import { Anchor, Box, Group, Image, Stack, Text } from '@mantine/core';

import { Email, Phone } from '@gp/ui/icons';

type AgentInfoProps = {
  agency?: string | null;
  agent?: string | null;
  phone?: string | null;
  email?: string | null;
  photo?: string | null;
  logo?: string | null;
  brandColor?: string | null;
  address?: string | null;
  children?: React.ReactNode;
};

export default function AgentInfoV2({
  agency,
  agent,
  phone,
  email,
  photo,
  logo,
  brandColor,
  children,
}: AgentInfoProps) {
  if (!agency && !agent) return null;
  const brandedBorder =
    brandColor && brandColor !== '#FFFFFF' ? `1px solid ${brandColor}` : '1px solid #F2F2F0';
  return (
    <Box>
      <Stack gap={0}>
        <Group
          bg={brandColor || '#F8F8F6'}
          mih={32}
          p="xxxs"
          style={{
            borderRadius: '6px 6px 0 0',
            borderTop: brandedBorder,
            borderLeft: brandedBorder,
            borderRight: brandedBorder,
          }}
        >
          {logo && <Image src={logo} alt={agency || 'Agency logo'} height={16} />}
        </Group>
        <Stack gap="0" style={{ border: '1px solid #F2F2F0' }}>
          <Group gap="xs" p="sm" wrap="nowrap" align="top">
            {photo && (
              <Image
                src={photo}
                alt={agent || 'Agent photo'}
                height={80}
                width={80}
                radius="100%"
              />
            )}
            <Stack gap="xxs">
              <Stack gap="0">
                {agent && (
                  <Text size="sm" fw="700">
                    {agent}
                  </Text>
                )}
                {agency && <Text size="sm">{agency}</Text>}
              </Stack>
              <Stack gap="xxs">
                {phone && (
                  <Group justify="flex-start" gap="6" c="grape.8" wrap="nowrap">
                    <Phone />
                    <Text>
                      <Anchor href={`tel:${phone}`}>{phone}</Anchor>
                    </Text>
                  </Group>
                )}
                {email && (
                  <Group justify="flex-start" gap="6" c="grape.8" wrap="nowrap">
                    <Email />
                    <Text style={{ wordBreak: 'break-all' }}>
                      <Anchor href={`mailto:${email}`}>{email}</Anchor>
                    </Text>
                  </Group>
                )}
              </Stack>
            </Stack>
          </Group>
          <Group justify="center" pb="sm">
            <Box maw="150px">{children}</Box>
          </Group>
        </Stack>
      </Stack>
    </Box>
  );
}
