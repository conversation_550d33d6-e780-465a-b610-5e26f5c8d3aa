'use client';

import { useEffect } from 'react';
import { Badge, Container, Divider, Flex, Group, Stack, Text, Title } from '@mantine/core';
import { Status, useSession } from '@lendi/lala-react';

import { LISTING_TYPE, type Property } from '@gp/data-access/property-hub';
import {
  BathroomOutlined,
  BedroomOutlined,
  CarspaceOutlined,
  FloorplanOutlined,
  LandsizeOutlined,
} from '@gp/ui/icons';

import { useViewedHistory } from '../../../../hooks/useViewedHistory';
import PropertyTrack from '../../../PropertyTrack';
import ShareButton from '../../ShareButton';

export default function PropertySummaryV2(property: Property) {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const isListing = !!property.data.campaignId;

  const { id, data, slug, corelogicId } = property;
  const {
    primaryAddress,
    streetAddress,
    suburb,
    state,
    postcode,
    forSale,
    listingType,
    listingDescription,
    bedrooms,
    bathrooms,
    carSpaces,
    type,
    subType,
    floorArea,
    landArea,
  } = data;
  const { addViewedPropertyToLocalStorage } = useViewedHistory();

  useEffect(() => {
    addViewedPropertyToLocalStorage({
      propertyId: id,
      relationship: null,
      address: streetAddress || primaryAddress || '',
      slug,
      photo: data.photo || '',
      type: type || '',
      subType: subType || '',
      bedrooms: bedrooms || 0,
      bathrooms: bathrooms || 0,
      carSpaces: carSpaces || 0,
      forSale: forSale || false,
      listingType: listingType || null,
      suburb: suburb || '',
      postcode: postcode || '',
      state: state || '',
      updatedAt: new Date(),
      corelogicId: corelogicId || '',
      data: data || {},
    });
  }, [
    addViewedPropertyToLocalStorage,
    bathrooms,
    bedrooms,
    carSpaces,
    corelogicId,
    data,
    data.photo,
    forSale,
    id,
    listingType,
    postcode,
    primaryAddress,
    property.id,
    slug,
    state,
    streetAddress,
    subType,
    suburb,
    type,
  ]);

  const propertyAddress = `${streetAddress || primaryAddress}, ${suburb}, ${state} ${postcode}`;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} pt={0} flex={1}>
      <Stack gap="sm">
        <Flex justify="space-between">
          {(isListing || isAuthenticated) && forSale && listingType === LISTING_TYPE.OFF_MARKET && (
            <Badge size="sm" radius="xs" variant="light-blue" flex="0 0 auto">
              Off-market listing
            </Badge>
          )}
          {(isListing || isAuthenticated) && forSale && listingType === LISTING_TYPE.PRE_MARKET && (
            <Badge size="sm" radius="xs" variant="light-blue" flex="0 0 auto">
              Pre-market listing
            </Badge>
          )}
          {forSale && listingType === LISTING_TYPE.ON_MARKET && (
            <Badge size="sm" radius="xs" variant="light-green" flex="0 0 auto">
              For sale
            </Badge>
          )}
          <Group gap="sm" hiddenFrom="sm" mah={24} align="flex-start">
            <ShareButton propertyAddress={primaryAddress || ''} variant="listing" iconOnly />
            <PropertyTrack
              propertyId={id.toString()}
              propertyAddress={primaryAddress || ''}
              variant="listing"
              photo={data.photo}
              iconOnly
            />
          </Group>
        </Flex>
        <Group gap="sm" justify="space-between" wrap="nowrap" align="start">
          {((isListing && listingDescription) || isAuthenticated) && forSale ? (
            <Stack gap="xxxs">
              <Title order={1} size="h5" lineClamp={2}>
                {listingDescription}
              </Title>
              <Text size="md">{propertyAddress}</Text>
            </Stack>
          ) : (
            <Title order={1} size="h5" lineClamp={2}>
              {propertyAddress}
            </Title>
          )}
        </Group>
        <Stack gap="xxs">
          <Group gap="sm">
            <Group gap="xxs">
              <BedroomOutlined size={20} fill="transparent" />
              <Text>{`${bedrooms ?? 0}`}</Text>
              <BathroomOutlined size={20} fill="transparent" />
              <Text>{`${bathrooms ?? 0}`}</Text>
              {/* For now, don't show car spaces if it's 0 */}
              {carSpaces && (
                <>
                  <CarspaceOutlined size={20} fill="transparent" />
                  <Text>{`${carSpaces}`}</Text>
                </>
              )}
              <LandsizeOutlined size={20} fill="transparent" />
              <Text>{`${landArea ?? 0}m²`}</Text>
              <FloorplanOutlined size={20} fill="transparent" />
              <Text>{`${floorArea ?? 0}m²`}</Text>
              <Text>•</Text>
              <Text tt="capitalize">
                {[type.toLowerCase(), subType?.toLowerCase()].filter(Boolean).join(' - ')}
              </Text>
            </Group>
          </Group>
        </Stack>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
