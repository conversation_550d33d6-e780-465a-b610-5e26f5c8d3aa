import { Box, Divider, Flex, Group, Paper, Stack, Title } from '@mantine/core';

import { Property } from '@gp/data-access/property-hub';
import { Aussie, LogoIcon } from '@gp/ui/icons';

import BrokerCTAV2 from '../../Brokers/BrokerCta/v2';
import { PropertyEnquiryWithSession } from '../../PropertyEnquiry';
import { PropertyDetails } from '../../PropertyEnquiry/EnquiryModal';
import AgentInfoV2 from '../../PropertyOverview/AgentInfo/v2';
import BuyerCTA from '../BuyerCTA';

export default function SideBarSticky(property: Property) {
  const { id, data, address, slug } = property;
  const propertyId = id || property.id;
  const { realEstateAgents, forSale, informationStatement, state, listingLoopId } = data;
  const showPropertyEnquiry = forSale && (state !== 'VIC' || informationStatement);
  return (
    <div
      style={{
        position: 'sticky',
        top: '100px',
        zIndex: 10,
      }}
      id="sticky-side-panel"
    >
      <Stack gap="0">
        {forSale && !!realEstateAgents && realEstateAgents.length > 0 ? (
          <AgentInfoV2
            agency={realEstateAgents[0]?.agency?.name}
            agent={realEstateAgents[0]?.name}
            phone={realEstateAgents[0]?.phone}
            email={realEstateAgents[0]?.email}
            photo={realEstateAgents[0]?.photo}
            logo={realEstateAgents[0]?.agency?.logo || realEstateAgents[0]?.agency?.logoUrl}
            brandColor={
              realEstateAgents[0]?.agency?.brand?.primary ||
              realEstateAgents[0]?.agency?.brandColour?.primary
            }
            address={address}
          >
            {showPropertyEnquiry && (
              <Box maw="150px">
                <PropertyEnquiryWithSession
                  propertyDetails={
                    {
                      propertyAddress: address || '',
                      propertyId: propertyId || '',
                      llPropertyId: listingLoopId || '',
                      propertySlug: slug || '',
                      agentDetails: {
                        agentName: realEstateAgents?.[0]?.name || '',
                        agentEmail: realEstateAgents?.[0]?.email || '',
                        agentPhone: realEstateAgents?.[0]?.phone || '',
                        agencyName: realEstateAgents?.[0]?.agency?.name || '',
                      },
                    } as PropertyDetails
                  }
                />
              </Box>
            )}
          </AgentInfoV2>
        ) : (
          showPropertyEnquiry && (
            <Stack
              style={{ border: '1px solid #F2F2F0', borderRadius: '6px 6px 0 0' }}
              justify="center"
              p="md"
            >
              <Group px="8" justify="center">
                <LogoIcon size={44} style={{ padding: '1px' }} />
              </Group>
              <Title fw={700} component="p" size="h5" ta="center">
                Interested in {address ? address : 'this property'}?
              </Title>
              <Group maw="150px" style={{ margin: '0 auto' }} justify="center">
                <PropertyEnquiryWithSession
                  propertyDetails={
                    {
                      propertyAddress: address || '',
                      propertyId: propertyId || '',
                      llPropertyId: listingLoopId || '',
                      propertySlug: slug || '',
                      agentDetails: {
                        agentName: realEstateAgents?.[0]?.name || '',
                        agentEmail: realEstateAgents?.[0]?.email || '',
                        agentPhone: realEstateAgents?.[0]?.phone || '',
                        agencyName: realEstateAgents?.[0]?.agency?.name || '',
                      },
                    } as PropertyDetails
                  }
                />
              </Group>
            </Stack>
          )
        )}
        <Paper w="100%">
          <Flex bg="#4B1F68" px="sm" py="4px" align="center" gap="sm">
            <Aussie size={32} width="128px" style={{ color: 'white' }} />
          </Flex>
          <Stack gap={0}>
            <BrokerCTAV2 />
            <Divider mx="24px" />
            <BuyerCTA />
          </Stack>
        </Paper>
      </Stack>
    </div>
  );
}
