'use client';

import { Box, Stack, Title } from '@mantine/core';

import { ButtonWithTracking } from '@gp/ui/components';
import { Aussie } from '@gp/ui/icons';

export default function BuyerCTA() {
  const handleClick = () => {
    window.location.assign('/book-appointment/buyers-agent/');
  };
  return (
    <Box p="md">
      <Stack gap="md" align="center">
        <Title fw={700} component="p" size="h5" ta="center">
          Looking to buy? We've got your back.
        </Title>
        <ButtonWithTracking
          label="Team up with a buyer's agent"
          variant="highlight"
          position="Side Panel CTA - Buyers Agent"
          purpose="Connect with buyers agent"
          onClick={handleClick}
        />
      </Stack>
    </Box>
  );
}
