'use client';
import { Flex } from '@mantine/core';

import { Property } from '@gp/data-access/property-hub';
import { ButtonWithTracking } from '@gp/ui/components';

export default function MobileBottomBarSticky(property: Property) {
  const { data } = property;
  const { forSale, informationStatement, state } = data;
  const showPropertyEnquiry = forSale && (state !== 'VIC' || informationStatement);
  const handleClick = () => {
    const sticky = document.getElementById('sticky-side-panel');
    window.scrollTo({
      top: (sticky?.offsetTop || 0) - 70,
      behavior: 'smooth',
    });
  };

  if (!showPropertyEnquiry) {
    return null;
  }

  return (
    <Flex
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        zIndex: 998,
        borderTop: '1px solid #F2F2F0',
        borderRadius: '16px 16px 0 0',
      }}
      bg="#ffffff"
      p="14px"
      w="100%"
      hiddenFrom="sm"
      justify="flex-start"
    >
      <ButtonWithTracking
        label="Enquire Now"
        variant="secondary"
        position="Mobile Bottom Sticky - Property "
        purpose="Enquire Now"
        onClick={handleClick}
      />
    </Flex>
  );
}
