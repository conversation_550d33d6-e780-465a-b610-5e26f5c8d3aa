import { Box, Center, Container, Divider, Flex, Stack, Text, Title } from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import { Image, LinkButtonWithTracking } from '@gp/ui/components';

export default function BuyersAgentPanelV2(props: Council | Property | State | Suburb) {
  const position =
    'address' in props
      ? 'Property Page'
      : 'councils' in props
      ? 'State Page'
      : 'suburbs' in props
      ? 'Council Page'
      : 'Suburb Page';

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Center>
          <Flex
            direction={{ base: 'column', md: 'row' }}
            align={{ base: 'flex-start', md: 'center' }}
            justify={{ base: 'stretch', md: 'space-between' }}
            maw={960}
            gap="md"
          >
            <Flex
              direction={{ base: 'column', md: 'row' }}
              align={{ base: 'flex-start', md: 'center' }}
              rowGap="xs"
              columnGap="md"
            >
              <Image
                src="property-hub/buyers-agent-panel-image.svg"
                alt="Buyers Agent"
                radius={6}
                w={{ base: '100%', md: 175 }}
                h={{ base: 120, md: 100 }}
                flex="none"
              />
              <Stack gap="xxs" maw={565}>
                <Title order={3} fw={700} fz={{ base: 'md', md: 'lg' }} lh="xs">
                  Find your perfect property with our buyer's agents
                </Title>
                <Text size="sm">
                  Streamline the home buying process, let us search over 11 million properties,
                  analyse suburbs and expertly negotiate for you.
                </Text>
              </Stack>
            </Flex>
            <Box pl={{ base: 0, md: 'lg' }} w={{ base: '100%', md: 'fit-content' }}>
              <LinkButtonWithTracking
                label="Learn More"
                href="/buyers-agent/"
                variant="secondary"
                w={{ base: '100%', md: 160 }}
                position={position}
              />
            </Box>
          </Flex>
        </Center>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
