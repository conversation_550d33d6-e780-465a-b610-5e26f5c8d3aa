'use client';

import { Box, Container, Divider, Group, Stack, Text, Title } from '@mantine/core';
import dayjs from 'dayjs';
import { Status, useSession } from '@lendi/lala-react';

import { Property } from '@gp/data-access/property-hub';
import { Image } from '@gp/ui/components';

export default function EventTimesV2(property: Property) {
  // TODO: add inspection date times when available
  const { forSale, auctionDateTime } = property.data;
  const { status } = useSession();

  const isAuthenticated = status === Status.Authenticated;

  if (!isAuthenticated || !forSale || !auctionDateTime) return null;

  return (
    <Container
      fluid
      color="white"
      p="lg"
      style={{ borderColor: '#F2F2F0', borderWidth: 1, borderRadius: 6 }}
    >
      <Stack gap="sm">
        <Stack>
          <Title order={4} fw={700}>
            Auction
          </Title>
          <Group
            gap="sm"
            p="sm"
            style={{
              border: '1px solid #E4E4E2',
              textAlign: 'left',
              borderRadius: 6,
            }}
          >
            <Box w={18} h={18}>
              <Image src={`property-hub/calendar.svg`} />
            </Box>
            <Stack gap="xxxs">
              <Text fw={700}>{dayjs(auctionDateTime).format('ddd, MMM D')}</Text>
              <Text>{dayjs(auctionDateTime).format('h:mma')}</Text>
            </Stack>
          </Group>
        </Stack>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
