'use client';

import { Box, Container, Divider, Stack, Text, Timeline } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import type { PropertyHistory } from '@gp/data-access/property-hub';
import { Property } from '@gp/data-access/property-hub';
import { Collapse, SectionHeader } from '@gp/ui/components';

import { HistoryItem } from '../HistoryItem';

export default function PropertyHistoryV2(property: Property) {
  const dbTimeline = property.data.timeline;
  const [opened, { toggle, close, open }] = useDisclosure(true);

  if (!dbTimeline || dbTimeline.length === 0) return null;

  const timeline = dbTimeline.filter((t) => t.date && t.type) as Required<PropertyHistory>[];

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Stack gap="32">
          <SectionHeader
            title="Property history"
            order={2}
            size="h4"
            description="A breakdown of how regularly this property entered and exited the market for sale or lease."
            info={
              <Stack gap="md" p="xs">
                <Text>
                  This section gives you a clear overview of the property's past. You can see when
                  it was listed, sold, withdrawn without a sale and its leasing history.
                </Text>
                <Text>
                  If the property sold for more or less than the listing price, it could indicate
                  demand for property at that point in time, or level of buyer and renter interest.
                  A steady rise in sold prices could mean the property is a strong investment.
                </Text>
              </Stack>
            }
          />
          <Box px="md">
            <Timeline active={-1}>
              {timeline.slice(0, 4).map((history, index) => (
                <HistoryItem
                  key={`property-history-${index}`}
                  {...history}
                  hideLine={index === 3 && !opened && timeline.length > 4}
                />
              ))}
              {timeline.length > 4 && (
                <Collapse
                  in={false}
                  expandText="See more"
                  collapseText="See less"
                  data-testid="property-history-expand-collapse"
                  type="Property"
                  name={`Property - ${property.address}`}
                  position="Property History"
                  disclosure={[opened, { toggle, close, open }]}
                >
                  <Stack gap={0} mt="lg">
                    {timeline.slice(4).map((history, index) => (
                      <HistoryItem key={`property-history-${index + 4}`} {...history} />
                    ))}
                  </Stack>
                </Collapse>
              )}
            </Timeline>
          </Box>
        </Stack>
        <Divider color="gray.1" />
      </Stack>
    </Container>
  );
}
