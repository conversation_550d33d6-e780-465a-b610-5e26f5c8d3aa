'use client';

import {
  Badge,
  Center,
  Container,
  Divider,
  Flex,
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { usePathname } from 'next/navigation';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { getPropertyHubAPIs, type Property } from '@gp/data-access/property-hub';
import { Image, LinkButtonWithTracking, SectionHeader } from '@gp/ui/components';
import { Unlock } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

export default function PropertyZoneAndRisksV2(property: Property) {
  const { corelogicId } = property;
  const { status, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { gpUnauthPropertySearch } = useFlags();
  const { getPropertyZoneAndRisks } = getPropertyHubAPIs(token);
  const path = usePathname();
  const loginUrl = embedMarketingTrackingParams(`/sign-in/${generateReturnURL([path])}`);

  const { data: zoneAndRisks, isLoading } = useSWRImmutable(
    isAuthenticated && token && corelogicId ? `${corelogicId}-zone-and-risks` : null,
    async () => await getPropertyZoneAndRisks(corelogicId)
  );

  if (!corelogicId || isLoading) return null;
  const { bushfire, flood, heritage, zoneCode, zoneDescription } = zoneAndRisks ?? {};

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            title="Planning overlays and zoning"
            order={3}
            size="h4"
            description="Overlays and zoning could mean potential building restrictions or standards due to environmental impacts or government regulations."
            info={
              <Stack gap="md" p="xs">
                <Stack gap="xxs">
                  <Title order={5}>Bushfire overlay</Title>
                  <Text>
                    Properties in bushfire-prone areas must meet strict building regulations to
                    reduce risk. This includes creating defendable space around structures, ensuring
                    access to water and meeting construction standards.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Flood overlay</Title>
                  <Text>
                    Flood overlays indicate areas at risk of riverine or stormwater flooding.
                    Properties in these zones often require raised foundations or specialised
                    drainage solutions to avoid damage. Development in high-risk areas may be
                    limited, and understanding the flood risk helps assess long-term costs and
                    safety.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Heritage overlay</Title>
                  <Text>
                    Heritage-listed properties are protected due to their historical or cultural
                    significance. This means any renovations or developments must adhere to specific
                    guidelines to preserve the property's character. Understanding the heritage
                    status is key to anticipating potential limitations or costs.
                  </Text>
                  <Text>
                    Knowing there may be certain building standards or restrictions to comply with
                    may help you estimate cost and effort if you have future intentions of
                    renovating this property or building in place of it.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Zoning</Title>
                  <Text>
                    Zoning defines how a property can be used or developed, this may include
                    specific regulations like building height limits or density requirements.
                    Knowing the zoning may assist with your research when looking into renovation or
                    building regulations.
                  </Text>
                </Stack>
              </Stack>
            }
          />
          {isAuthenticated || gpUnauthPropertySearch ? (
            <SimpleGrid cols={{ base: 1, md: 2 }}>
              <Paper p="sm">
                <Stack>
                  <Group justify="space-between">
                    <Title order={5} tt="capitalize">
                      Bushfire
                    </Title>
                    <Badge variant={bushfire ? 'light-green' : 'gray'} size="xs">
                      {bushfire ? 'Detected' : 'Not detected'}
                    </Badge>
                  </Group>
                  <Group justify="flex-start" w="100%" gap="sm" wrap="nowrap">
                    <Image src="property-hub/bushfire-risk.svg" />
                    <Text fz={{ base: 'xs', md: 'sm' }} c="gray.6">
                      We {bushfire ? 'have' : "haven't"} detected bushfire overlays on this
                      property.
                    </Text>
                  </Group>
                </Stack>
              </Paper>
              <Paper p="sm">
                <Stack>
                  <Group justify="space-between">
                    <Title order={5} tt="capitalize">
                      Flood
                    </Title>
                    <Badge variant={flood ? 'light-green' : 'gray'} size="xs">
                      {flood ? 'Detected' : 'Not detected'}
                    </Badge>
                  </Group>
                  <Group justify="flex-start" w="100%" gap="sm" wrap="nowrap">
                    <Image src="property-hub/flood-risk.svg" />
                    <Text fz={{ base: 'xs', md: 'sm' }} c="gray.6">
                      We {flood ? 'have' : "haven't"} detected flood overlays on this property.
                    </Text>
                  </Group>
                </Stack>
              </Paper>
              <Paper p="sm">
                <Stack>
                  <Group justify="space-between">
                    <Title order={5} tt="capitalize">
                      Heritage
                    </Title>
                    <Badge variant={heritage ? 'light-green' : 'gray'} size="xs">
                      {heritage ? 'Detected' : 'Not detected'}
                    </Badge>
                  </Group>
                  <Group justify="flex-start" w="100%" gap="sm" wrap="nowrap">
                    <Image src="property-hub/heritage-risk.svg" />
                    <Text fz={{ base: 'xs', md: 'sm' }} c="gray.6">
                      We {heritage ? 'have' : "haven't"} detected heritage overlays on this
                      property.
                    </Text>
                  </Group>
                </Stack>
              </Paper>
              <Paper p="sm">
                <Stack>
                  <Group justify="space-between">
                    <Title order={5} tt="capitalize">
                      Zoning
                    </Title>
                    <Badge variant={zoneCode ? 'light-green' : 'gray'} size="xs">
                      {zoneCode ? 'Detected' : 'Not detected'}
                    </Badge>
                  </Group>
                  <Group justify="flex-start" w="100%" gap="sm" wrap="nowrap">
                    <Image src="property-hub/heritage-risk.svg" />
                    <Text fz={{ base: 'xs', md: 'sm' }} c="gray.6">
                      We {zoneCode ? 'have' : "haven't"} detected {zoneCode ?? 'zone code'}{' '}
                      {zoneDescription ? `(${zoneDescription})` : ''} on this property.
                    </Text>
                  </Group>
                </Stack>
              </Paper>
            </SimpleGrid>
          ) : (
            <Center h={150}>
              <Stack gap="xxs" align="center">
                <Group gap="xxs">
                  <Unlock size={18} />
                  <Title order={4} size="h6">
                    Bring your property plans to life
                  </Title>
                </Group>
                <Text>Log in to access essential zoning details for building or renovating.</Text>
                <LinkButtonWithTracking
                  w={108}
                  size="sm"
                  href={loginUrl}
                  label="Log in"
                  position="Property Government Planning"
                  type="Property"
                  name={`Property - ${property.address}`}
                />
              </Stack>
            </Center>
          )}
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
