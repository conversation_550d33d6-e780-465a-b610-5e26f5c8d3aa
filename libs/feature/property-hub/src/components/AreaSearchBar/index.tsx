'use client';

import { AuthenticationStatus, EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';
import { Badge, Group } from '@mantine/core';
import { useMediaQuery, useScrollIntoView } from '@mantine/hooks';
import { useRouter, useSearchParams } from 'next/navigation';
import { useContext } from 'react';

import {
  type CustomerPropertySearch,
  getPropertyHubAPIs,
  SEARCH_RESULT_TYPE,
  type SearchResult,
} from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { Autocomplete } from '@gp/ui/components';
import { Plus, Search } from '@gp/ui/icons';
import { setAreaReportLead } from '@gp/util/leads';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

import { useSearchHistory } from '../../hooks/useSearchHistory';

import classes from './style.module.css';

const searchCache: Record<string, SearchResult[]> = {};

const getSearchDescription = (type: SearchResult['type']) => {
  switch (type) {
    case 'state':
      return 'State';
    case 'council':
      return 'Council Area';
    case 'suburb':
      return 'Suburb';
    case 'address':
      return 'Address';
  }
};

const getSearchResult = async (searchQuery?: string, searchType?: SEARCH_RESULT_TYPE) => {
  const { search } = getPropertyHubAPIs();
  let result: SearchResult[];
  if (!searchQuery || searchQuery.length < 3) {
    result = [];
  } else if (searchQuery in searchCache) {
    result = searchType
      ? searchCache[searchQuery].filter(({ type }) => type === searchType)
      : searchCache[searchQuery];
  } else {
    result = await search(searchQuery, 10, searchType ? [searchType] : undefined);
    searchCache[searchQuery] = result;
  }

  return result.map(({ id, name, slug, type }) => ({
    id,
    label: name,
    value: slug,
    description: getSearchDescription(type),
    areaType: type,
    icon: <Plus size={16} color="gray.9" />,
  }));
};

interface AreaSearchBarProps {
  autoScrollOffset?: number;
  withBetaBadge?: boolean;
  searchType?: SEARCH_RESULT_TYPE;
  shouldSetCustomerLocation?: boolean;
}

export default function AreaSearchBar({
  autoScrollOffset = 120,
  withBetaBadge = true,
  searchType,
  shouldSetCustomerLocation = false,
}: AreaSearchBarProps) {
  const isConversion = useSearchParams().get('conversion')?.toLowerCase() === 'true';
  const { status } = useSession();
  const isMobile = useMediaQuery('(max-width: 36em)');
  const isAuthenticated = status === Status.Authenticated;

  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const router = useRouter();
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLInputElement>({
    offset: autoScrollOffset,
    duration: 300,
  });

  const { addSearchHistory } = useSearchHistory();
  const { analytics } = useContext(LDContext);

  return (
    <Group gap="xs" className={classes.searchBar}>
      <Autocomplete
        data-testid="search-field-autocomplete"
        id="search-bar"
        variant="unstyled"
        ref={targetRef}
        flex={1}
        options={(keyword) => getSearchResult(keyword, searchType)}
        leftSection={<Search />}
        placeholder={
          searchType
            ? `Search for a ${
                searchType === SEARCH_RESULT_TYPE.ADDRESS
                  ? 'property'
                  : searchType.toLocaleLowerCase()
              }`
            : 'Search for an address, suburb or state'
        }
        keepSearchText
        minSearchLength={3}
        noResults={{
          value: 'No result',
          label: 'No results found',
          description: `Try searching for another ${
            searchType === SEARCH_RESULT_TYPE.ADDRESS ? 'property' : searchType?.toLocaleLowerCase()
          }`,
        }}
        onSelectOption={({ id, value, label, areaType }) => {
          console.log('-------:::::');
          const path = `/property/${value}`;

          // Create search criteria object
          const propertySearchCriteria = {
            label,
            queryString: value,
            // Not to be mistaken as SEARCH_RESULT_TYPE
            searchType: undefined,
            // actual SEARCH_RESULT_TYPE, renamed to areaType to differentiate
            searchCategory: areaType,
          } as CustomerPropertySearch;

          // Save search to history
          addSearchHistory(propertySearchCriteria);

          if (
            typeof window !== 'undefined' &&
            shouldSetCustomerLocation &&
            searchType === SEARCH_RESULT_TYPE.SUBURB &&
            !isAuthenticated
          ) {
            window?.sessionStorage.setItem(
              'GP_CUSTOMER_LOCATION',
              JSON.stringify({
                suburb: label.split(', ')[0],
                postcode: label.split(', ')[1].split(' ')[1],
              })
            );
            setAreaReportLead();
            const loginUrl = embedMarketingTrackingParams(`/sign-in/${generateReturnURL([path])}`);
            router.push(loginUrl);
          } else if (isConversion && !isAuthenticated) {
            if ((value.match(/\//g) || []).length < 2) {
              setAreaReportLead();
              const loginUrl = embedMarketingTrackingParams(
                `/sign-in/${generateReturnURL([path])}`
              );
              router.push(loginUrl);
            } else {
              if (!id) return;
              const propertyRelationshipUrl = `/my-properties/relationship/?propertyId=${id}&propertyAddress=${encodeURIComponent(
                label
              )}&propertyPath=${encodeURIComponent(path)}`;
              router.push(propertyRelationshipUrl);
            }
          } else {
            router.push(path);
          }
        }}
        onFocus={() => isMobile && scrollIntoView()}
        onClick={() =>
          analytics?.trackEvent({
            event_name: 'Search Bar Clicked',
            category: EventCategory.AUSSIE_HOMES,
            text: 'Search',
            position: 'Property Search Bar',
            authenticationStatus,
          })
        }
      />
      {withBetaBadge && (
        <Badge variant="green" size="sm" tt="uppercase">
          Beta
        </Badge>
      )}
    </Group>
  );
}
