'use client';

import {
  Container,
  Divider,
  Flex,
  List,
  ListItem,
  SimpleGrid,
  Stack,
  Text,
  Title,
} from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import { Card, SectionHeader } from '@gp/ui/components';
import { formatNumber } from '@gp/util/intl';

export default function MarketActivityV2(props: State | Council | Suburb | Property) {
  const isProperty = 'address' in props;

  const {
    houseMedianDaysOnMarket,
    unitMedianDaysOnMarket,
    soldLast12Months,
    propertiesAvailableLastMonth,
  } = isProperty ? props.suburb.data : props.data;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            title="Market activity"
            order={3}
            size="h4"
            description="A snapshot of how quickly properties are selling in the area and how many are currently available."
            info={
              <Stack gap="md" p="xs">
                <Text>
                  This section may help you assess how competitive an area is due the amount of
                  properties available and how quickly they sell.
                </Text>
                <Stack gap="xxs">
                  <Title order={5}>Median days on market</Title>
                  <Text>
                    How quickly properties are staying on the market, which can indicate the level
                    of competition amongst buyers.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Properties sold in the last 12 months</Title>
                  <Text>
                    This may indicate the likelihood of properties being put up for sale in this
                    area based on the last 12 months of activity.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Properties available in the last month</Title>
                  <Text>
                    This may indicate your chances of securing a property in the area, showing you
                    how many are currently available.
                  </Text>
                </Stack>
                <Text>These together may give you an indication of:</Text>
                <List>
                  <ListItem>How popular and competitive the area is</ListItem>
                  <ListItem>
                    Potential for properties to be sold over valuation due to high demand
                  </ListItem>
                  <ListItem>
                    Possible negotiating power depending on if it's a buyer's or seller's market
                  </ListItem>
                </List>
              </Stack>
            }
          />
          <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }}>
            <Card
              title={
                houseMedianDaysOnMarket ? `${formatNumber(houseMedianDaysOnMarket)} Days` : 'N/A'
              }
              order={4}
              description="House median days on market"
              image="property-hub/days-on-market.svg"
            />
            <Card
              title={
                unitMedianDaysOnMarket ? `${formatNumber(unitMedianDaysOnMarket)} Days` : 'N/A'
              }
              order={4}
              description="Unit median days on market"
              image="property-hub/days-on-market.svg"
            />
            <Card
              title={soldLast12Months ? `${formatNumber(soldLast12Months)} Properties` : 'N/A'}
              order={4}
              description="Sold in last 12 months"
              image="property-hub/sold-properties.svg"
            />
            <Card
              title={
                propertiesAvailableLastMonth
                  ? `${formatNumber(propertiesAvailableLastMonth)} Properties`
                  : 'N/A'
              }
              order={4}
              description="Available in the last month"
              image="property-hub/available-properties.svg"
            />
          </SimpleGrid>
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
