'use client';

import { Container, Image, Paper, ScrollArea, SimpleGrid, Stack, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';

import { ButtonWithTracking, ModalWithTracking } from '@gp/ui/components';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

interface CreateAccountModalProps {
  opened: boolean;
  disclosure: [boolean, { openedCreateAccountModal: boolean; closeCreateAccountModal: () => void }];
  address?: string;
  propertyId?: string;
  photo?: string | null;
}

export default function CreateAccountModal({
  address,
  propertyId,
  photo,
  disclosure,
}: CreateAccountModalProps) {
  const [openedCreateAccountModal, { closeCreateAccountModal }] = disclosure;
  const isMobile = useMediaQuery('(max-width: 36em)');
  const handleButtonClick = () => {
    const link = embedMarketingTrackingParams(
      `/sign-in/${generateReturnURL([
        `${window.location.pathname}${window.location.search}&trackProperty=${propertyId}`,
      ])}`
    );
    window.location.assign(link);
  };
  return (
    <ModalWithTracking
      opened={openedCreateAccountModal}
      title="Create an account"
      name="Create account"
      onClose={closeCreateAccountModal}
      fullScreen={isMobile}
      size={600}
      scrollAreaComponent={ScrollArea.Autosize}
      centered
      footer={
        <SimpleGrid cols={{ base: 1 }}>
          <ButtonWithTracking
            flex={1}
            variant="secondary"
            fullWidth={!isMobile}
            label="Sign in"
            onClick={handleButtonClick}
          />
          <ButtonWithTracking
            flex={1}
            fullWidth={!isMobile}
            label="Create account"
            onClick={handleButtonClick}
          />
        </SimpleGrid>
      }
    >
      <Container fluid p="md">
        <Stack gap="sm">
          <Stack gap="xs">
            <Paper>
              <Image
                height="240px"
                src={photo}
                alt={`${address} property image`}
                loading="lazy"
                style={{ borderRadius: '6px' }}
              />
            </Paper>
            <Title ta="center" size="h5">
              {address}.<br />
              Don’t miss out. Create your account.
            </Title>
          </Stack>

          <Text>
            <Text>Join Aussie and experience the benefits today:</Text>
            <ul>
              <li>Get immediate updates on this home.</li>
              <li>Get recommendations for similar homes.</li>
              <li>Thousands of unlisted properties exclusive to Aussie.</li>
            </ul>
          </Text>
        </Stack>
      </Container>
    </ModalWithTracking>
  );
}
