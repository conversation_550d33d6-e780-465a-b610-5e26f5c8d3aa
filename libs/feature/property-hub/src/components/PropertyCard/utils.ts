//This function determines whether a color (specified as a hexadecimal string) is considered "dark" or "light" based on its perceived brightness
export const isColorDark = (hex: string): boolean => {
  const cleanedHex = hex.replace(/^#/, '');
  const bigint = parseInt(cleanedHex, 16);

  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  // Perceived brightness formula (YIQ)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness < 128; // <128 is dark, >=128 is light
};
