'use client';

import { useContext, useMemo, useState } from 'react';
import { CarouselSlide } from '@mantine/carousel';
import { Badge, Box, Center, Flex, Group, Paper, Stack, Text, Title } from '@mantine/core';
import { useSessionStorage, useViewportSize } from '@mantine/hooks';
import useSWRImmutable from 'swr/immutable';
import { AuthenticationStatus, EventCategory } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import { LISTING_SEARCH_PROPERTY_TYPE } from '@gp/data-access/corelogic';
import { getPropertyHubAPIs, LISTING_TYPE, type Property } from '@gp/data-access/property-hub';
import { LDContext } from '@gp/shared/launchdarkly';
import { Carousel, Image } from '@gp/ui/components';
import {
  BathroomOutlined,
  BedroomOutlined,
  CarspaceOutlined,
  LandsizeOutlined,
} from '@gp/ui/icons';

import PropertyTrack from '../../../PropertyTrack';
import { isColorDark } from '../../utils';

import classes from './style.module.css';

function toTitleCase(str: string) {
  return str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
}

interface ForSalePropertyCardPropsV2 {
  property: Pick<Property, 'id' | 'slug' | 'data' | 'corelogicId'>;
  isCarousel?: boolean;
}
export default function ForSalePropertyCardV2({
  property,
  isCarousel,
}: ForSalePropertyCardPropsV2) {
  const { status, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const { width } = useViewportSize();
  const isMobile = useMemo(() => width < 576, [width]);
  const { id, slug, data, corelogicId } = property;

  const {
    type,
    primaryAddress,
    streetAddress,
    suburb,
    state,
    postcode,
    bedrooms,
    bathrooms,
    carSpaces,
    photo,
    listingDescription,
    listingType,
    realEstateAgents,
    floorArea,
    landArea,
    campaignId,
  } = data;
  const agentData = realEstateAgents?.[0] || {};
  const { photo: agentPhoto, name: agentName, agency } = agentData;
  const { name: agencyName, logo, brand } = agency || {};
  const isExclusiveListing =
    listingType === LISTING_TYPE.PRE_MARKET || listingType === LISTING_TYPE.OFF_MARKET;
  const isPreMarket = listingType === LISTING_TYPE.PRE_MARKET;
  const [photos, setPhotos] = useState<string[]>();
  const brandPrimary = brand?.primary;
  const { getPropertyImages } = getPropertyHubAPIs(token);
  const { data: imagery, isLoading } = useSWRImmutable(
    isAuthenticated && corelogicId ? `${corelogicId}-property-images` : null,
    async () => await getPropertyImages(corelogicId)
  );
  const [, setResultPageReferrer] = useSessionStorage<string>({
    key: 'GP_RESULT_PAGE_REFERRER',
    getInitialValueInEffect: undefined,
    defaultValue: '',
  });
  const link = campaignId ? `/property/${slug}/?listingId=${campaignId}` : `/property/${slug}/`;
  useMemo(() => {
    if (!isLoading && imagery?.photos && imagery.photos.length > 0) {
      setPhotos(imagery.photos);
    } else {
      setPhotos(
        (data.photos && data.photos.length > 0 ? data.photos : undefined) ||
          (data.photo ? [data.photo] : [])
      );
    }
  }, [isLoading, imagery?.photos, data.photos, data.photo]);

  const { analytics } = useContext(LDContext);

  const handleCardClick = () => {
    analytics?.trackEvent({
      event_name: 'Link Clicked',
      text: `Property - ${data.primaryAddress}`,
      category: EventCategory.AUSSIE_HOMES,
      position: `Listing search result card`,
      authenticationStatus,
    });

    if (typeof window !== 'undefined') {
      if (window.location.pathname.includes('results')) {
        setResultPageReferrer(slug);
      }
    }
    window.location.href = link;
  };

  const areaSize =
    type === LISTING_SEARCH_PROPERTY_TYPE.APARTMENT ||
    type === LISTING_SEARCH_PROPERTY_TYPE.TOWNHOUSE ||
    type === LISTING_SEARCH_PROPERTY_TYPE.UNIT
      ? floorArea
      : landArea;

  return (
    <Paper
      radius="6px"
      shadow="xs"
      // 416px converts to 364px on mobile due to 14/16
      w={{ base: '416px', sm: '100%' }}
      maw={{ base: '416px', xs: '364px', sm: 'unset' }}
      h="100%"
      withBorder={false}
      className={classes.hoverCard}
      onClick={handleCardClick}
    >
      <Stack gap="0" style={{ borderRadius: '6px', borderColor: 'gray.1' }} bg="#FFFFFF">
        {isExclusiveListing && (
          <Box>
            <Group
              py={4}
              pr="sm"
              pl="xs"
              w="100%"
              bg={brandPrimary || 'transparent'}
              wrap="nowrap"
              justify="space-between"
              style={{
                borderTopLeftRadius: '6px',
                borderTopRightRadius: '6px',
                position: 'absolute',
                top: '0',
                zIndex: '1',
              }}
            >
              {logo && (
                <Image
                  src={logo}
                  alt={agencyName || 'Agency logo'}
                  mah={32}
                  maw={90}
                  loading="lazy"
                  style={{ objectFit: 'contain' }}
                />
              )}
              <Group justify="flex-end" wrap="nowrap" gap={isMobile ? 'xxs' : 'sm'}>
                {agentPhoto && agentName && (
                  <Image
                    src={agentPhoto}
                    alt={agentName || 'Agent photo'}
                    height={32}
                    width={32}
                    radius="100%"
                    loading="lazy"
                  />
                )}
                <Stack h={32} gap={0} justify="center">
                  {agentName && (
                    <Text
                      size={'sm'}
                      c={brandPrimary && isColorDark(brandPrimary) ? 'white' : 'gray.9'}
                      h={isMobile ? '17.5' : 20}
                      fw={700}
                      lineClamp={0}
                      style={{ whiteSpace: 'nowrap' }}
                    >
                      {agentName}
                    </Text>
                  )}
                </Stack>
              </Group>
            </Group>
          </Box>
        )}
        <Box
          style={{
            position: 'relative',
            borderBottom: '1px solid #F2F2F0',
            overflow: 'hidden',
          }}
        >
          {isCarousel && photos && photos.length > 0 ? (
            <Carousel
              slideSize={100}
              controlPosition="center"
              pageIndicatorPosition="hidden"
              controlsOffset="16px"
              controlSize="32px"
              variant="hover"
              loop
              h={{ base: '293px', md: '270px', lg: '270px', xl: '270px' }}
              bg="#F2F2F0"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {photos &&
                photos.slice(0, 10).map((url, index) => (
                  <CarouselSlide key={`slide-${index}`}>
                    <Image
                      style={{
                        borderTopLeftRadius: '6px',
                        borderTopRightRadius: '6px',
                      }}
                      src={url}
                      h="100%"
                      w="100%"
                      alt={`${primaryAddress} - Image ${index + 1}`}
                      loading="lazy"
                      fit="cover"
                      display="block"
                      onClick={handleCardClick}
                    />
                  </CarouselSlide>
                ))}
            </Carousel>
          ) : (
            <Box
              style={{
                borderTopLeftRadius: '6px',
                borderTopRightRadius: '6px',
              }}
              bg="#F2F2F0"
            >
              {photo ? (
                <Image
                  style={{
                    borderTopLeftRadius: '6px',
                    borderTopRightRadius: '6px',
                  }}
                  h={{ base: '293px', md: '201px', lg: '201px', xl: '274px' }}
                  w="100%"
                  src={photo}
                  alt={`Property at ${primaryAddress}`}
                  loading="lazy"
                  fit="cover"
                  display="block"
                  onClick={handleCardClick}
                />
              ) : (
                <Box>
                  <Center
                    h={{ base: '293px', md: '201px', lg: '201px', xl: '274px' }}
                    w="100%"
                    bg="#F2F2F0"
                    onClick={handleCardClick}
                  >
                    <Text c="gray.7" fw={700}>
                      No images available
                    </Text>
                  </Center>
                </Box>
              )}
            </Box>
          )}
          {isExclusiveListing && (
            <Badge
              variant="yellow"
              c="#1D1D1D"
              size="xs"
              tt="capitalize"
              style={{
                position: 'absolute',
                bottom: '12px',
                left: '8px',
                zIndex: '1',
                border: '1px solid #E6DBAE',
              }}
            >
              {isPreMarket ? 'Pre-market' : 'Off-market'}
            </Badge>
          )}
          {primaryAddress && (
            <Box
              style={{
                position: 'absolute',
                top: `${isExclusiveListing && brandPrimary ? '32px' : '6px'}`,
                right: '4px',
                zIndex: '1',
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <PropertyTrack
                propertyId={id.toString()}
                propertyAddress={primaryAddress}
                variant="searchResult"
                photo={photo}
                iconOnly
              />
            </Box>
          )}
        </Box>
        <Box>
          <Stack gap="4px" px="12px" pt="6px" pb="12px">
            <Title c={'black'} component="p" fz="16px" lh="24px" lineClamp={1} fw="700">
              {listingDescription || 'Contact Agent'}
            </Title>
            <Flex gap="xs" justify="space-between">
              <Stack gap="6px" miw={0}>
                {streetAddress && (
                  <Text size="sm" c="black" lineClamp={1}>
                    {streetAddress}
                  </Text>
                )}
                {suburb && state && postcode && (
                  <Text size="sm" c="black" lineClamp={1}>
                    {suburb}, {state} {postcode}
                  </Text>
                )}
              </Stack>
              <Stack gap="4px" flex={{ base: '1 1 60%', md: '1 1 40%' }}>
                <Flex gap={{ base: '4px', lg: '14px' }} justify="flex-end" wrap="nowrap">
                  <Group gap="xxxs">
                    <BedroomOutlined size={20} fill="transparent" />
                    <Text fz="14px" lh="20px">{`${bedrooms ?? '-'}`}</Text>
                  </Group>
                  <Group gap="xxxs">
                    <BathroomOutlined size={20} fill="transparent" />
                    <Text fz="14px" lh="20px">{`${bathrooms ?? '-'}`}</Text>
                  </Group>
                  <Group gap="xxxs">
                    <CarspaceOutlined size={20} fill="transparent" />
                    <Text fz="14px" lh="20px">{`${carSpaces ?? '-'}`}</Text>
                  </Group>
                </Flex>
                <Flex gap={{ base: '4px', lg: '8px' }} align="top" wrap="nowrap" justify="flex-end">
                  {!!areaSize && areaSize > 0 && (
                    <>
                      <Group gap="xxxs" wrap="nowrap">
                        <LandsizeOutlined size={20} fill="transparent" />
                        <Text fz="14px" lh="20px" c="#000000">
                          {areaSize} m²
                        </Text>
                      </Group>
                      <Text fz="14px" lh="20px" c="#000000">
                        •
                      </Text>
                    </>
                  )}
                  <Text fz="14px" lh="20px" c="#000000">
                    {toTitleCase(type)}
                  </Text>
                </Flex>
              </Stack>
            </Flex>
          </Stack>
        </Box>
      </Stack>
    </Paper>
  );
}
