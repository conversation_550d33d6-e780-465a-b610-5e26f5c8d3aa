'use client';

import { Box, Container, Divider, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import type { Property } from '@gp/data-access/property-hub';
import { Collapse, SectionHeader } from '@gp/ui/components';

import classes from '../style.module.css';

export default function PropertyDescriptionV2(property: Property) {
  const { listedDate, propertyDescription } = property.data;
  const [opened, { toggle, close, open }] = useDisclosure(true);

  if (!propertyDescription) return null;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Stack gap="md">
          <SectionHeader
            title="Property Description"
            order={3}
            size="h4"
            description={
              listedDate
                ? `Listed on ${new Date(listedDate).toLocaleDateString('en-AU', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                  })}`
                : null
            }
          />

          <Box>
            {!opened && (
              <Box
                h="72px"
                style={{
                  WebkitLineClamp: 3,
                }}
                className={classes.clamp}
              >
                <Text dangerouslySetInnerHTML={{ __html: propertyDescription }} />
              </Box>
            )}
            <Collapse
              in={false}
              expandText="See more"
              collapseText="See less"
              data-testid="property-description-expand-collapse"
              type="Property"
              name={`Property - ${property.address}`}
              position="Property Description"
              disclosure={[opened, { toggle, close, open }]}
            >
              <Text dangerouslySetInnerHTML={{ __html: propertyDescription }} />
            </Collapse>
          </Box>
        </Stack>
        <Divider color="gray.1" />
      </Stack>
    </Container>
  );
}
