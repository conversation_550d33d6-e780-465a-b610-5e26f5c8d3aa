'use client';

import {
  Badge,
  Center,
  Container,
  Divider,
  Flex,
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title,
  TypographyStylesProvider,
} from '@mantine/core';
import { usePathname } from 'next/navigation';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { AVM_CONFIDENCE } from '@gp/data-access/corelogic';
import { getPropertyHubAPIs, type Property } from '@gp/data-access/property-hub';
import { LinkButtonWithTracking, SectionHeader } from '@gp/ui/components';
import { formatCurrency, formatLargeNumber } from '@gp/util/intl';

import PropertyEnquiry from '../../PropertyEnquiry';
import { PropertyDetails } from '../../PropertyEnquiry/EnquiryModal';

const getConfidenceBadge = (confidence: AVM_CONFIDENCE) => {
  const badgeVariant = (() => {
    switch (confidence) {
      case AVM_CONFIDENCE.HIGH:
        return 'light-green';
      case AVM_CONFIDENCE.MEDIUM_HIGH:
      case AVM_CONFIDENCE.MEDIUM:
        return 'light-orange';
      case AVM_CONFIDENCE.MEDIUM_LOW:
      case AVM_CONFIDENCE.LOW:
        return 'light-red';
    }
  })();
  return (
    <Badge size="sm" variant={badgeVariant}>
      {confidence.toLocaleLowerCase().split('_').join(' ')} confidence
    </Badge>
  );
};

export default function ValuationV2(property: Property) {
  const { corelogicId, address, id, suburb, data, slug } = property;
  const {
    forSale,
    state,
    informationStatement,
    primaryAddress,
    streetAddress,
    listingLoopId,
    realEstateAgents,
    type,
  } = data;
  const { status, token, identity } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const customerId = identity?.id || '';

  const path = usePathname();
  const loginUrl = `/my-properties/relationship/?propertyId=${id}&propertyAddress=${encodeURIComponent(
    address
  )}&propertyPath=${encodeURIComponent(path)}`;

  const { getAVMDisclaimers, getPropertyAVM } = getPropertyHubAPIs(token);

  const { data: disclaimers } = useSWRImmutable(
    token ? 'avm-disclaimer' : null,
    async () => await getAVMDisclaimers()
  );

  const { data: avm } = useSWRImmutable(
    isAuthenticated && token && corelogicId ? `${corelogicId}-avm` : null,
    async () => await getPropertyAVM(corelogicId)
  );

  if (!corelogicId) return null;

  if (!isAuthenticated)
    return (
      <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
        <Stack gap="sm">
          <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
            <SectionHeader
              title="Estimated value"
              order={3}
              size="h4"
              description="Based on comparable sales, past sales and available property data"
            />
            <SimpleGrid spacing="md" cols={{ base: 1, sm: 2 }}>
              <Paper p="md">
                <Flex
                  direction="column"
                  gap="sm"
                  justify={'space-between'}
                  align={'center'}
                  h="100%"
                >
                  <Text size="sm">Property valuation, estimate, range and confidence score</Text>
                  <LinkButtonWithTracking
                    label="Calculate property value"
                    useNextLink
                    href={loginUrl}
                    variant="primary"
                    size="sm"
                    w="auto"
                    position="Property Valuation CTA Panel"
                    type="Property"
                    name={`Property - ${address}`}
                  />
                </Flex>
              </Paper>
              <Paper p="md">
                <Flex
                  direction="column"
                  gap="sm"
                  justify={'space-between'}
                  align={'center'}
                  h="100%"
                >
                  <Text size="sm">Rental income estimate and range</Text>
                  <LinkButtonWithTracking
                    label="Calculate rental estimate"
                    useNextLink
                    href={loginUrl}
                    variant="primary"
                    size="sm"
                    w="auto"
                    position="Property Valuation CTA Panel"
                    type="Property"
                    name={`Property - ${address}`}
                  />
                </Flex>
              </Paper>
            </SimpleGrid>
          </Flex>
          <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
        </Stack>
      </Container>
    );

  if (!avm) return null;

  const { estimatedRent, estimatedValue } = avm || {};

  const propertyValueComparison =
    type === 'House'
      ? estimatedValue?.estimate - suburb.data.houseMedianPrice
      : type === 'Unit'
      ? estimatedValue?.estimate - suburb.data.unitMedianPrice
      : undefined;

  const propertyValueComparisonPercentage =
    propertyValueComparison && type === 'House'
      ? (propertyValueComparison / suburb.data.houseMedianPrice) * 100
      : propertyValueComparison && type === 'Unit'
      ? (propertyValueComparison / suburb.data.unitMedianPrice) * 100
      : undefined;

  const getComparisonMessage = (propertyValueComparisonPercentage: number) => {
    const message = (() => {
      if (propertyValueComparisonPercentage <= -10) {
        return 'Looks like a great deal!';
      } else if (
        propertyValueComparisonPercentage > -10 &&
        propertyValueComparisonPercentage <= 10
      ) {
        return "Fairly priced in today's market";
      } else if (propertyValueComparisonPercentage > 10) {
        return 'Consider negotiating';
      } else {
        return "Fairly priced in today's market";
      }
    })();
    return (
      <Title order={5} size="lg" c="gray.9" ta="center">
        {message}
      </Title>
    );
  };

  const showPropertyEnquiry =
    !!propertyValueComparisonPercentage && forSale && (state !== 'VIC' || informationStatement);

  const hasEstimate = estimatedValue?.estimate || estimatedRent?.estimate;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            title="Valuation estimates"
            order={3}
            size="h4"
            info={
              <TypographyStylesProvider>
                <Text mt="sm" mb="xs">
                  <b>AVM estimate</b>
                </Text>
                <div dangerouslySetInnerHTML={{ __html: disclaimers?.estimation || '' }} />
                <Text mt="sm" mb="xs">
                  <b>Confidence score</b>
                </Text>
                <div dangerouslySetInnerHTML={{ __html: disclaimers?.confidence || '' }} />
              </TypographyStylesProvider>
            }
          />
          <SimpleGrid spacing="md" cols={{ base: 1, sm: 2 }}>
            <Stack gap="sm">
              <Title order={6} size="md" c="gray.9">
                Property value
              </Title>
              <Paper p="sm" mih={hasEstimate ? 132 : undefined} style={{ alignContent: 'center' }}>
                <Flex direction="column" gap="xxs" align={{ base: 'stretch', sm: 'center' }}>
                  <Group justify="space-between" gap="xxs">
                    {estimatedValue?.confidence && getConfidenceBadge(estimatedValue.confidence)}
                  </Group>
                  <Title component="span" size="h2">
                    {estimatedValue?.estimate ? formatCurrency(estimatedValue.estimate) : 'N/A'}
                  </Title>
                  {estimatedValue?.lowEstimate && estimatedValue.highEstimate && (
                    <Text component="span" c="gray.9" size="xs" fw={500} lh="xs">
                      Range: ${formatLargeNumber(estimatedValue.lowEstimate)}-$
                      {formatLargeNumber(estimatedValue.highEstimate)}
                    </Text>
                  )}
                </Flex>
              </Paper>
            </Stack>
            <Stack gap="sm">
              <Title order={6} size="md" c="gray.9">
                Rental income
              </Title>
              <Paper p="sm" mih={hasEstimate ? 132 : undefined} style={{ alignContent: 'center' }}>
                <Flex direction="column" gap="xxs" align={{ base: 'stretch', sm: 'center' }}>
                  <Group justify="space-between" gap="xxs">
                    {estimatedRent?.confidence && getConfidenceBadge(estimatedRent.confidence)}
                  </Group>
                  <Group align="flex-end" gap="xxxs">
                    <Title component="span" size="h2">
                      {estimatedRent?.estimate ? formatCurrency(estimatedRent.estimate) : 'N/A'}
                    </Title>
                    <Text component="span">/week</Text>
                  </Group>
                  {estimatedRent?.lowEstimate && estimatedRent.highEstimate && (
                    <Text component="span" c="gray.9" size="xs" fw={500} lh="xs">
                      Range: ${formatLargeNumber(estimatedRent.lowEstimate)}-$
                      {formatLargeNumber(estimatedRent.highEstimate)}
                    </Text>
                  )}
                </Flex>
              </Paper>
            </Stack>
          </SimpleGrid>
          {showPropertyEnquiry && propertyValueComparison && (
            <Center>
              <Stack gap="sm" align="center">
                <Text size="md" c="gray.9" ta="center">
                  This property listing is {formatCurrency(Math.abs(propertyValueComparison))} (
                  {Math.abs(propertyValueComparisonPercentage).toFixed(2)}%)
                  {propertyValueComparison <= 0 ? ' less' : ' more'} than the suburb median.
                </Text>
                {getComparisonMessage(propertyValueComparisonPercentage)}
                <PropertyEnquiry
                  variant="highlight"
                  width="fit-content"
                  customerId={customerId}
                  propertyDetails={
                    {
                      propertyAddress: streetAddress || primaryAddress || '',
                      propertyId: id || property.id,
                      llPropertyId: listingLoopId || '',
                      propertySlug: slug || '',
                      agentDetails: {
                        agentName: realEstateAgents?.[0]?.name || '',
                        agentEmail: realEstateAgents?.[0]?.email || '',
                        agentPhone: realEstateAgents?.[0]?.phone || '',
                        agencyName: realEstateAgents?.[0]?.agency?.name || '',
                      },
                    } as PropertyDetails
                  }
                />
              </Stack>
            </Center>
          )}
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
