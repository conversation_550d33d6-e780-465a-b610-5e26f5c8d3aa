'use client';

import { useState } from 'react';
import { Container, Divider, Flex, SimpleGrid, Stack, Tabs, Text, Title } from '@mantine/core';

import type { Council, Property, State, Suburb } from '@gp/data-access/property-hub';
import {
  AreaChart,
  BadgeWithIconAndText,
  Card,
  Collapse,
  SectionHeader,
  SegmentedControlWithTracking,
  TabsWithTracking,
} from '@gp/ui/components';
import { ArrowUpward } from '@gp/ui/icons';
import { formatCurrency, formatLargeNumber, formatNumber } from '@gp/util/intl';

const getPriceHistory = (
  area: string,
  data: Array<[string, number]>,
  state?: string,
  stateData?: Array<[string, number]>
) => {
  return data.map(([month, value], index) => ({
    date: month,
    [area]: value,
    ...(state && stateData?.length ? { [state]: stateData[index]?.[1] } : {}),
  }));
};

export default function PropertyPriceTrendsV2(props: State | Council | Suburb | Property) {
  const isProperty = 'address' in props;
  const isState = 'councils' in props && 'suburbs' in props;
  const isCouncil = 'state' in props && 'suburbs' in props;
  const isSuburb = 'state' in props && 'surroundingSuburbs' in props;

  const dataPageType = isProperty
    ? 'Property'
    : isSuburb
    ? 'Suburb'
    : isCouncil
    ? 'Council'
    : 'State';

  const {
    houseMedianPrice,
    houseMedianPriceHistory,
    housePriceChangeLast12Months,
    houseMedianRent,
    houseRentChangeLast12Months,
    unitMedianPrice,
    unitMedianPriceHistory,
    unitPriceChangeLast12Months,
    unitMedianRent,
    unitRentChangeLast12Months,
  } = isProperty ? props.suburb.data : props.data;
  const areaName = isProperty ? props.suburb.name : props.name;

  const name = isState
    ? props.name
    : isCouncil
    ? `${props.name}, ${props.state.name}`
    : isSuburb
    ? `${props.name} ${props.council ? `- ${props.council?.name}` : ''}, ${props.state.name}`
    : areaName;

  const [selectedTrend, setSelectedTrend] = useState<string>(
    isProperty ? (props.data.type.toLowerCase() === 'house' ? 'house' : 'unit') : 'house'
  );

  const state = isProperty ? props.suburb.state : 'state' in props ? props.state : undefined;

  const priceHistory = selectedTrend === 'house' ? houseMedianPriceHistory : unitMedianPriceHistory;
  const statePriceHistory =
    selectedTrend === 'house' ? state?.houseMedianPriceHistory : state?.unitMedianPriceHistory;

  const badgeText = (
    <>
      <Text size="sm" c="black" tt={'uppercase'}>
        L
      </Text>
      <Text size="sm" c="black" tt={'lowercase'}>
        ast 12 months
      </Text>
    </>
  );

  if (isProperty && !priceHistory.length) {
    return null;
  }

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <Flex direction="column" gap={{ base: 'md', md: 'lg' }}>
          <SectionHeader
            title={isProperty ? `Median ${selectedTrend} price trends` : 'Property price trends'}
            order={3}
            size="h4"
            description={
              isProperty
                ? `Comparing the price trend of ${selectedTrend}s in ${areaName} against the state of ${
                    state?.name
                  } over the last ${Math.floor(priceHistory.length / 12)} years. Source: CoreLogic`
                : `These median prices may indicate the common property values in this area and how they have changed over the last ${Math.floor(
                    priceHistory.length / 12
                  )} years. Source: CoreLogic`
            }
            info={
              <Stack gap="md" p="xs">
                <Text>
                  This section may help you assess if properties in this area fit in your budget.
                </Text>
                <Stack gap="xxs">
                  <Title order={5}>Median sale price</Title>
                  <Text>
                    Will show you typically what people are paying for properties in the area.
                  </Text>
                </Stack>
                <Stack gap="xxs">
                  <Title order={5}>Trend graph</Title>
                  <Text>
                    This visualises the property price changes over time in this area. You can
                    compare the price movements for houses and units to see how they align with your
                    financial plans. You can use the market activity section below to give you an
                    indication of how competitive the area is.
                  </Text>
                </Stack>
              </Stack>
            }
          />
          {!isProperty && (
            <TabsWithTracking defaultValue="buy" position="Property Price Trends" type={areaName}>
              <Tabs.List>
                <Tabs.Tab value="buy" data-testid="buy-toggle-in-market-trend" miw={150}>
                  Buy
                </Tabs.Tab>
                <Tabs.Tab value="rent" data-testid="rent-toggle-in-market-trend" miw={150}>
                  Rent
                </Tabs.Tab>
              </Tabs.List>
              <Tabs.Panel value="buy">
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                  <Card
                    title={houseMedianPrice ? formatCurrency(houseMedianPrice) : 'N/A'}
                    order={4}
                    description="House median sale price"
                    image="property-hub/house.svg"
                    type="badge"
                    badge={
                      <BadgeWithIconAndText
                        variant={
                          housePriceChangeLast12Months
                            ? housePriceChangeLast12Months > 0
                              ? 'green'
                              : 'red'
                            : 'gray'
                        }
                        label={
                          housePriceChangeLast12Months
                            ? `${formatNumber(Math.abs(housePriceChangeLast12Months))}% ${
                                housePriceChangeLast12Months > 0 ? 'Growth' : 'Decline'
                              }`
                            : ''
                        }
                        icon={
                          housePriceChangeLast12Months ? (
                            <ArrowUpward
                              size={14}
                              style={{
                                transform:
                                  housePriceChangeLast12Months > 0 ? undefined : 'rotate(180deg)',
                              }}
                            />
                          ) : undefined
                        }
                        text={housePriceChangeLast12Months ? badgeText : 'No trend data available'}
                      />
                    }
                  />
                  <Card
                    title={unitMedianPrice ? formatCurrency(unitMedianPrice) : 'N/A'}
                    order={4}
                    description="Unit median sale price"
                    image="property-hub/unit.svg"
                    type="badge"
                    badge={
                      <BadgeWithIconAndText
                        variant={
                          unitPriceChangeLast12Months
                            ? unitPriceChangeLast12Months > 0
                              ? 'green'
                              : 'red'
                            : 'gray'
                        }
                        label={
                          unitPriceChangeLast12Months
                            ? `${formatNumber(Math.abs(unitPriceChangeLast12Months))}% ${
                                unitPriceChangeLast12Months > 0 ? 'Growth' : 'Decline'
                              }`
                            : ''
                        }
                        icon={
                          unitPriceChangeLast12Months ? (
                            <ArrowUpward
                              size={14}
                              style={{
                                transform:
                                  unitPriceChangeLast12Months > 0 ? undefined : 'rotate(180deg)',
                              }}
                            />
                          ) : undefined
                        }
                        text={unitPriceChangeLast12Months ? badgeText : 'No trend data available'}
                      />
                    }
                  />
                </SimpleGrid>
              </Tabs.Panel>
              <Tabs.Panel value="rent">
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                  <Card
                    title={houseMedianRent ? formatCurrency(houseMedianRent) : 'N/A'}
                    order={4}
                    description="House median weekly rent"
                    image="property-hub/house.svg"
                    type="badge"
                    badge={
                      <BadgeWithIconAndText
                        variant={
                          houseRentChangeLast12Months
                            ? houseRentChangeLast12Months > 0
                              ? 'green'
                              : 'red'
                            : 'gray'
                        }
                        label={
                          houseRentChangeLast12Months
                            ? `${formatNumber(Math.abs(houseRentChangeLast12Months))}% ${
                                houseRentChangeLast12Months > 0 ? 'Growth' : 'Decline'
                              }`
                            : ''
                        }
                        icon={
                          houseRentChangeLast12Months ? (
                            <ArrowUpward
                              size={14}
                              style={{
                                transform:
                                  houseRentChangeLast12Months > 0 ? undefined : 'rotate(180deg)',
                              }}
                            />
                          ) : undefined
                        }
                        text={houseRentChangeLast12Months ? badgeText : 'No trend data available'}
                      />
                    }
                  />
                  <Card
                    title={unitMedianRent ? formatCurrency(unitMedianRent) : 'N/A'}
                    order={4}
                    description="Unit median weekly rent"
                    image="property-hub/unit.svg"
                    type="badge"
                    badge={
                      <BadgeWithIconAndText
                        variant={
                          unitRentChangeLast12Months
                            ? unitRentChangeLast12Months > 0
                              ? 'green'
                              : 'red'
                            : 'gray'
                        }
                        label={
                          unitRentChangeLast12Months
                            ? `${formatNumber(Math.abs(unitRentChangeLast12Months))}% ${
                                unitRentChangeLast12Months > 0 ? 'Growth' : 'Decline'
                              }`
                            : ''
                        }
                        icon={
                          unitRentChangeLast12Months ? (
                            <ArrowUpward
                              size={14}
                              style={{
                                transform:
                                  unitRentChangeLast12Months > 0 ? undefined : 'rotate(180deg)',
                              }}
                            />
                          ) : undefined
                        }
                        text={unitRentChangeLast12Months ? badgeText : 'No trend data available'}
                      />
                    }
                  />
                </SimpleGrid>
              </Tabs.Panel>
            </TabsWithTracking>
          )}
          {(houseMedianPriceHistory.length > 0 || unitMedianPriceHistory.length > 0) && (
            <Collapse
              in={isProperty}
              hideControl={isProperty}
              expandText="See trends over time"
              collapseText="See less"
              position="Market trends"
              data-testid="expand-collapse-in-market-trend"
              name={name}
              type={dataPageType}
              hasText={true}
            >
              <Stack align="flex-start">
                {!isProperty && (
                  <>
                    <Stack gap="xxs">
                      <Title order={5}>Median price trend</Title>
                      <Text>
                        This could show if the area is improving, suggesting future growth, or
                        declining which may offer short-term buying opportunities.
                      </Text>
                    </Stack>
                    <SegmentedControlWithTracking
                      withItemsBorders={false}
                      value={selectedTrend}
                      onChange={setSelectedTrend}
                      data={[
                        {
                          label: 'House',
                          value: 'house',
                          disabled: !houseMedianPriceHistory.length,
                        },
                        { label: 'Unit', value: 'unit', disabled: !unitMedianPriceHistory.length },
                      ]}
                      data-testid="house-unit-toggle-in-market-trend"
                      name={name}
                      position={'Area Listing'}
                    />
                    <Text size="sm">
                      Showing the trend for {selectedTrend} prices over the last{' '}
                      {Math.floor(priceHistory.length / 12)} years. Source: CoreLogic
                    </Text>
                  </>
                )}
                <AreaChart
                  withDots={false}
                  xAxisProps={{
                    padding: { right: 16 },
                    reversed: true,
                    tickSize: 5,
                    ticks: priceHistory
                      .filter((_, index) => index % 6 === 0)
                      .map(([month]) => month),
                  }}
                  yAxisProps={{
                    tickFormatter: (value) => `$${formatLargeNumber(value)}`,
                    domain: ['auto', 'auto'],
                    width: 45,
                  }}
                  dataKey="date"
                  data={getPriceHistory(areaName, priceHistory, state?.name, statePriceHistory)}
                  series={[areaName, ...(state ? [state.name] : [])]}
                  valueFormatter={(value) => formatCurrency(value)}
                />
              </Stack>
            </Collapse>
          )}
        </Flex>
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
