'use client';

import { Box, Divider, Flex, Group, Paper, Stack, Text, Title } from '@mantine/core';
import { isEmail } from '@mantine/form';
import { useSessionStorage } from '@mantine/hooks';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';
import { SESSION_STORAGE_KEYS } from '@lendi/lala-utils';

import { getBrokerAPIs } from '@gp/data-access/broker';
import { fetchCustomer } from '@gp/data-access/customer';
import { AnchorWithTracking, ButtonWithTracking, Image } from '@gp/ui/components';
import { Calendar, Email, Telephone } from '@gp/ui/icons';

export default function BrokerCTAV2() {
  const { getBasicBrokersByIds } = getBrokerAPIs();

  const { status, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;

  // Fetch the customer details if authenticated
  const { data: customer, isLoading: isLoadingCustomer } = useSWRImmutable(
    isAuthenticated && token ? 'me' : null,
    async () => fetchCustomer('me', token)
  );
  const [targetedBrokerId] = useSessionStorage<string | undefined>({
    key: SESSION_STORAGE_KEYS.CTOR_TARGET_BROKER_ID,
    defaultValue: undefined,
  });

  const brokerToCall = targetedBrokerId || customer?.ownerId;
  // Check if there is a targeted broker (PRL with higher priority)
  const { data: Broker, isLoading } = useSWRImmutable(
    !isLoadingCustomer && brokerToCall ? brokerToCall : null,
    getBasicBrokersByIds
  );
  const handleClick = () => {
    window.location.assign('/book-appointment/');
  };
  if (isLoading) return null;

  const noBrokerData = !Broker || Broker.length === 0;
  const broker = noBrokerData ? null : Broker[0];
  const ctaLink = `/book-appointment${noBrokerData ? '' : `?brokerId=${broker?.id}`}`;
  const cta = `Book a chat with ${noBrokerData ? 'a broker' : broker?.name.split(' ')[0]}`;
  const imageUrl = `${noBrokerData ? undefined : broker?.imageUrl}`;

  return (
    <>
      {!noBrokerData && broker ? (
        <Flex gap="sm" align="center" p="sm">
          {imageUrl && (
            <Image
              bg="grape.1"
              src={imageUrl}
              alt={ctaLink}
              radius="100%"
              h={{ base: 80 }}
              w={{ base: 80 }}
              flex="none"
            />
          )}
          <Stack gap="8">
            <Stack gap="0">
              <Text>{broker.name}</Text>
              <Text tt="capitalize">{broker.role.toLowerCase().replace('_', ' ')}</Text>
            </Stack>
            {broker.email && (
              <Flex align="center" wrap="nowrap" justify="start" gap="6">
                <Email size={16} fill="#4B1F68" />
                <AnchorWithTracking
                  href={`mailto:${broker.email}`}
                  label=""
                  fw={400}
                  linkType={'LinkToEmail'}
                  name={broker.email}
                  style={{ wordBreak: 'break-all' }}
                >
                  {broker.email}
                </AnchorWithTracking>
              </Flex>
            )}
            <Flex align="center" wrap="nowrap" justify="start" gap="6">
              <Calendar size={16} fill="#4B1F68" />
              <AnchorWithTracking
                href={ctaLink}
                label="Book an appointment"
                useNextLink
                position={`Side Panel CTA Card`}
                fw={700}
                linkType={'LinkToPage'}
                name={cta}
              />
            </Flex>
          </Stack>
        </Flex>
      ) : (
        <Box p="md">
          <Stack gap="xs" align="center">
            <Title fw={700} component="p" size="h5" ta="center">
              Need help with your home loan?
            </Title>
            <ButtonWithTracking
              label="Book a free chat"
              variant="highlight"
              position="Side Panel CTA - Home Loan"
              purpose="Book appointment"
              onClick={handleClick}
            />
          </Stack>
        </Box>
      )}
    </>
  );
}
