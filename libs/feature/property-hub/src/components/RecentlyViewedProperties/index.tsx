'use client';

import { CarouselSlide } from '@mantine/carousel';
import { Stack } from '@mantine/core';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { Status, useSession } from '@lendi/lala-react';

import { CustomerProperty, LISTING_TYPE } from '@gp/data-access/property-hub';
import { Carousel, SectionHeader } from '@gp/ui/components';

import { useViewedHistory } from '../../hooks/useViewedHistory';
import PropertyCard from '../CustomerProperties/PropertyCard';
import ForSalePropertyCardV2 from '../PropertyCard/ForSalePropertyCard/v2';

export default function RecentlyViewedProperties() {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const { gpListingSearchV2 } = useFlags();

  const { viewedPropertyHistory } = useViewedHistory();

  const isPreMarketOrOffMarket = (property: CustomerProperty) =>
    property?.data?.listingType
      ? property?.data?.listingType === LISTING_TYPE.PRE_MARKET ||
        property?.data?.listingType === LISTING_TYPE.OFF_MARKET
      : property?.listingType
      ? property?.listingType === LISTING_TYPE.PRE_MARKET ||
        property?.listingType === LISTING_TYPE.OFF_MARKET
      : false;

  const filteredData = isAuthenticated
    ? viewedPropertyHistory
    : viewedPropertyHistory.filter((property) => !isPreMarketOrOffMarket(property));

  const showOldPropertyCard = filteredData.some((property) => !property.data);

  if (filteredData.length === 0) {
    return null;
  }

  return (
    <Stack gap="xs">
      <SectionHeader order={1} fz={19.25} fw={700} title="Recently Viewed Properties" />
      <Carousel
        withIndicators
        variant="default"
        controlPosition="bottom"
        slideGap="sm"
        slideSize={{ xs: 60, sm: 50, md: 38, lg: 30 }}
      >
        {filteredData.map((property) => (
          <CarouselSlide key={property.propertyId}>
            {gpListingSearchV2 && !showOldPropertyCard ? (
              <ForSalePropertyCardV2
                property={{
                  ...property,
                  id: property.propertyId,
                  data: { ...property.data, type: property.type },
                }}
                isCarousel={false}
              />
            ) : (
              <PropertyCard key={property.propertyId} {...property} isRecentlyViewed={true} />
            )}
          </CarouselSlide>
        ))}
      </Carousel>
    </Stack>
  );
}
