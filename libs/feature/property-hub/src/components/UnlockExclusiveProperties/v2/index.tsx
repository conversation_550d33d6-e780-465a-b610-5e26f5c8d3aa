import { Container, Divider, Stack } from '@mantine/core';

import type { Property } from '@gp/data-access/property-hub';
import { hasExclusiveListings } from '@gp/database/property-hub';
import { LinkButtonWithTracking, SectionHeader } from '@gp/ui/components';

export default async function UnlockExclusivePropertiesV2(property: Property) {
  const exclusiveListingsAvailable = await hasExclusiveListings(`${property.suburb.slug}`);
  if (!exclusiveListingsAvailable) return null;

  return (
    <Container color="white" p={{ base: 'xs', md: 'md' }} flex={1}>
      <Stack gap="sm">
        <SectionHeader
          title="Unlock Exclusive Properties"
          order={3}
          size="h4"
          description={`Discover hidden gems in ${property.suburb.name} that aren't listed anywhere else. Get exclusive
              access to our Off & Pre-Market properties`}
        />
        <LinkButtonWithTracking
          variant="secondary"
          label="View exclusive properties ->"
          useNextLink
          href={`/property/results?areas=${property.suburb.slug}&showExclusiveListingsFilter=true`}
          size="sm"
          w={{ base: '100%', md: '234px' }}
          position={`Property Detail - Unlock Exclusive Properties - ${property.suburb.name}`}
          type="Property"
          name="unlock-exclusive-properties"
        />
        <Divider color="gray.1" mt={{ base: 'xs', md: 'md' }} />
      </Stack>
    </Container>
  );
}
