import { Divider, Grid, GridCol, Stack } from '@mantine/core';

import type { Property } from '@gp/data-access/property-hub';
import { PageTracking } from '@gp/ui/components';

import {
  AppDownloadPanelV2,
  Breadcrumbs,
  BuyersAgentPanelV2,
  ComparablePropertiesV2,
  EducationV2,
  EventTimesV2,
  MarketActivityV2,
  PriceGuideStatementV2,
  PropertyDescriptionV2,
  PropertyHistoryV2,
  PropertyPriceTrendsV2,
  PropertyZoneAndRisksV2,
  RepaymentsV2,
  ResidentsV2,
  SideBarSticky,
  SuburbsV2,
  UnlockExclusivePropertiesV2,
  ValuationV2,
  WiderViewV2,
} from '../../../components';
import GoogleMapBox from '../../../components/GoogleMapBox';
import PropertyGalleryV2 from '../../../components/PropertyOverview/PropertyGallery/v2';
import PropertySummaryV2 from '../../../components/PropertyOverview/PropertySummary/v2';
import MobileBottomBarSticky from '../../../components/StickyBar/MobileBottom';

export default async function PropertyDetailsV2(property: Property) {
  return (
    <PageTracking
      category="AUSSIE_HOMES"
      propertyProp={{
        property_search_state: property.suburb.state.name,
        property_search_suburb: property.suburb.name,
        property_search_council: property.suburb.council?.name || '',
        // IS needs something like 31a Alfred Road NSW Narraweena 2099 for address for email
        property_search_address: `${property.address} ${property.suburb.state.name} ${property.suburb.name} ${property.suburb.postcode}`,
        // IS needs the slug for redirection
        property_search_webaSlug: property.slug,
      }}
      name={`Property - ${property.address}, ${property.suburb.name}, ${
        property.suburb.state.name
      } ${property.data.photo ? 'imagepresent' : 'imageabsent'}`}
      withMarketingCloudTracking
    >
      <Stack gap="sm">
        <Breadcrumbs {...property} />
        <Divider />
        <PropertyGalleryV2 {...property} />
        <Grid pt="sm">
          <GridCol span={{ base: 12, sm: 7.5, md: 8.5 }}>
            <PropertySummaryV2 {...property} />
            <GoogleMapBox {...property} />
            <PropertyDescriptionV2 {...property} />
            <ValuationV2 {...property} />
            <UnlockExclusivePropertiesV2 {...property} />
            <RepaymentsV2 {...property} />
            <EventTimesV2 {...property} />
            <PropertyHistoryV2 {...property} />
            <PropertyPriceTrendsV2 {...property} />
            <ComparablePropertiesV2 {...property} />
            <AppDownloadPanelV2 {...property} />
            <PropertyZoneAndRisksV2 {...property} />
            <MarketActivityV2 {...property} />
            <ResidentsV2 {...property} />
            <BuyersAgentPanelV2 {...property} />
            <PriceGuideStatementV2 {...property} />
            <EducationV2 {...property} />
            <WiderViewV2 {...property} />
            <SuburbsV2 {...property} />
          </GridCol>
          <GridCol span={{ base: 12, sm: 4.5, md: 3.5 }}>
            <SideBarSticky {...property} />
          </GridCol>
        </Grid>
      </Stack>
      <MobileBottomBarSticky {...property} />
    </PageTracking>
  );
}
