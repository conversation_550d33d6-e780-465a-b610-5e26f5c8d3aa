import { useCallback, useEffect, useMemo } from 'react';
import { useLocalStorage } from '@mantine/hooks';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { CustomerProperty, getPropertyHubAPIs } from '@gp/data-access/property-hub';

const HISTORY_LIMIT = 8;

export const useViewedHistory = () => {
  const { status, token, identity } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const customerId = identity?.id;
  const [viewedPropertyHistoryLocal, setViewedPropertyHistoryLocal] = useLocalStorage<
    CustomerProperty[]
  >({
    key: 'GP_CUSTOMER_VIEWED_PROPERTY_HISTORY',
    getInitialValueInEffect: false,
    defaultValue: [],
  });

  const { getCustomerProperties, upsertCustomerProperty } = getPropertyHubAPIs(token);

  const {
    data: customerProperties,
    isLoading,
    mutate,
  } = useSWRImmutable(
    isAuthenticated && token && customerId ? `customer-${customerId}-properties` : null,
    () => (customerId ? getCustomerProperties(customerId) : [])
  );

  // memoize the local storage history to prevent infinite re-renders
  const newLocalStorageHistory = useMemo(() => {
    const remoteLatestUpdatedAt = customerProperties?.[0]?.updatedAt || new Date(0).toISOString();

    const result = viewedPropertyHistoryLocal.filter((property) => {
      const localLatestUpdatedAt = property.updatedAt
        ? property.updatedAt
        : new Date().toISOString();
      return localLatestUpdatedAt > remoteLatestUpdatedAt;
    });

    return result;
  }, [customerProperties, viewedPropertyHistoryLocal]);

  // combine the local storage history with the remote history and limit to 8
  const mergedHistory = useMemo(() => {
    const mergedData = [...newLocalStorageHistory, ...(customerProperties || [])];
    const uniqueData = [];
    const keySet = new Set<string>();
    for (const item of mergedData) {
      const key = item.propertyId;
      if (!keySet.has(key)) {
        keySet.add(key);
        uniqueData.push(item);
      }
    }
    return uniqueData.slice(0, HISTORY_LIMIT);
  }, [customerProperties, newLocalStorageHistory]);

  //if unauthed, update local storage
  const addViewedPropertyToLocalStorage = useCallback(
    (viewedProperty: CustomerProperty) => {
      const latestViewedDate = new Date();
      setViewedPropertyHistoryLocal((prevHistory) => {
        //check for existing data in local storage
        const existingProperty = prevHistory.find(
          (property) => property.propertyId === viewedProperty.propertyId
        );
        const existingPropertyIndex = prevHistory.findIndex(
          (property) => property.propertyId === viewedProperty.propertyId
        );
        if (existingPropertyIndex >= 0) {
          const updatedHistory = [...prevHistory];
          updatedHistory.splice(existingPropertyIndex, 1);
          updatedHistory.unshift({
            ...existingProperty,
            updatedAt: latestViewedDate,
          } as CustomerProperty);

          return [...updatedHistory];
        } else {
          return [
            {
              ...viewedProperty,
              updatedAt: latestViewedDate,
              createdAt: latestViewedDate,
            } as CustomerProperty,
            ...prevHistory,
          ];
        }
      });
    },
    [setViewedPropertyHistoryLocal]
  );

  //when authed, save data from local storage into customer properties table
  const saveViewedPropertyHistoryToRemote = useCallback(
    async (viewedHistory: CustomerProperty[]) => {
      if (!customerId || !isAuthenticated || viewedHistory.length === 0) return;
      for (const property of viewedHistory) {
        await upsertCustomerProperty(customerId, { propertyId: property.propertyId });
      }
      mutate();
    },
    [customerId, isAuthenticated, mutate, upsertCustomerProperty]
  );

  //when authed, save data from customer properties table into local storage
  const syncRemoteWithLocalStorage = useCallback(async () => {
    const localExistingKeys = new Set();
    for (const property of viewedPropertyHistoryLocal) {
      localExistingKeys.add(property.propertyId);
    }
    const hasNewPropertiesFromRemote = mergedHistory?.some(
      (property) => !localExistingKeys.has(property.propertyId)
    );
    if (hasNewPropertiesFromRemote) {
      setViewedPropertyHistoryLocal(mergedHistory);
    }
  }, [mergedHistory, setViewedPropertyHistoryLocal, viewedPropertyHistoryLocal]);

  useEffect(() => {
    if (!isAuthenticated || !customerId || isLoading || !newLocalStorageHistory.length) return;
    //reverse the array to save the latest viewed property to the top
    saveViewedPropertyHistoryToRemote([...newLocalStorageHistory].reverse());
  }, [
    customerId,
    isAuthenticated,
    isLoading,
    newLocalStorageHistory,
    saveViewedPropertyHistoryToRemote,
    viewedPropertyHistoryLocal,
  ]);

  useEffect(() => {
    if (!isAuthenticated || !customerId) return;
    syncRemoteWithLocalStorage();
  }, [customerId, isAuthenticated, syncRemoteWithLocalStorage, mergedHistory]);

  return {
    viewedPropertyHistory: mergedHistory,
    addViewedPropertyToLocalStorage,
  };
};
