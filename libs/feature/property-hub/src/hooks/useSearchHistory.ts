'use client';
import { useCallback, useEffect, useMemo } from 'react';
import { useLocalStorage } from '@mantine/hooks';
import useSWRImmutable from 'swr/immutable';
import { Status, useSession } from '@lendi/lala-react';

import { CustomerPropertySearch, getPropertyHubAPIs } from '@gp/data-access/property-hub';

const HISTORY_LIMIT = 8;

export const useSearchHistory = () => {
  const { status, identity, token } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const customerId = identity?.id;
  const [localSearchHistory, setLocalSearchHistory] = useLocalStorage<CustomerPropertySearch[]>({
    key: 'GP_CUSTOMER_PROPERTY_SEARCH_HISTORY',
    getInitialValueInEffect: false,
    defaultValue: [],
  });
  const { upsertCustomerPropertySearch, getCustomerPropertySearches } = getPropertyHubAPIs(token);
  const key = isAuthenticated && customerId ? `customer-${customerId}-property-searches` : null;
  const {
    data: remoteSearchHistory,
    isLoading,
    mutate,
  } = useSWRImmutable(key, () => getCustomerPropertySearches(customerId || ''));

  const localNewSearchHistory = useMemo(() => {
    const remoteLatestUpdatedAt = remoteSearchHistory?.[0]?.updatedAt || new Date(0).toISOString();
    const result = localSearchHistory.filter((item) => {
      const itemDate = item.updatedAt ? item.updatedAt : new Date().toISOString();
      return itemDate > remoteLatestUpdatedAt;
    });
    return result;
  }, [localSearchHistory, remoteSearchHistory]);

  const mergedSearchHistory = useMemo(() => {
    const merged = [...localNewSearchHistory, ...(remoteSearchHistory || [])];
    // remove duplicates based on queryString and searchCategory
    const uniqueHistories = [];
    const keySet = new Set<string>();
    for (const item of merged) {
      const key = `${item.queryString}-${item.searchCategory}`;
      if (!keySet.has(key)) {
        keySet.add(key);
        uniqueHistories.push(item);
      }
    }
    return uniqueHistories.splice(0, HISTORY_LIMIT);
  }, [localNewSearchHistory, remoteSearchHistory]);

  const saveSearchesToRemote = useCallback(
    async (searches: CustomerPropertySearch[]) => {
      // do nothing if not logged in, or no searches to save, or remote search history is not loaded yet
      if (!customerId || searches.length === 0 || remoteSearchHistory === undefined) return;
      for (const search of searches) {
        await upsertCustomerPropertySearch(customerId, search);
      }
      mutate();
    },
    [customerId, mutate, remoteSearchHistory, upsertCustomerPropertySearch]
  );

  const addSearchHistory = useCallback(
    (history: CustomerPropertySearch) => {
      if (!history.createdAt) {
        history.createdAt = new Date();
      }
      if (!history.updatedAt) {
        history.updatedAt = new Date();
      }
      setLocalSearchHistory((prevHistory) => {
        const existingIndex = prevHistory.findIndex(
          (item) =>
            item.queryString === history.queryString &&
            item.searchCategory === history.searchCategory
        );

        if (existingIndex >= 0) {
          const updatedHistory = [...prevHistory];
          updatedHistory.splice(existingIndex, 1);
          return [history, ...updatedHistory];
        } else {
          return [history, ...prevHistory];
        }
      });
    },
    [setLocalSearchHistory]
  );

  // Sync local search history to remote
  useEffect(() => {
    if (!isAuthenticated || !customerId || !localNewSearchHistory?.length || isLoading) return;
    saveSearchesToRemote(localNewSearchHistory);
  }, [isAuthenticated, customerId, saveSearchesToRemote, localNewSearchHistory, isLoading]);

  // Sync remote search history to local
  useEffect(() => {
    if (!isAuthenticated || !customerId) return;
    const localExistingKeys = new Set();
    for (const search of localSearchHistory) {
      localExistingKeys.add(`${search.queryString}-${search.searchCategory}`);
    }
    const hasNewSearchesFromRemote = mergedSearchHistory?.some(
      (search) => !localExistingKeys.has(`${search.queryString}-${search.searchCategory}`)
    );
    if (hasNewSearchesFromRemote) {
      setLocalSearchHistory(mergedSearchHistory);
    }
  }, [isAuthenticated, customerId, mergedSearchHistory, localSearchHistory, setLocalSearchHistory]);

  return {
    searchHistory: mergedSearchHistory,
    addSearchHistory,
  };
};
