# Feature Libraries Guidelines

## Overview
Feature libraries contain complete business features with components, pages, hooks, and business logic. They represent self-contained feature modules that can be composed into applications.

## Import Pattern
```typescript
import { FeatureComponent, FeaturePage } from '@gp/feature/feature-name';
```

## Available Feature Libraries

### 1. Articles Hub (`@gp/feature/articles-hub`)
Content management and article display features.

**Components:**
- `ArticleListBlock`: Grid layout for article listings
- `ArticleListCard`: Individual article card component
- `TagListBlock`: Tag filtering interface
- `Carousel`: Article carousel display

**Pages:**
- `ArticleList`: Main article listing page
- `Insights`: Insights hub page
- `Pillar`: Topic pillar pages

**Usage:**
```typescript
import { 
  ArticleListBlock,
  ArticleListCard,
  ArticleList,
  Insights 
} from '@gp/feature/articles-hub';

// Article listing with filtering
<ArticleListBlock
  articles={articles}
  tags={availableTags}
  onTagFilter={handleTagFilter}
/>

// Individual article card
<ArticleListCard
  article={article}
  onClick={handleArticleClick}
/>
```

### 2. Navigation Features

#### Aussie Navbar (`@gp/feature/aussie-navbar`)
Aussie-branded navigation components with multi-variant support.

**Components:**
- `MultiNavbar`: Main navigation with purple/white variants
- `SimpleNavbar`: Simplified navigation
- `PurpleNavbar`: Purple-themed navigation
- `WhiteNavbar`: White-themed navigation

**Usage:**
```typescript
import { MultiNavbar, SimpleNavbar } from '@gp/feature/aussie-navbar';

// Multi-variant navbar
<MultiNavbar
  variant="purple"
  showAuthButtons={true}
  customerData={customerData}
/>

// Simple navbar for landing pages
<SimpleNavbar
  logoVariant="white"
  showBackButton={true}
/>
```

#### Aussie Footer (`@gp/feature/aussie-footer`)
Complete footer with all sections and links.

**Components:**
- `Footer`: Main footer component
- `Acknowledgement`: Indigenous acknowledgement
- `AppDownload`: App download section
- `BrandBar`: Brand information
- `Copyright`: Copyright information
- `FooterLinks`: Footer navigation links
- `SocialMedia`: Social media links

**Usage:**
```typescript
import { Footer } from '@gp/feature/aussie-footer';

<Footer
  showAppDownload={true}
  showSocialMedia={true}
  customLinks={additionalLinks}
/>
```

#### Lendi Navbar (`@gp/feature/lendi-navbar`)
Lendi-branded navigation components.

**Usage:**
```typescript
import { Navbar } from '@gp/feature/lendi-navbar';

<Navbar
  showUserProfile={isAuthenticated}
  variant="default"
/>
```

### 3. Service Features

#### Buyers Agent (`@gp/feature/buyers-agent`)
Complete buyers agent engagement flow.

**Components:**
- `BuyersAgentBenefits`: Benefits showcase
- `BuyingSituation`: Buying situation form
- `ConfirmationPanel`: Confirmation display
- `EngagementConfirmation`: Engagement confirmation
- `PendingPaymentConfirmation`: Payment pending state
- `PropertySpecification`: Property requirements form

**Pages:**
- `BuyersEngagement`: Main engagement flow
- `Confirmation`: Success confirmation
- `GetStarted`: Initial landing page
- `TransactionComplete`: Transaction completion

**Usage:**
```typescript
import { 
  BuyersEngagement,
  BuyingSituation,
  PropertySpecification 
} from '@gp/feature/buyers-agent';

// Complete engagement flow
<BuyersEngagement
  onComplete={handleEngagementComplete}
  customerData={customerData}
/>

// Individual form components
<BuyingSituation
  onSubmit={handleBuyingSituation}
  initialData={buyingSituationData}
/>

<PropertySpecification
  onSubmit={handlePropertySpec}
  locationData={locationData}
/>
```

#### Conveyancing (`@gp/feature/conveyancing`)
Conveyancing service engagement flow.

**Components:**
- `Confirmation`: Service confirmation
- `ConveyancingService`: Service details
- `QuoteEstimation`: Quote calculation
- `SearchBar`: Property search
- `UserSituation`: User situation assessment

**Pages:**
- `ConveyancingEngagement`: Main engagement flow

**Usage:**
```typescript
import { 
  ConveyancingEngagement,
  QuoteEstimation,
  ConveyancingService 
} from '@gp/feature/conveyancing';

<ConveyancingEngagement
  onQuoteRequest={handleQuoteRequest}
  propertyData={propertyData}
/>

<QuoteEstimation
  propertyValue={propertyValue}
  propertyType={propertyType}
  state={state}
  onQuoteCalculated={handleQuote}
/>
```

### 4. Calculator Features

#### Home Loan Health Check (`@gp/feature/home-loan-health-check`)
Comprehensive loan health check feature.

**Components:**
- `AppointmentBooking`: Appointment scheduling
- `CompetitionBanner`: Promotional banner
- `Disclaimer`: Legal disclaimers
- `Feedback`: User feedback collection
- `FixedRateExpiryDate`: Rate expiry handling
- `OtherTools`: Related tools showcase
- `PayOffSlider`: Payoff calculation slider
- `RepaymentComparison`: Repayment comparison
- `ResultOptions`: Result action options
- `ResultSummary`: Results summary

**Pages:**
- Multiple step pages for the health check flow

**Context & Hooks:**
- Health check context for state management
- `useBestOffer`: Hook for best offer calculation

**Usage:**
```typescript
import { 
  HealthCheckProvider,
  PayOffSlider,
  RepaymentComparison,
  useBestOffer 
} from '@gp/feature/home-loan-health-check';

// Wrap in provider
<HealthCheckProvider>
  <HealthCheckFlow />
</HealthCheckProvider>

// Use components
<PayOffSlider
  currentLoan={loanData}
  onPayoffChange={handlePayoffChange}
/>

<RepaymentComparison
  currentRepayment={currentRepayment}
  newRepayment={newRepayment}
  savings={savings}
/>

// Use hook
const { bestOffer, loading, error } = useBestOffer(loanDetails);
```

### 5. Funnel Features

#### Funnels (`@gp/feature/funnels`)
Shared funnel components and layouts.

**Components:**
- `BookAppointment`: Appointment booking widget
- `FunnelCTAs`: Call-to-action components
- `FunnelFAQs`: FAQ sections with predefined content
- `FunnelLayout`: Standard funnel page layout

**Usage:**
```typescript
import { 
  FunnelLayout,
  FunnelCTAs,
  FunnelFAQs,
  BookAppointment 
} from '@gp/feature/funnels';

// Standard funnel page
<FunnelLayout
  title="Home Loan Application"
  progress={75}
  onBack={handleBack}
>
  <FunnelStep />
  
  <FunnelCTAs
    primaryAction={{ label: "Continue", onClick: handleContinue }}
    secondaryAction={{ label: "Save & Exit", onClick: handleSave }}
  />
  
  <FunnelFAQs category="home-loans" />
</FunnelLayout>

// Appointment booking
<BookAppointment
  serviceType="home-loan"
  customerData={customerData}
  onBookingComplete={handleBooking}
/>
```

#### New Purchase (`@gp/feature/new-purchase`)
New property purchase funnel components.

**Components:**
- `BuyingSituation`: Buying situation assessment
- `NewProperty`: Property details form
- `PersonalDetails`: Personal information form
- `Preferences`: Loan preferences form

**Usage:**
```typescript
import { 
  BuyingSituation,
  NewProperty,
  PersonalDetails,
  Preferences 
} from '@gp/feature/new-purchase';

// Multi-step funnel
<FunnelLayout>
  <BuyingSituation onComplete={handleBuyingSituation} />
  <NewProperty onComplete={handleProperty} />
  <PersonalDetails onComplete={handlePersonalDetails} />
  <Preferences onComplete={handlePreferences} />
</FunnelLayout>
```

#### Refinance (`@gp/feature/refinance`)
Refinance funnel components.

**Components:**
- `CurrentLoan`: Current loan details form
- `PersonalDetails`: Personal information form
- `Preferences`: Refinance preferences
- `Property`: Property information form

**Usage:**
```typescript
import { 
  CurrentLoan,
  Property,
  Preferences 
} from '@gp/feature/refinance';

// Refinance flow
<FunnelLayout>
  <CurrentLoan onComplete={handleCurrentLoan} />
  <Property onComplete={handleProperty} />
  <Preferences onComplete={handlePreferences} />
</FunnelLayout>
```

### 6. Property Features

#### Property Hub (`@gp/feature/property-hub`)
Comprehensive property search and management.

**Components:**
- 130+ components for property features
- Property search and filtering
- Property details and comparisons
- Map integration
- Property valuation tools
- Property watchlists

**Pages:**
- Property search results
- Property detail pages
- Property comparison pages
- User property dashboard

**Hooks:**
- Property data management hooks
- Search and filtering hooks

**Usage:**
```typescript
import { 
  PropertySearch,
  PropertyCard,
  PropertyDetails,
  PropertyComparison,
  usePropertySearch 
} from '@gp/feature/property-hub';

// Property search
const { 
  properties, 
  loading, 
  searchProperties 
} = usePropertySearch();

<PropertySearch
  onSearch={searchProperties}
  filters={searchFilters}
  results={properties}
  loading={loading}
/>

// Property details
<PropertyDetails
  propertyId={propertyId}
  showValuation={true}
  showComparables={true}
/>
```

### 7. Utility Features

#### Mobile App (`@gp/feature/mobile-app`)
Mobile app promotion and download features.

**Components:**
- App promotion banners
- Download buttons
- Feature showcases

**Pages:**
- Mobile app landing pages
- App download pages

**Usage:**
```typescript
import { 
  AppPromoBanner,
  AppDownloadPage 
} from '@gp/feature/mobile-app';

<AppPromoBanner
  showOnMobile={false}
  downloadLinks={{
    ios: "https://apps.apple.com/app/id123",
    android: "https://play.google.com/store/apps/details?id=com.app"
  }}
/>
```

#### Copilot (`@gp/feature/copilot`)
AI chat integration features.

**Components:**
- `CopilotChat`: AI chat interface

**Usage:**
```typescript
import { CopilotChat } from '@gp/feature/copilot';

<CopilotChat
  context="home-loan-application"
  customerData={customerData}
  onMessageSent={handleMessage}
/>
```

## Feature Development Patterns

### 1. Component Structure
```typescript
// Feature component with props interface
interface FeatureComponentProps {
  data: FeatureData;
  onAction: (action: ActionType) => void;
  loading?: boolean;
  error?: Error;
}

export function FeatureComponent({ 
  data, 
  onAction, 
  loading = false, 
  error 
}: FeatureComponentProps) {
  if (loading) return <Loader />;
  if (error) return <ErrorDisplay error={error} />;
  
  return (
    <Container>
      <FeatureContent data={data} />
      <FeatureActions onAction={onAction} />
    </Container>
  );
}
```

### 2. Page Structure
```typescript
// Feature page with data fetching
export function FeaturePage() {
  const [data, setData] = useState<FeatureData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getFeatureData();
        setData(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  return (
    <PageLayout>
      <FeatureComponent
        data={data}
        loading={loading}
        error={error}
        onAction={handleAction}
      />
    </PageLayout>
  );
}
```

### 3. Context Pattern
```typescript
// Feature context for state management
interface FeatureContextType {
  state: FeatureState;
  actions: FeatureActions;
  loading: boolean;
  error: Error | null;
}

const FeatureContext = createContext<FeatureContextType | null>(null);

export function FeatureProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(featureReducer, initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const actions = useMemo(() => ({
    updateFeature: (data: UpdateData) => {
      dispatch({ type: 'UPDATE_FEATURE', payload: data });
    },
    resetFeature: () => {
      dispatch({ type: 'RESET_FEATURE' });
    }
  }), []);
  
  const contextValue = {
    state,
    actions,
    loading,
    error
  };
  
  return (
    <FeatureContext.Provider value={contextValue}>
      {children}
    </FeatureContext.Provider>
  );
}

export function useFeature() {
  const context = useContext(FeatureContext);
  if (!context) {
    throw new Error('useFeature must be used within FeatureProvider');
  }
  return context;
}
```

### 4. Custom Hooks
```typescript
// Feature-specific hooks
export function useFeatureData(id: string) {
  const [data, setData] = useState<FeatureData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getFeatureData(id);
        setData(result);
        setError(null);
      } catch (err) {
        setError(err as Error);
        setData(null);
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      fetchData();
    }
  }, [id]);
  
  const refetch = useCallback(() => {
    if (id) {
      fetchData();
    }
  }, [id]);
  
  return { data, loading, error, refetch };
}
```

## Best Practices

### 1. Component Composition
```typescript
// Compose features from smaller components
export function CompleteFeature() {
  return (
    <FeatureProvider>
      <FeatureHeader />
      <FeatureContent>
        <FeatureStep1 />
        <FeatureStep2 />
        <FeatureStep3 />
      </FeatureContent>
      <FeatureFooter />
    </FeatureProvider>
  );
}
```

### 2. Error Boundaries
```typescript
// Feature-level error boundary
export class FeatureErrorBoundary extends Component<
  { children: ReactNode; fallback?: ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Feature error:', error, errorInfo);
    // Log to error tracking service
  }
  
  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} />;
    }
    
    return this.props.children;
  }
}
```

### 3. Performance Optimization
```typescript
// Lazy load feature components
const LazyFeatureComponent = lazy(() => import('./FeatureComponent'));

export function FeaturePage() {
  return (
    <Suspense fallback={<FeatureLoader />}>
      <LazyFeatureComponent />
    </Suspense>
  );
}

// Memoize expensive computations
export const FeatureComponent = memo(function FeatureComponent({ data }: Props) {
  const processedData = useMemo(() => {
    return processExpensiveData(data);
  }, [data]);
  
  return <FeatureDisplay data={processedData} />;
});
```

### 4. Testing Strategy
```typescript
// Feature component testing
describe('FeatureComponent', () => {
  const defaultProps = {
    data: mockFeatureData,
    onAction: jest.fn(),
    loading: false,
    error: null
  };
  
  it('renders feature content when data is available', () => {
    render(<FeatureComponent {...defaultProps} />);
    expect(screen.getByText('Feature Content')).toBeInTheDocument();
  });
  
  it('shows loading state', () => {
    render(<FeatureComponent {...defaultProps} loading={true} />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  it('handles user actions', async () => {
    const onAction = jest.fn();
    render(<FeatureComponent {...defaultProps} onAction={onAction} />);
    
    await user.click(screen.getByRole('button', { name: 'Submit' }));
    expect(onAction).toHaveBeenCalledWith(expect.objectContaining({
      type: 'SUBMIT'
    }));
  });
});
```

## Integration Guidelines

### 1. With UI Components
```typescript
// Use UI components within features
import { ButtonWithTracking, Card, Modal } from '@gp/ui/components';

export function FeatureComponent() {
  return (
    <Card>
      <FeatureContent />
      <ButtonWithTracking
        label="Complete Feature"
        position="Feature Component"
        onClick={handleComplete}
      />
    </Card>
  );
}
```

### 2. With Data Access
```typescript
// Use data access libraries for data operations
import { getFeatureData, updateFeatureData } from '@gp/data-access/feature';

export function useFeatureOperations() {
  const fetchData = useCallback(async (id: string) => {
    return getFeatureData(id);
  }, []);
  
  const updateData = useCallback(async (id: string, data: UpdateData) => {
    return updateFeatureData(id, data);
  }, []);
  
  return { fetchData, updateData };
}
```

### 3. With Utilities
```typescript
// Use utility libraries for common operations
import { formatCurrency } from '@gp/util/intl';
import { trackEvent } from '@gp/util/analytics';

export function FeatureWithUtils({ amount }: { amount: number }) {
  const handleAction = () => {
    trackEvent('feature_action', { amount });
  };
  
  return (
    <div>
      <Text>Amount: {formatCurrency(amount)}</Text>
      <Button onClick={handleAction}>Continue</Button>
    </div>
  );
}
```