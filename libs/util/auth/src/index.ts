/**
 * Authentication utility exports
 *
 * All components should import authentication functionality from this file. This will allow
 * future updates to underlying implementations for authentication to be modified without updating n files.
 *
 * Ideally we can just replace the underlying implementation here and it should reflect throughout the application.
 */

import { AuthService, Brand, Environment, Type } from '@lendi/auth';

export type AuthBrand = Brand;
export type AuthEnvironment = Environment;
export type AuthType = Type;

export interface AuthConfig {
  brand: AuthBrand;
  type: AuthType;
  environment: AuthEnvironment;
  phoneValidation?: {
    enableDeepValidation?: boolean;
    allowedCountries?: string[];
    requireValidCountry?: boolean;
  };
  debug?: boolean;
  retry?: {
    maxAttempts: number;
    backoffFactor: number;
    initialDelay: number;
  };
}

export class AuthUtil {
  private authService: AuthService;

  constructor(
    brand: AuthBrand,
    environment: AuthEnvironment,
    type: AuthType = Type.Customer,
    config?: Partial<AuthConfig>
  ) {
    this.authService = this.init(brand, environment, type, config);
  }

  private init(
    brand: AuthBrand,
    environment: AuthEnvironment,
    type: AuthType,
    config?: Partial<AuthConfig>
  ): AuthService {
    const defaultConfig: AuthConfig = {
      brand,
      type,
      environment,
      phoneValidation: this.getPhoneValidationConfig(),
      debug: environment === Environment.Development,
      // TODO: Uncomment when we have decided on our retry config
      // retry: {
      //   maxAttempts: 3,
      //   backoffFactor: 2,
      //   initialDelay: 1000,
      // },
    };

    const finalConfig = { ...defaultConfig, ...config };

    return new AuthService(finalConfig);
  }

  private getPhoneValidationConfig() {
    return {
      enableDeepValidation: true,
      allowedCountries: ['AU'],
      requireValidCountry: true,
    };
  }

  // Proxy methods to AuthService
  public async sendCode(phoneNumber: string): Promise<void> {
    return this.authService.sendCode({ phoneNumber });
  }

  public async verifyCode(params: {
    phone: string;
    code: string;
    email?: string;
    firstName?: string;
    leadId?: string;
  }): Promise<any> {
    return this.authService.verifyCode(params);
  }

  public getToken(): string | undefined {
    return this.authService.getToken();
  }

  public getStatus(): any {
    return this.authService.getStatus();
  }

  public logout(): void {
    this.authService.logout();
  }

  public getSessionState(): any {
    return this.authService.getSessionState();
  }

  public isAuthenticated(): boolean {
    const status = this.authService.getStatus();
    return status === 'authenticated';
  }

  public getUserId(): string | null {
    const sessionState = this.authService.getSessionState();
    return sessionState?.identity?.id || null;
  }
}
