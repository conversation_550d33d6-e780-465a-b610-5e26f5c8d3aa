# Auth Utils

A centralized authentication utility that wraps `@lendi/auth` with simplified configuration and brand-specific settings.

## Purpose

This utility provides a consistent authentication interface across all applications in the growth-product workspace, similar to how `util/analytics` works. It centralizes:

- Brand-specific configurations
- Environment-specific settings
- Phone validation rules
- Common authentication patterns

## Usage

```ts
import { AuthUtil } from '@gp/util/auth';
import { Brand, Environment } from '@lendi/lala-utils';

// Simple initialization with brand and environment
const auth = new AuthUtil(Brand.Aussie, Environment.Development);

// Send authentication code
await auth.sendCode('+61412345678');

// Verify code with user data
const result = await auth.verifyCode({
  phone: '+61412345678',
  code: '123456',
  email: '<EMAIL>',
  firstName: 'John',
  leadId: 'lead-123',
});

// Check authentication status
if (auth.isAuthenticated()) {
  console.log('User is authenticated');
  console.log('User ID:', auth.getUserId());
}
```

## Local HTTPS and Reverse Proxy for aussie-dev.com.au via Caddy

This guide maps `aussie-dev.com.au` and `www.aussie-dev.com.au` to localhost, generates trusted local TLS certificates with `mkcert`, and runs Caddy as a reverse proxy to `127.0.0.1:3000` on ports 80/443 (with HTTP→HTTPS redirect).

### 1) Update /etc/hosts

```bash
sudo /usr/bin/sed -i '' '/aussie-dev\.com\.au/d' /etc/hosts
echo '127.0.0.1 aussie-dev.com.au www.aussie-dev.com.au' | sudo tee -a /etc/hosts >/dev/null
sudo dscacheutil -flushcache; sudo killall -HUP mDNSResponder
```

### 2) Install tools

```bash
brew install caddy mkcert nss
```

### 3) Trust mkcert root CA

```bash
mkcert -install
```

- macOS: in Keychain Access → System keychain → Certificates, ensure the mkcert root is set to “Always Trust”.
- Firefox: `nss` enables installation into its store; restart Firefox after the step above.

### 4) Create local certificates

```bash
sudo mkdir -p /opt/homebrew/etc/certificates
sudo chown "$USER":staff /opt/homebrew/etc/certificates

mkcert -cert-file /opt/homebrew/etc/certificates/aussie-dev.pem \
       -key-file  /opt/homebrew/etc/certificates/aussie-dev.key \
       aussie-dev.com.au www.aussie-dev.com.au
```

### 5) Configure Caddy

If you do not have an existing Caddyfile, create `/opt/homebrew/etc/Caddyfile` with the following steps:

To create:
`sudo mkdir -p /opt/homebrew/etc`

Then run:

```bash
sudo tee /opt/homebrew/etc/Caddyfile > /dev/null << 'EOF'
aussie-dev.com.au, www.aussie-dev.com.au {
tls /opt/homebrew/etc/certificates/aussie-dev.pem /opt/homebrew/etc/certificates/aussie-dev.key
reverse_proxy 127.0.0.1:3000
}

http://aussie-dev.com.au, http://www.aussie-dev.com.au {
redir https://{host}{uri} permanent
}
EOF
```

To check:
`cat /opt/homebrew/etc/Caddyfile`

If you already have an existing Caddyfile, simply replace the content with:

```caddy
aussie-dev.com.au, www.aussie-dev.com.au {
  tls /opt/homebrew/etc/certificates/aussie-dev.pem /opt/homebrew/etc/certificates/aussie-dev.key
  reverse_proxy 127.0.0.1:3000
}

http://aussie-dev.com.au, http://www.aussie-dev.com.au {
  redir https://{host}{uri} permanent
}
```

### 6) Validate and run Caddy (binds 80/443)

```bash
sudo caddy validate --config /opt/homebrew/etc/Caddyfile --adapter caddyfile
sudo caddy start --config /opt/homebrew/etc/Caddyfile --adapter caddyfile
# For changes later:
sudo caddy reload --config /opt/homebrew/etc/Caddyfile --adapter caddyfile
# To stop:
sudo caddy stop
```

### 7) Test

- Open: `https://aussie-dev.com.au` and `https://www.aussie-dev.com.au`.
- If you still see a cert error:
  - Ensure mkcert root is “Always Trust” in System Keychain.
    🔧 What to do:
    1. In Keychain Access, click on "System" in the left sidebar
    2. Select "Certificates" category
    3. Find the mkcert root certificate (usually named with "mkcert" and your username/hostname)
    4. Double-click the certificate
    5. Expand "Trust" section
    6. Set "When using this certificate" to "Always Trust"
    7. This ensures that certificates generated by mkcert (like the ones for aussie-dev.com.au) will be trusted by your system and browsers.
  - In Chrome, clear HSTS for the domain (chrome://net-internals/#hsts → Delete domain security policies).
    🔧 Steps to Clear HSTS in Chrome:
    1. Open Chrome's Net Internals:
       Open Chrome
       In the address bar, type: chrome://net-internals/#hsts
       Press Enter
    2. Delete Domain Security Policies:
       Scroll down to the "Delete domain security policies" section
       In the "Domain" field, enter: aussie-dev.com.au
       Click the "Delete" button
       Repeat for www.aussie-dev.com.au if needed
    3. Query to Verify (Optional):
       Scroll up to the "Query HSTS/PKP domain" section
       Enter aussie-dev.com.au in the Domain field
       Click "Query"
       It should return "Not found" if successfully deleted 4. Restart Chrome:
       Close all Chrome windows
       Reopen Chrome
       Try accessing https://aussie-dev.com.au again
       💡 What this does:
       HSTS (HTTP Strict Transport Security) forces browsers to use HTTPS. Sometimes browsers cache old certificate information, causing SSL errors even after installing new certificates. Clearing HSTS removes this cached security policy, allowing Chrome to accept your new mkcert certificates.
  - Confirm the served cert SANs:
    ```bash
    openssl s_client -showcerts -servername aussie-dev.com.au -connect 127.0.0.1:443 < /dev/null 2>/dev/null | \
      openssl x509 -noout -subject -issuer -ext subjectAltName
    ```

### 8) To switch between production version of aussie-dev.com.au and reverse proxy:

#### 1) Switch aussie-dev.com.au back to PRODUCTION (what we just did):**

```bash
# Remove localhost mapping from hosts file
sudo /usr/bin/sed -i '' '/aussie-dev\.com\.au/d' /etc/hosts

# Flush DNS cache
sudo dscacheutil -flushcache; sudo killall -HUP mDNSResponder

# Stop Caddy reverse proxy (if running)
sudo caddy stop
```

#### 2) Switch back to LOCAL REVERSE PROXY (when you need local development):**

```bash
# Add localhost mapping back to hosts file
echo '127.0.0.1 aussie-dev.com.au www.aussie-dev.com.au' | sudo tee -a /etc/hosts

# Flush DNS cache
sudo dscacheutil -flushcache; sudo killall -HUP mDNSResponder

# Start Caddy reverse proxy
sudo caddy start --config /opt/homebrew/etc/Caddyfile --adapter caddyfile

# Start your development server
pnpm aussie:dev
```

#### **Quick Reference:**
- **Production**: Remove from `/etc/hosts` → Flush DNS → Stop Caddy
- **Local Dev**: Add to `/etc/hosts` → Flush DNS → Start Caddy → Start dev server

#### **Verification Commands:**
```bash
# Check current hosts file
grep "aussie-dev" /etc/hosts

# Test DNS resolution
nslookup aussie-dev.com.au

# Test connection
curl -I https://aussie-dev.com.au --max-time 5
```

### Notes

- Keep `/etc/hosts` without ports; ports are handled by the proxy/browser, not hosts.
- `mkcert` avoids warnings by creating and trusting a local CA.
- You can switch the `reverse_proxy` target if your app runs on a different port.
