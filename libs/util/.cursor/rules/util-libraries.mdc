# Utility Libraries Guidelines

## Overview
Utility libraries contain pure functions, helpers, and common utilities that can be used across the entire application. They provide reusable functionality for analytics, calculations, data formatting, HTTP requests, and other common operations.

## Import Pattern
```typescript
import { utilityFunction, UtilityClass } from '@gp/util/utility-name';
```

## Available Utility Libraries

### 1. Analytics (`@gp/util/analytics`)
Analytics tracking and event management utilities.

**Usage:**
```typescript
import { trackEvent, trackPageView, setUserProperties } from '@gp/util/analytics';

// Track user events
trackEvent('button_clicked', {
  buttonName: 'Get Started',
  location: 'Hero Section',
  userId: 'user-123'
});

// Track page views
trackPageView('/home-loans', {
  title: 'Home Loans',
  category: 'Product Pages'
});

// Set user properties
setUserProperties({
  plan: 'premium',
  location: 'sydney',
  signupDate: '2024-01-15'
});
```

**Event Patterns:**
```typescript
// Standard event structure
export interface AnalyticsEvent {
  eventName: string;
  properties?: Record<string, any>;
  userId?: string;
  timestamp?: Date;
}

// Common event types
export const ANALYTICS_EVENTS = {
  // User actions
  BUTTON_CLICKED: 'button_clicked',
  FORM_SUBMITTED: 'form_submitted',
  LINK_CLICKED: 'link_clicked',
  
  // Navigation
  PAGE_VIEWED: 'page_viewed',
  SECTION_VIEWED: 'section_viewed',
  
  // Business events
  CALCULATOR_USED: 'calculator_used',
  QUOTE_REQUESTED: 'quote_requested',
  APPLICATION_STARTED: 'application_started',
  
  // Engagement
  VIDEO_PLAYED: 'video_played',
  DOCUMENT_DOWNLOADED: 'document_downloaded',
  CHAT_INITIATED: 'chat_initiated'
} as const;
```

### 2. Calculators (`@gp/util/calculators`)
Financial calculation utilities and loan-related math functions.

**Repayment Calculations:**
```typescript
import { 
  calculateMonthlyRepayment,
  calculateTotalInterest,
  calculatePayoffTime,
  generateAmortizationSchedule 
} from '@gp/util/calculators';

// Calculate monthly loan repayment
const monthlyPayment = calculateMonthlyRepayment({
  principal: 500000, // Loan amount
  annualRate: 0.035, // 3.5% annual interest rate
  termYears: 30      // 30-year loan
});

// Calculate total interest over loan term
const totalInterest = calculateTotalInterest({
  principal: 500000,
  annualRate: 0.035,
  termYears: 30
});

// Calculate payoff time with extra payments
const payoffTime = calculatePayoffTime({
  principal: 500000,
  annualRate: 0.035,
  monthlyPayment: 2500, // Including extra payments
  currentBalance: 450000
});

// Generate full amortization schedule
const schedule = generateAmortizationSchedule({
  principal: 500000,
  annualRate: 0.035,
  termYears: 30,
  extraPayment: 200 // Optional extra monthly payment
});
```

**Borrowing Power Calculations:**
```typescript
import { 
  calculateBorrowingPower,
  calculateServiceability,
  calculateLVR 
} from '@gp/util/calculators';

// Calculate maximum borrowing capacity
const borrowingPower = calculateBorrowingPower({
  grossIncome: 100000,        // Annual gross income
  otherIncome: 10000,         // Other income sources
  monthlyExpenses: 3000,      // Monthly living expenses
  existingDebts: 500,         // Monthly debt payments
  dependents: 2,              // Number of dependents
  interestRate: 0.035,        // Assessment rate
  loanTerm: 30               // Loan term in years
});

// Calculate loan serviceability ratio
const serviceability = calculateServiceability({
  monthlyIncome: 8000,
  monthlyExpenses: 4000,
  monthlyLoanPayment: 2200
});

// Calculate Loan-to-Value Ratio
const lvr = calculateLVR({
  loanAmount: 400000,
  propertyValue: 500000
});
```

**Utility Functions:**
```typescript
import { 
  formatCurrency,
  formatPercentage,
  roundToNearestCent,
  validateFinancialInput 
} from '@gp/util/calculators';

// Format currency values
const formatted = formatCurrency(1234567.89); // "$1,234,567.89"

// Format percentage values
const percentage = formatPercentage(0.035); // "3.50%"

// Round to nearest cent
const rounded = roundToNearestCent(123.456); // 123.46

// Validate financial inputs
const isValid = validateFinancialInput({
  amount: 500000,
  rate: 0.035,
  term: 30
});
```

### 3. Data Service (`@gp/util/data-service`)
HTTP client utilities and API communication helpers.

**HTTP Client:**
```typescript
import { httpClient, createApiClient } from '@gp/util/data-service';

// Basic HTTP operations
const response = await httpClient.get('/api/properties');
const created = await httpClient.post('/api/customers', customerData);
const updated = await httpClient.put('/api/customers/123', updateData);
const deleted = await httpClient.delete('/api/customers/123');

// Create API client with base configuration
const apiClient = createApiClient({
  baseURL: 'https://api.example.com',
  timeout: 10000,
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

**Contentful Integration:**
```typescript
import { 
  createContentfulClient,
  getContentfulEntry,
  getContentfulEntries 
} from '@gp/util/data-service';

// Create Contentful client
const contentfulClient = createContentfulClient({
  space: process.env.CONTENTFUL_SPACE_ID,
  accessToken: process.env.CONTENTFUL_ACCESS_TOKEN,
  environment: 'master'
});

// Get single entry
const entry = await getContentfulEntry('landing-page-id');

// Get multiple entries with filtering
const entries = await getContentfulEntries({
  content_type: 'article',
  'fields.category': 'home-loans',
  limit: 10,
  order: '-sys.createdAt'
});
```

**Request/Response Utilities:**
```typescript
import { 
  withRetry,
  withTimeout,
  handleApiError,
  transformResponse 
} from '@gp/util/data-service';

// Retry failed requests
const dataWithRetry = await withRetry(
  () => httpClient.get('/api/data'),
  { maxRetries: 3, delay: 1000 }
);

// Add timeout to requests
const dataWithTimeout = await withTimeout(
  httpClient.get('/api/slow-endpoint'),
  5000 // 5 second timeout
);

// Handle API errors consistently
try {
  const data = await apiCall();
} catch (error) {
  const handledError = handleApiError(error);
  // Handle based on error type
}
```

### 4. Internationalization (`@gp/util/intl`)
Formatting and localization utilities.

**Number Formatting:**
```typescript
import { 
  formatNumber,
  formatCurrency,
  formatPercentage,
  getOrdinal 
} from '@gp/util/intl';

// Format numbers with locale-specific formatting
const formatted = formatNumber(1234567.89); // "1,234,567.89"
const currency = formatCurrency(1234567.89, 'AUD'); // "$1,234,567.89"
const percentage = formatPercentage(0.1234); // "12.34%"

// Get ordinal numbers
const first = getOrdinal(1); // "1st"
const second = getOrdinal(2); // "2nd"
const third = getOrdinal(3); // "3rd"
```

**Duration Formatting:**
```typescript
import { formatDuration } from '@gp/util/intl';

// Format durations in human-readable format
const duration1 = formatDuration(90); // "1 hour 30 minutes"
const duration2 = formatDuration(3661); // "1 hour 1 minute 1 second"
const duration3 = formatDuration(86400); // "1 day"
```

**Configuration:**
```typescript
// Locale configuration
export const LOCALE_CONFIG = {
  default: 'en-AU',
  currency: 'AUD',
  timezone: 'Australia/Sydney',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm'
} as const;
```

### 5. Interaction Studio (`@gp/util/interaction-studio`)
Salesforce Interaction Studio integration utilities.

**Usage:**
```typescript
import { 
  trackInteraction,
  identifyUser,
  trackPageView,
  trackPurchase 
} from '@gp/util/interaction-studio';

// Track user interactions
trackInteraction('calculator_used', {
  calculatorType: 'borrowing-power',
  loanAmount: 500000,
  result: 'qualified'
});

// Identify users for personalization
identifyUser({
  userId: 'user-123',
  email: '<EMAIL>',
  attributes: {
    plan: 'premium',
    location: 'sydney',
    interests: ['home-loans', 'investment']
  }
});

// Track page views for personalization
trackPageView({
  page: '/home-loans',
  category: 'product-pages',
  attributes: {
    userType: 'returning',
    referrer: 'google'
  }
});
```

### 6. Leads (`@gp/util/leads`)
Lead management and tracking utilities.

**Lead Tracking:**
```typescript
import { setLeads, trackLeadSource, updateLeadStatus } from '@gp/util/leads';

// Set lead information
setLeads({
  leadId: 'lead-123',
  source: 'calculator',
  campaign: 'home-loan-promo',
  customerData: {
    email: '<EMAIL>',
    phone: '+61412345678',
    interests: ['home-loan']
  }
});

// Track lead sources
trackLeadSource({
  source: 'google-ads',
  medium: 'cpc',
  campaign: 'home-loan-campaign',
  term: 'home loan calculator',
  content: 'ad-variant-a'
});

// Update lead status
updateLeadStatus('lead-123', {
  status: 'qualified',
  score: 85,
  notes: 'High-value prospect, ready for broker contact'
});
```

### 7. Logging (`@gp/util/logging`)
Structured logging utilities and error tracking.

**Logger Usage:**
```typescript
import { logger } from '@gp/util/logging';

// Different log levels
logger.debug('Debug information', { userId: 'user-123', action: 'page-load' });
logger.info('User action completed', { action: 'form-submit', formId: 'contact' });
logger.warn('Potential issue detected', { issue: 'slow-response', duration: 5000 });
logger.error('Error occurred', { error: error.message, stack: error.stack });

// Structured logging with context
logger.withContext({ userId: 'user-123', sessionId: 'session-456' })
  .info('Calculator used', { 
    calculatorType: 'borrowing-power',
    result: { maxLoan: 500000, approved: true }
  });
```

**Error Logging:**
```typescript
import { logError, logWarning, logInfo } from '@gp/util/logging';

// Log errors with full context
logError('API request failed', {
  error: error.message,
  url: '/api/properties',
  method: 'GET',
  userId: 'user-123',
  timestamp: new Date().toISOString()
});

// Log warnings
logWarning('Slow API response', {
  url: '/api/slow-endpoint',
  duration: 5000,
  threshold: 2000
});

// Log info events
logInfo('Feature flag evaluated', {
  flag: 'new-calculator',
  result: true,
  userId: 'user-123'
});
```

### 8. OpenAPI (`@gp/util/openapi`)
OpenAPI specification utilities and type generation.

**Usage:**
```typescript
import { 
  generateApiTypes,
  validateApiResponse,
  createApiClient 
} from '@gp/util/openapi';

// Generate TypeScript types from OpenAPI spec
const types = await generateApiTypes('/api/openapi.json');

// Validate API responses against schema
const isValid = validateApiResponse(response, '/properties/{id}', 'GET');

// Create typed API client
const apiClient = createApiClient<PropertyApi>({
  baseUrl: 'https://api.example.com',
  spec: propertyApiSpec
});

// Type-safe API calls
const property = await apiClient.getProperty({ id: 'prop-123' });
```

### 9. Session (`@gp/util/session`)
Session management and user context utilities.

**Session Management:**
```typescript
import { 
  getCustomerSession,
  getBrand,
  getEnv,
  jwtDecode 
} from '@gp/util/session';

// Get customer session information
const session = getCustomerSession();
if (session.isAuthenticated) {
  console.log('User:', session.user);
  console.log('Permissions:', session.permissions);
}

// Get current brand context
const brand = getBrand(); // 'aussie' | 'lendi'

// Get environment information
const env = getEnv(); // 'development' | 'staging' | 'production'

// Decode JWT tokens
const decoded = jwtDecode(authToken);
console.log('Token expires:', new Date(decoded.exp * 1000));
```

**Session Utilities:**
```typescript
import { 
  isSessionValid,
  refreshSession,
  clearSession,
  getSessionData 
} from '@gp/util/session';

// Check session validity
if (!isSessionValid()) {
  await refreshSession();
}

// Get specific session data
const userData = getSessionData('user');
const preferences = getSessionData('preferences');

// Clear session on logout
const handleLogout = () => {
  clearSession();
  window.location.href = '/login';
};
```

### 10. URL (`@gp/util/url`)
URL manipulation and marketing parameter utilities.

**Marketing Parameters:**
```typescript
import { 
  embedMarketingTrackingParams,
  persistMarketingTrackingParams,
  generateReturnURL 
} from '@gp/util/url';

// Embed marketing tracking parameters in URLs
const trackedUrl = embedMarketingTrackingParams('/apply', {
  utm_source: 'calculator',
  utm_medium: 'web',
  utm_campaign: 'home-loan-promo',
  utm_content: 'cta-button'
});

// Persist marketing parameters across session
persistMarketingTrackingParams({
  utm_source: 'google',
  utm_medium: 'cpc',
  utm_campaign: 'brand-search'
});

// Generate return URLs for redirects
const returnUrl = generateReturnURL('/dashboard', {
  preserveParams: true,
  addTimestamp: true
});
```

**URL Utilities:**
```typescript
import { 
  parseQueryParams,
  buildQueryString,
  isValidUrl,
  extractDomain 
} from '@gp/util/url';

// Parse query parameters
const params = parseQueryParams(window.location.search);

// Build query strings
const queryString = buildQueryString({
  page: 1,
  limit: 20,
  category: 'home-loans'
});

// Validate URLs
const isValid = isValidUrl('https://example.com/path');

// Extract domain from URL
const domain = extractDomain('https://www.example.com/path?query=1');
```

## Utility Development Patterns

### 1. Pure Functions
```typescript
// Utilities should be pure functions when possible
export function calculateLoanPayment(
  principal: number,
  rate: number,
  term: number
): number {
  const monthlyRate = rate / 12;
  const numPayments = term * 12;
  
  return (principal * monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
         (Math.pow(1 + monthlyRate, numPayments) - 1);
}

// Input validation
export function validateLoanInputs(inputs: LoanInputs): ValidationResult {
  const errors: string[] = [];
  
  if (inputs.principal <= 0) {
    errors.push('Principal must be greater than zero');
  }
  
  if (inputs.rate < 0 || inputs.rate > 1) {
    errors.push('Rate must be between 0 and 1');
  }
  
  if (inputs.term <= 0) {
    errors.push('Term must be greater than zero');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

### 2. Configuration Objects
```typescript
// Use configuration objects for complex utilities
export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

export function createHttpClient(config: HttpClientConfig) {
  return {
    get: (url: string) => makeRequest('GET', url, config),
    post: (url: string, data: any) => makeRequest('POST', url, config, data),
    put: (url: string, data: any) => makeRequest('PUT', url, config, data),
    delete: (url: string) => makeRequest('DELETE', url, config)
  };
}
```

### 3. Error Handling
```typescript
// Consistent error handling in utilities
export class UtilityError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'UtilityError';
  }
}

export function safeCalculation<T>(
  calculation: () => T,
  errorMessage: string
): T {
  try {
    return calculation();
  } catch (error) {
    throw new UtilityError(
      errorMessage,
      'CALCULATION_ERROR',
      { originalError: error }
    );
  }
}
```

### 4. Type Safety
```typescript
// Strong typing for utility functions
export interface FormatCurrencyOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

export function formatCurrency(
  amount: number,
  options: FormatCurrencyOptions = {}
): string {
  const {
    currency = 'AUD',
    locale = 'en-AU',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options;
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits
  }).format(amount);
}
```

## Testing Utilities

### 1. Unit Testing
```typescript
describe('calculateLoanPayment', () => {
  it('calculates correct monthly payment', () => {
    const payment = calculateLoanPayment(500000, 0.035, 30);
    expect(payment).toBeCloseTo(2245.22, 2);
  });
  
  it('handles edge cases', () => {
    expect(() => calculateLoanPayment(0, 0.035, 30))
      .toThrow('Principal must be greater than zero');
  });
});
```

### 2. Mock Utilities
```typescript
// Mock utilities for testing
export const mockHttpClient = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
};

export const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};
```

## Best Practices

### 1. Keep Functions Small and Focused
- Each utility should have a single responsibility
- Functions should be easily testable
- Avoid side effects in utility functions

### 2. Use TypeScript Effectively
- Provide strong typing for all functions
- Use generics where appropriate
- Document complex types with JSDoc

### 3. Error Handling
- Provide meaningful error messages
- Use custom error types for different error categories
- Handle edge cases gracefully

### 4. Performance Considerations
- Memoize expensive calculations
- Use lazy evaluation where appropriate
- Avoid unnecessary object creation

### 5. Documentation
- Document all public APIs
- Provide usage examples
- Document any assumptions or limitations