import Cookies from 'js-cookie';
import type { LDContext } from 'launchdarkly-js-sdk-common';
import { v4 } from 'uuid';
import type { Brand } from '@lendi/lala-react';

import { getEnvVar } from '@gp/util/data-service';

const ENV_KEY =
  getEnvVar('GP_LAUNCHDARKLY_ENV_KEY') ||
  process.env.NEXT_PUBLIC_LAUNCH_DARKLY_ENV_KEY ||
  process.env.NX_PUBLIC_LAUNCH_DARKLY_ENV_KEY;
const API_KEY =
  getEnvVar('GP_LAUNCHDARKLY_API_KEY') ||
  process.env.NEXT_PUBLIC_LAUNCH_DARKLY_API_KEY ||
  process.env.NX_PUBLIC_LAUNCH_DARKLY_API_KEY;

const targetId = Cookies.get('targetId') || v4();

export const getLDSingleKindContext = async (
  brand: Brand,
  customerId?: string
): Promise<LDContext> => {
  if (!customerId) {
    return {
      anonymous: true,
      key: targetId,
      kind: 'user',
      targetId,
      isTeamView: false,
      brand,
    };
  }
  const response = await fetch(
    `https://app.launchdarkly.com/api/v2/users/funnels/${ENV_KEY}/${customerId}`,
    {
      method: 'GET',
      headers: {
        Authorization: API_KEY || '',
      },
    }
  );
  const result = await response.json();
  return {
    key: customerId,
    kind: 'user',
    targetId: result.user?.key || targetId,
    isTeamView: false,
    brand,
  };
};
