import type { LDClient } from 'launchdarkly-js-client-sdk';
import type { Brand } from '@lendi/lala-react';

import { getLDSingleKindContext } from './getLDSingleKindContext';

export const identifyLDUser = async (
  ldClient: LDClient,
  brand: Brand,
  customerId?: string,
  maxRetries = 3 // set up a max retry number
) => {
  const ldSingleKindContext = await getLDSingleKindContext(brand, customerId);
  await ldClient.waitUntilReady();
  const tryToIdentify = async (timeout: number) =>
    await Promise.race([
      ldClient.identify(ldSingleKindContext),
      new Promise((_, reject) =>
        setTimeout(
          reject,
          timeout,
          new Error(`${timeout}ms timeout reached waiting for launchdarkly identify!`)
        )
      ),
    ]);
  for (let i = 0; i <= maxRetries; i++) {
    try {
      await tryToIdentify(1000 * (i + 1)); // increase timeout by 1s for every retry
      break;
    } catch (e) {
      if (i !== maxRetries) {
        // give a 0.1s break before re-attempting ld identify
        await new Promise((r) => setTimeout(r, 100));
      }
    }
  }
};
