'use client';
/* eslint-disable @typescript-eslint/no-empty-function */

import React, { useEffect, useRef, useState } from 'react';
import { useFlags, useLDClient } from 'launchdarkly-react-client-sdk';
import { useSession } from '@lendi/lala-react';
import { Brand, Environment } from '@lendi/lala-utils';

import { Analytics } from '@gp/util/analytics';
import { getEnv } from '@gp/util/session';

import { identifyLDUser } from '../utils/identifyLDUser';

const LD_ANONYMOUS_USER_ID_KEY = 'ld:$anonUserId';

type LaunchDarklyConsumerProps = {
  children?: React.ReactNode;
  env: Environment;
  brand: Brand;
};

interface AnalyticsInstance {
  trackEvent: any;
  trackPage: any;
  identify: any;
}

const initialState = {
  analytics: {
    trackEvent: () => {},
    trackPage: () => {},
    identify: () => {},
    isReady: () => false,
  },
};

interface LDState {
  analytics?: AnalyticsInstance;
}
export const LDContext = React.createContext<LDState>(initialState);

export default function LaunchDarklyConsumer({ children }: LaunchDarklyConsumerProps) {
  const [isLDUserIdentified, setIsLDUserIdentified] = useState(false);
  const [isSegmentUserIdentified, setIsSegmentUserIdentified] = useState(false);
  const ldClient = useLDClient();
  const flags = useFlags();
  const { analyticsWebV4 } = flags;
  const analyticsInstance = useRef<Analytics | undefined>(undefined);

  const { brand, identity } = useSession();
  const analysticsIsReady = analyticsInstance.current?.isReady();

  useEffect(() => {
    if (typeof window === 'undefined') return;
    if (!analysticsIsReady) {
      const env = getEnv();
      analyticsInstance.current = new Analytics(analyticsWebV4, env, brand);
    }
  }, [analysticsIsReady, analyticsWebV4, brand]);

  useEffect(() => {
    if (!ldClient) return;
    const analysticsIsReady = analyticsInstance.current?.isReady();
    if (!analysticsIsReady || !analyticsInstance.current) return;

    identifyLDUser(ldClient, brand, identity?.id).finally(() => {
      setIsLDUserIdentified(true);
    });
  }, [brand, identity?.id, ldClient]);

  useEffect(() => {
    if (isLDUserIdentified) {
      const analyticsCurrentInstance = analyticsInstance.current;
      if (!analyticsCurrentInstance) return;
      Promise.race([
        analyticsCurrentInstance.identify(identity?.id || '', flags),
        new Promise((resolve) => setTimeout(resolve, 500)),
      ]).finally(() => {
        setIsSegmentUserIdentified(true);
      });
    }
  }, [flags, identity?.id, isLDUserIdentified]);

  if (!isLDUserIdentified || !isSegmentUserIdentified) return null;
  const analytics = {
    trackEvent: analyticsInstance.current?.trackEvent.bind(analyticsInstance.current),
    trackPage: analyticsInstance.current?.trackPage.bind(analyticsInstance.current),
    identify: analyticsInstance.current?.identify.bind(analyticsInstance.current),
  };
  return <LDContext.Provider value={{ analytics }}>{children}</LDContext.Provider>;
}

export function HydratedLaunchDarklyConsumer({ children, brand, env }: LaunchDarklyConsumerProps) {
  const flags = useFlags();
  const { identity } = useSession();
  const { analyticsWebV4 } = flags;
  const analyticsInstance = useRef<Analytics | undefined>(undefined);
  const analysticsIsReady = analyticsInstance.current?.isReady();

  useEffect(() => {
    if (typeof window === 'undefined') return;
    if (!analysticsIsReady) {
      analyticsInstance.current = new Analytics(analyticsWebV4, env, brand);
    }
  }, [analysticsIsReady, analyticsWebV4, brand, env]);

  useEffect(() => {
    const analyticsCurrentInstance = analyticsInstance.current;

    if (!analyticsCurrentInstance || !analysticsIsReady) return;
    Promise.race([
      analyticsCurrentInstance.identify(identity?.id || '', flags),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).finally(() => {});
  }, [analysticsIsReady, flags, identity?.id]);

  const analytics = React.useMemo(
    () => ({
      trackEvent: analysticsIsReady
        ? analyticsInstance.current?.trackEvent.bind(analyticsInstance.current)
        : () => {},
      trackPage: analysticsIsReady
        ? analyticsInstance.current?.trackPage.bind(analyticsInstance.current)
        : () => {},
      identify: analysticsIsReady
        ? analyticsInstance.current?.identify.bind(analyticsInstance.current)
        : () => {},
    }),
    [analysticsIsReady]
  );

  useEffect(() => {
    if (!analysticsIsReady) return;
    Promise.race([
      analytics?.identify?.(identity?.id || '', flags),
      new Promise((resolve) => setTimeout(resolve, 500)),
    ]).then(() => {
      console.log('Called identify');
    });
  }, [flags, identity?.id, analytics, analysticsIsReady]);

  return <LDContext.Provider value={{ analytics }}>{children}</LDContext.Provider>;
}
