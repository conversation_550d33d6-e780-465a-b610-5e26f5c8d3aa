# Shared Libraries Guidelines

## Overview
Shared libraries contain cross-cutting concerns and common functionality used across multiple applications and features. They provide foundational services like app providers, feature flags, session management, and error handling.

## Import Pattern
```typescript
import { SharedComponent, SharedProvider } from '@gp/shared/library-name';
```

## Available Shared Libraries

### 1. App Providers (`@gp/shared/app-providers`)
Core application providers and context setup.

**Components:**
- `AppProviders`: Main provider wrapper for all app-level contexts
- `ErrorProvider`: Global error handling context

**Usage:**
```typescript
import { AppProviders } from '@gp/shared/app-providers';

// Wrap your app with all necessary providers
export function App() {
  return (
    <AppProviders>
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<AboutPage />} />
        </Routes>
      </Router>
    </AppProviders>
  );
}

// AppProviders includes:
// - LaunchDarkly provider
// - Session management
// - URL parameter handling
// - Error boundary
// - Theme provider
```

**Error Provider Usage:**
```typescript
import { ErrorProvider, useError } from '@gp/shared/app-providers';

// Use error context in components
function MyComponent() {
  const { showError, clearErrors } = useError();
  
  const handleApiCall = async () => {
    try {
      await apiCall();
    } catch (error) {
      showError('Failed to load data', error);
    }
  };
  
  return (
    <div>
      <button onClick={handleApiCall}>Load Data</button>
      <button onClick={clearErrors}>Clear Errors</button>
    </div>
  );
}

// Error constants
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;
```

### 2. LaunchDarkly (`@gp/shared/launchdarkly`)
Feature flag management and A/B testing integration.

**Components:**
- `LaunchDarklyProvider`: Provider for feature flags context
- `LaunchDarklyConsumer`: Consumer component for feature flags

**Utilities:**
- `getLDSingleKindContext`: Create LaunchDarkly context
- `identifyLDUser`: Identify user for feature flags

**Usage:**
```typescript
import { 
  LaunchDarklyProvider, 
  LaunchDarklyConsumer,
  useLaunchDarkly 
} from '@gp/shared/launchdarkly';

// Provider setup
export function App() {
  return (
    <LaunchDarklyProvider clientSideID={process.env.LD_CLIENT_ID}>
      <AppContent />
    </LaunchDarklyProvider>
  );
}

// Using feature flags with hook
function FeatureComponent() {
  const { flags, ldClient } = useLaunchDarkly();
  
  if (flags.newFeatureEnabled) {
    return <NewFeature />;
  }
  
  return <OldFeature />;
}

// Using feature flags with consumer
function AnotherComponent() {
  return (
    <LaunchDarklyConsumer>
      {({ flags, ldClient }) => (
        <div>
          {flags.showBanner && <PromoBanner />}
          {flags.enableNewUI ? <NewUI /> : <LegacyUI />}
        </div>
      )}
    </LaunchDarklyConsumer>
  );
}

// Context creation utility
import { getLDSingleKindContext } from '@gp/shared/launchdarkly';

const userContext = getLDSingleKindContext({
  kind: 'user',
  key: 'user-123',
  name: 'John Doe',
  email: '<EMAIL>',
  custom: {
    plan: 'premium',
    location: 'sydney'
  }
});
```

**Feature Flag Patterns:**
```typescript
// Feature flag naming conventions
export const FEATURE_FLAGS = {
  // Navigation features
  NEW_NAVBAR_ENABLED: 'new-navbar-enabled',
  MOBILE_MENU_V2: 'mobile-menu-v2',
  
  // Calculator features
  ADVANCED_CALCULATOR: 'advanced-calculator-enabled',
  CALCULATOR_REDESIGN: 'calculator-redesign',
  
  // Property features
  PROPERTY_COMPARISON: 'property-comparison-enabled',
  MAP_INTEGRATION: 'map-integration-enabled',
  
  // Business features
  BROKER_MATCHING: 'broker-matching-enabled',
  INSTANT_QUOTES: 'instant-quotes-enabled',
} as const;

// Feature flag hook with defaults
export function useFeatureFlag(flagKey: string, defaultValue: boolean = false): boolean {
  const { flags } = useLaunchDarkly();
  return flags[flagKey] ?? defaultValue;
}

// Feature wrapper component
export function FeatureFlag({ 
  flag, 
  children, 
  fallback = null 
}: {
  flag: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const isEnabled = useFeatureFlag(flag);
  
  return isEnabled ? <>{children}</> : <>{fallback}</>;
}
```

### 3. Server LaunchDarkly (`@gp/shared/server-launchdarkly`)
Server-side feature flag integration for Next.js applications.

**Usage:**
```typescript
import { getServerSideFlags } from '@gp/shared/server-launchdarkly';

// In Next.js pages or API routes
export async function getServerSideProps(context) {
  const flags = await getServerSideFlags({
    userKey: 'user-123',
    userAttributes: {
      email: '<EMAIL>',
      plan: 'premium'
    }
  });
  
  return {
    props: {
      flags,
      // other props
    }
  };
}

// In API routes
export default async function handler(req, res) {
  const flags = await getServerSideFlags({
    userKey: req.user?.id,
    userAttributes: req.user
  });
  
  if (flags.newApiEnabled) {
    return handleNewApi(req, res);
  }
  
  return handleLegacyApi(req, res);
}
```

### 4. Session Redirect (`@gp/shared/session-redirect`)
Session-based redirect management and URL handling.

**Components:**
- `SessionRedirect`: Component for handling session-based redirects

**Usage:**
```typescript
import { SessionRedirect } from '@gp/shared/session-redirect';

// Automatic redirect based on session state
export function ProtectedPage() {
  return (
    <SessionRedirect
      requireAuth={true}
      redirectTo="/login"
      loadingComponent={<LoadingSpinner />}
    >
      <ProtectedContent />
    </SessionRedirect>
  );
}

// Redirect after authentication
export function LoginPage() {
  const handleLoginSuccess = () => {
    // SessionRedirect will handle the redirect to the original destination
  };
  
  return (
    <SessionRedirect
      requireAuth={false}
      redirectTo="/dashboard"
      redirectWhenAuthenticated={true}
    >
      <LoginForm onSuccess={handleLoginSuccess} />
    </SessionRedirect>
  );
}
```

**Redirect Patterns:**
```typescript
// Common redirect scenarios
export const REDIRECT_SCENARIOS = {
  // Authentication redirects
  LOGIN_REQUIRED: {
    requireAuth: true,
    redirectTo: '/login',
    preserveQuery: true
  },
  
  // Post-auth redirects
  AUTHENTICATED_REDIRECT: {
    requireAuth: false,
    redirectTo: '/dashboard',
    redirectWhenAuthenticated: true
  },
  
  // Role-based redirects
  ADMIN_REQUIRED: {
    requireAuth: true,
    requireRole: 'admin',
    redirectTo: '/unauthorized'
  },
  
  // Feature flag redirects
  FEATURE_GATED: {
    requireFeatureFlag: 'new-feature-enabled',
    redirectTo: '/coming-soon'
  }
} as const;
```

## Provider Composition Patterns

### 1. App-Level Providers
```typescript
// Complete app provider setup
import { AppProviders } from '@gp/shared/app-providers';
import { LaunchDarklyProvider } from '@gp/shared/launchdarkly';
import { MantineProvider } from '@mantine/core';
import { aussieTheme } from '@gp/theme/aussie';

export function AppWithProviders({ children }: { children: React.ReactNode }) {
  return (
    <LaunchDarklyProvider clientSideID={process.env.NEXT_PUBLIC_LD_CLIENT_ID}>
      <MantineProvider theme={aussieTheme}>
        <AppProviders>
          {children}
        </AppProviders>
      </MantineProvider>
    </LaunchDarklyProvider>
  );
}
```

### 2. Feature-Specific Providers
```typescript
// Feature-level provider composition
export function FeatureProviders({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <FeatureProvider>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </FeatureProvider>
    </ErrorBoundary>
  );
}
```

### 3. Conditional Providers
```typescript
// Conditional provider based on feature flags
export function ConditionalProviders({ children }: { children: React.ReactNode }) {
  const { flags } = useLaunchDarkly();
  
  if (flags.newProviderEnabled) {
    return (
      <NewProvider>
        {children}
      </NewProvider>
    );
  }
  
  return (
    <LegacyProvider>
      {children}
    </LegacyProvider>
  );
}
```

## Error Handling Patterns

### 1. Global Error Boundary
```typescript
import { ErrorProvider } from '@gp/shared/app-providers';

// Global error handling
export class GlobalErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to error tracking service
    console.error('Global error:', error, errorInfo);
    
    // Track error analytics
    if (window.analytics) {
      window.analytics.track('Error Occurred', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback 
          error={this.state.error} 
          onRetry={() => this.setState({ hasError: false, error: null })}
        />
      );
    }
    
    return this.props.children;
  }
}
```

### 2. Error Context Usage
```typescript
// Using error context throughout the app
export function DataLoadingComponent() {
  const { showError, clearErrors } = useError();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const loadData = async () => {
    try {
      setLoading(true);
      clearErrors(); // Clear previous errors
      const result = await fetchData();
      setData(result);
    } catch (error) {
      showError('Failed to load data. Please try again.', error);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadData();
  }, []);
  
  if (loading) return <LoadingSpinner />;
  
  return (
    <div>
      {data ? <DataDisplay data={data} /> : <EmptyState />}
      <button onClick={loadData}>Refresh</button>
    </div>
  );
}
```

## Testing Patterns

### 1. Provider Testing
```typescript
// Test utilities for providers
export function renderWithProviders(
  ui: ReactElement,
  options: {
    initialFlags?: Record<string, boolean>;
    theme?: MantineTheme;
    user?: User;
  } = {}
) {
  const { initialFlags = {}, theme = aussieTheme, user } = options;
  
  function Wrapper({ children }: { children: ReactNode }) {
    return (
      <LaunchDarklyProvider 
        clientSideID="test-client-id"
        options={{ bootstrap: initialFlags }}
      >
        <MantineProvider theme={theme}>
          <AppProviders>
            {user && <UserProvider user={user} />}
            {children}
          </AppProviders>
        </MantineProvider>
      </LaunchDarklyProvider>
    );
  }
  
  return render(ui, { wrapper: Wrapper, ...options });
}
```

### 2. Feature Flag Testing
```typescript
// Testing with feature flags
describe('Feature Component', () => {
  it('shows new feature when flag is enabled', () => {
    renderWithProviders(<FeatureComponent />, {
      initialFlags: { 'new-feature-enabled': true }
    });
    
    expect(screen.getByText('New Feature')).toBeInTheDocument();
    expect(screen.queryByText('Old Feature')).not.toBeInTheDocument();
  });
  
  it('shows old feature when flag is disabled', () => {
    renderWithProviders(<FeatureComponent />, {
      initialFlags: { 'new-feature-enabled': false }
    });
    
    expect(screen.getByText('Old Feature')).toBeInTheDocument();
    expect(screen.queryByText('New Feature')).not.toBeInTheDocument();
  });
});
```

### 3. Error Handling Testing
```typescript
// Testing error scenarios
describe('Error Handling', () => {
  it('displays error when API call fails', async () => {
    const mockError = new Error('API Error');
    jest.spyOn(api, 'fetchData').mockRejectedValue(mockError);
    
    renderWithProviders(<DataComponent />);
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load data/i)).toBeInTheDocument();
    });
  });
  
  it('clears errors when retry is successful', async () => {
    const mockError = new Error('API Error');
    jest.spyOn(api, 'fetchData')
      .mockRejectedValueOnce(mockError)
      .mockResolvedValueOnce(mockData);
    
    renderWithProviders(<DataComponent />);
    
    // Wait for error
    await waitFor(() => {
      expect(screen.getByText(/failed to load data/i)).toBeInTheDocument();
    });
    
    // Click retry
    fireEvent.click(screen.getByText('Refresh'));
    
    // Wait for success
    await waitFor(() => {
      expect(screen.queryByText(/failed to load data/i)).not.toBeInTheDocument();
      expect(screen.getByText('Data loaded')).toBeInTheDocument();
    });
  });
});
```

## Best Practices

### 1. Provider Organization
- Keep providers focused on single concerns
- Compose providers at appropriate levels
- Use feature flags to conditionally enable providers
- Implement proper error boundaries around providers

### 2. Feature Flag Management
- Use descriptive flag names with consistent naming conventions
- Implement flag defaults for graceful degradation
- Clean up unused flags regularly
- Test both enabled and disabled states

### 3. Error Handling
- Implement global error boundaries for unhandled errors
- Provide user-friendly error messages
- Log errors for debugging and monitoring
- Implement retry mechanisms where appropriate

### 4. Performance Considerations
- Minimize provider re-renders with proper memoization
- Use lazy loading for heavy providers
- Implement proper cleanup in useEffect hooks
- Monitor provider performance impact