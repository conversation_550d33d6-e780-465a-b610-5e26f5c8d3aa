/**
 * Considering the package setup, this file must be placed in node projects rather than browser projects.
 */
import * as LaunchDarkly from '@launchdarkly/node-server-sdk';
import { v4 } from 'uuid';
import type { Brand } from '@lendi/lala-react';

let ldClient: LaunchDarkly.LDClient | undefined;

export async function initializeLDClient() {
  if (!process.env.LAUNCHDARKLY_SDK_KEY) {
    throw new Error('LAUNCHDARKLY_SDK_KEY is not defined');
  }
  try {
    ldClient = LaunchDarkly.init(process.env.LAUNCHDARKLY_SDK_KEY, {
      stream: false,
    });
    console.log('LD Client initializing...');
    await ldClient.waitForInitialization({ timeout: 20 });
    console.log('LD Client initialized successfully');

    return ldClient;
  } catch (error) {
    console.error('Error initializing LaunchDarkly client:', error);
    throw new Error('Failed to initialize LaunchDarkly client');
  }
}

export async function getLDClient() {
  if (!ldClient) {
    ldClient = await initializeLDClient();
  }
  return ldClient;
}

export async function getServerFlag(
  key: string,
  context: LaunchDarkly.LDContext,
  defaultValue: LaunchDarkly.LDFlagValue
) {
  return (await getLDClient()).variation(key, context, defaultValue);
}

export async function getLDInitFlags(context: LaunchDarkly.LDContext) {
  return (await getLDClient()).allFlagsState(context);
}

export const getLDSingleKindContext = (
  brand: Brand,
  targetId?: string,
  customerId?: string
): LaunchDarkly.LDContext => {
  const _targetId = targetId || v4();
  if (!customerId) {
    return {
      anonymous: true,
      key: _targetId,
      kind: 'user',
      targetId: _targetId,
      isTeamView: false,
      brand,
    };
  }
  return {
    key: customerId,
    kind: 'user',
    targetId: _targetId,
    isTeamView: false,
    brand,
  };
};
