import { nativeEnum, z } from 'zod';

import { CONVEYANCING_SERVICE_TYPE, FINANCING_STATUS, USER_SITUATION } from './constants';

const { object } = z;

export const customerSituationSchema = object({
  conveyancingService: nativeEnum(CONVEYANCING_SERVICE_TYPE),
  userSituation: nativeEnum(USER_SITUATION).optional().nullable(),
  location: z.string(),
  financingStatus: nativeEnum(FINANCING_STATUS).optional().nullable(),
  enquiryId: z.string().optional().nullable(),
});

export type CustomerSituation = z.infer<typeof customerSituationSchema>;

export const caseOwnerSchema = object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
});

export type CaseOwner = z.infer<typeof caseOwnerSchema>;

export const enquirySchema = object({
  id: z.string().optional().nullable(),
  customerId: z.string().uuid(),
  provider: z.literal('SETTLE_EASY'),
  sourceType: z.literal('NO_SOURCE_OPPORTUNITY'),
});

export type Enquiry = z.infer<typeof enquirySchema>;

export const engagementSchema = object({
  id: z.string().optional().nullable(),
  customerId: z.string().uuid(),
  transactionType: z.string(),
  provider: z.literal('SETTLE_EASY'),
  sourceType: z.literal('NO_SOURCE_OPPORTUNITY'),
  property: z
    .object({
      address: z.object({
        formatted: z.string().optional().nullable(),
        state: z.string().optional().nullable(),
        postcode: z.string().optional().nullable(),
      }),
    })
    .optional()
    .nullable(),
  broker: z.object({
    id: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email(),
  }),
  customer: z.object({
    customerId: z.string().uuid(),
    brand: z.literal('AUSSIE'),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email(),
    phone: z.string(),
    state: z.string(),
  }),
  additionalNotes: z.string().optional().nullable(),
});

export type Engagement = z.infer<typeof engagementSchema>;
