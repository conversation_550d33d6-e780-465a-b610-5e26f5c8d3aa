export enum CONVEYANCING_SERVICE_TYPE {
  BUYING = 'BUYING',
  SELLING = 'SELLING',
  SELLING_AND_BUYING = 'SELLING_AND_BUYING',
  PROPERTY_TRANSFER = 'PROPERTY_TRANSFER',
}

export const conveyancingServiceTypeOptions = [
  {
    value: CONVEYANCING_SERVICE_TYPE.BUYING,
    label: 'Buying only',
    subtitle: 'Legal assistance when purchasing a property',
  },
  {
    value: CONVEYANCING_SERVICE_TYPE.SELLING,
    label: 'Selling only',
    subtitle: 'Legal assistance when selling a property',
  },
  {
    value: CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING,
    label: 'Selling and buying',
    subtitle: 'Combined legal assistance for both transactions',
  },
  {
    value: CONVEYANCING_SERVICE_TYPE.PROPERTY_TRANSFER,
    label: 'Property transfer',
    subtitle: 'Transfer property between family members or entities',
  },
] as const;

export enum USER_SITUATION {
  STILL_SEARCHING = 'STILL_SEARCHING',
  FOUND_PROPERTY = 'FOUND_PROPERTY',
}

export const userSituationOptions = [
  {
    value: USER_SITUATION.STILL_SEARCHING,
    label: 'I am still searching for a property',
    subtitle: "You haven't found the right property yet",
  },
  {
    value: USER_SITUATION.FOUND_PROPERTY,
    label: 'I have found a property',
    subtitle: "You've found a property and are ready to proceed",
  },
] as const;

export enum LOCATION {
  SEARCH_LOCATION = 'SEARCH_LOCATION',
  PROPERTY_ADDRESS = 'PROPERTY_ADDRESS',
}

export enum FINANCING_STATUS {
  PRE_APPROVED = 'PRE_APPROVED',
  NOT_PRE_APPROVED = 'NOT_PRE_APPROVED',
}

export const financingStatusOptions = [
  {
    value: FINANCING_STATUS.PRE_APPROVED,
    label: 'I have a pre-approval',
    subtitle: 'You have financing pre-approval from a lender',
  },
  {
    value: FINANCING_STATUS.NOT_PRE_APPROVED,
    label: 'I need to get pre-approved',
    subtitle: 'You need assistance with financing',
  },
] as const;

export const fixedProfessionalFees = [
  { state: 'ACT', price: 1850 },
  { state: 'NSW', price: 1850 },
  { state: 'NT', price: 1200 },
  { state: 'QLD', price: 1400 },
  { state: 'SA', price: 1400 },
  { state: 'TAS', price: 1300 },
  { state: 'VIC', price: 1700 },
  { state: 'WA', price: 1200 },
];

export const disbursementFees = {
  buyingOnly: {
    ACT: 550,
    NSW: 550,
    NT: 300,
    QLD: 850,
    SA: 250,
    TAS: 600,
    VIC: 550,
    WA: 400,
  },
  sellingOnly: {
    ACT: 550,
    NSW: 650,
    NT: 700,
    QLD: 150,
    SA: 250,
    TAS: 250,
    VIC: 650,
    WA: 250,
  },
  transferOnly: {
    ACT: 100,
    NSW: 200,
    NT: 200,
    QLD: 250,
    SA: 100,
    TAS: 150,
    VIC: 200,
    WA: 150,
  },
};

export const transferCost = 1000;

export const benefitsList = [
  {
    title: 'Expert legal team',
    description: 'Experienced professionals dedicated to your property transaction',
  },
  {
    title: 'Transparent pricing',
    description: 'No hidden fees, with clear communication throughout',
  },
  {
    title: 'Streamlined process',
    description: 'Efficient handling of all legal aspects of your property transaction',
  },
  {
    title: 'Online tracking',
    description: 'Monitor your conveyancing progress through our secure online portal',
  },
] as const;

export const DEFAULT_BROKER = {
  id: '053c2b38-7f51-436c-9d41-3fa4a85109cf',
  firstName: 'Lendi Group',
  lastName: 'Lendi Group',
  email: '<EMAIL>',
};

export enum RESULTS_CTA_TYPE {
  BUYERS_AGENT = 'BUYERS_AGENT',
  BUYERS_AGENT_NEGOTIATION = 'BUYERS_AGENT_NEGOTIATION',
  GET_PRE_APPROVED = 'GET_PRE_APPROVED',
  SELLER_ASSIST = 'SELLER_ASSIST',
}

export const resultsCTAs = [
  {
    type: RESULTS_CTA_TYPE.BUYERS_AGENT,
    title: 'Still searching for the right property?',
    subtitle:
      "Work with an Aussie buyer's agent who can help you find, evaluate, and negotiate your next home",
    cta: "Connect with a buyer's agent",
    link: '/buyers-agent/',
  },
  {
    type: RESULTS_CTA_TYPE.GET_PRE_APPROVED,
    title: 'Need help with your home loan?',
    subtitle:
      'Our mortgage brokers compare lenders and find a loan that suits your needs — at no cost to you.^',
    cta: 'Speak to a mortgage broker',
    link: '/home-loans/basics/new-home/',
  },
  {
    type: RESULTS_CTA_TYPE.BUYERS_AGENT_NEGOTIATION,
    title: 'Want help negotiating your purchase?',
    subtitle: "Buyer's agents know how to secure the right property at the right price",
    cta: 'Get negotiation support',
    link: '/buyers-agent/',
  },
] as const; //copy for Seller Assist not yet available
