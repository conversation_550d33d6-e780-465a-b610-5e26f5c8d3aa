import { Customer } from '@gp/data-access/customer';
import { get, getEnvVar, post, put } from '@gp/util/data-service';

import { CONVEYANCING_SERVICE_TYPE, USER_SITUATION } from './constants';
import {
  CaseOwner,
  CustomerSituation,
  Engagement,
  engagementSchema,
  Enquiry,
  enquirySchema,
} from './schema';

export const getCaseOwner = async (id: string, token?: string) => {
  try {
    const response = await get<CaseOwner>(
      `${getEnvVar('GP_API_BASE_URL')}/v1/case-owner/CUSTOMER/${id}`,
      token
    );
    return response.parsedBody;
  } catch (error) {
    console.error(error);
    return undefined;
  }
};

export const submitEnquiry = async (id: string, token?: string) => {
  const enquiry = enquirySchema.safeParse({
    customerId: id,
    provider: 'SETTLE_EASY',
    sourceType: 'NO_SOURCE_OPPORTUNITY',
  });
  if (!enquiry.success) {
    throw new Error(enquiry.error.toString());
  }
  const { data } = enquiry;
  try {
    const response = await post<Omit<Enquiry, 'id'>, Enquiry>(
      `${getEnvVar('GP_API_BASE_URL')}/v1/conveyancing/internal`,
      data,
      token
    );
    return response.parsedBody.id;
  } catch (error) {
    console.error(error);
    return undefined;
  }
};

export const submitEngagementReferral = async (
  customer: Partial<Customer>,
  customerSituation: Partial<CustomerSituation>,
  broker?: CaseOwner,
  token?: string
) => {
  const enquiryId = customerSituation.enquiryId;
  const isSellingAndBuying =
    customerSituation.conveyancingService === CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING;
  const addressRequired =
    customerSituation?.conveyancingService === CONVEYANCING_SERVICE_TYPE.SELLING ||
    customerSituation?.conveyancingService === CONVEYANCING_SERVICE_TYPE.PROPERTY_TRANSFER ||
    customerSituation.userSituation === USER_SITUATION.FOUND_PROPERTY;
  const splitLocation = customerSituation.location?.split(' ') ?? [];
  const postcode = splitLocation.pop();
  const state = splitLocation.pop();
  const formattedAddress = splitLocation.join(' ');

  const getTransactionType = () => {
    switch (customerSituation.conveyancingService) {
      case CONVEYANCING_SERVICE_TYPE.SELLING_AND_BUYING:
      case CONVEYANCING_SERVICE_TYPE.SELLING:
        return 'SALE';
      case CONVEYANCING_SERVICE_TYPE.PROPERTY_TRANSFER:
        return 'TRANSFER';
      case CONVEYANCING_SERVICE_TYPE.BUYING:
      default:
        return 'PURCHASE';
    }
  };

  const engagement = engagementSchema.safeParse({
    customerId: customer.id,
    transactionType: getTransactionType(),
    provider: 'SETTLE_EASY',
    sourceType: 'NO_SOURCE_OPPORTUNITY',
    property: {
      address: {
        formatted: addressRequired ? formattedAddress : null,
        state: addressRequired ? state : null,
        postcode: addressRequired ? postcode : null,
      },
    },
    broker: {
      id: broker?.id,
      firstName: broker?.firstName,
      lastName: broker?.lastName,
      email: broker?.email,
    },
    customer: {
      customerId: customer.id,
      brand: 'AUSSIE',
      firstName: customer.firstName || 'unknown',
      lastName: customer.lastName || 'unknown',
      email: customer.email || 'unknown',
      phone: customer.mobileNumber || 'unknown',
      state: state || 'unknown',
    },
    additionalNotes: isSellingAndBuying
      ? 'Customer is looking for a second conveyancing service for PURCHASE'
      : null,
  });
  if (!engagement.success) {
    throw new Error(engagement.error.toString());
  }
  const { data } = engagement;

  try {
    const response = await put<Omit<Engagement, 'id'>, Engagement>(
      `${getEnvVar('GP_API_BASE_URL')}/v1/conveyancing/internal/${enquiryId}/refer`,
      data,
      token
    );
    return response.parsedBody;
  } catch (error) {
    console.error(error);
    return undefined;
  }
};
