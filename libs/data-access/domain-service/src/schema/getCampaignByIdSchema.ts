export const getCampaignByIdSchema = `
query Campaign($getCampaignId: ID!) {
  getCampaign(id: $getCampaignId) {
    Campaign {
      id
      createdAt
      updatedAt
      campaignId
      propertyId
      locationId
      listingLoopId
      slug
      suburbSlug
      address
      suburbText
      state
      postcode
      locationPoint
      propertyType
      propertySubType
      campaignType
      campaignMarketType
      campaignStatus
      bedrooms
      bathrooms
      carSpaces
      landArea
      priceGuideMin
      priceGuideMax
      firstListedDate
      auctionDateTime
      primaryAddress
      campaignDescription
      campaignSource
      priceDescription
      realEstateAgencies
      realEstateAgents
      inspectionDatetimes
      informationStatementUrl
      builtYear
      heroPhotoUrl
      imageUrl
      photoUrls
      floorPlanUrls
      floorArea
      latitude
      longitude
      campaignAttributes
    }
  }
}
`;
