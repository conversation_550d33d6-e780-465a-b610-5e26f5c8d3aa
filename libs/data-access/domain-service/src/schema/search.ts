export const campaignSearchSchema = `
query Campaigns($suburbSlugIn: [String], $propertyTypeIn: [String], $campaignType: CampaigntypeEnum, $bedroomsLte: Float, $bedroomsGte: Float, $bathroomsLte: Float, $bathroomsGte: Float, $carSpacesLte: Float, $carSpacesGte: Float, $landAreaLte: Float, $landAreaGte: Float, $priceGuideMinGte: Float, $priceGuideMaxLte: Float, $campaignSourceIn: [String], $campaignMarketTypeIn: [CampaignmarkettypeEnum], $campaignStatus: CampaignstatusEnum, $limit: Int, $offset: Int,) {
  allCampaigns(suburbSlug_in_order: $suburbSlugIn, propertyType_in: $propertyTypeIn, campaignType: $campaignType, bedrooms_lte: $bedroomsLte, bedrooms_gte: $bedroomsGte, bathrooms_lte: $bathroomsLte, bathrooms_gte: $bathroomsGte, carSpaces_lte: $carSpacesLte, carSpaces_gte: $carSpacesGte, landArea_lte: $landAreaLte, landArea_gte: $landAreaGte, priceGuideMin_gte: $priceGuideMinGte, priceGuideMax_lte: $priceGuideMaxLte, campaignSource_in: $campaignSourceIn, campaignMarketType_in: $campaignMarketTypeIn, campaignStatus: $campaignStatus, LIMIT: $limit, OFFSET: $offset) {
  pagination {
    totalCount
    totalPages
  }  
  Campaigns {
      id
      createdAt
      updatedAt
      campaignId
      propertyId
      locationId
      listingLoopId
      suburbSlug
      address
      suburbText
      state
      postcode
      slug
      locationPoint
      propertyType
      propertySubType
      campaignType
      campaignMarketType
      campaignStatus
      bedrooms
      bathrooms
      carSpaces
      landArea
      priceGuideMin
      priceGuideMax
      firstListedDate
      auctionDateTime
      primaryAddress
      campaignDescription
      campaignSource
      priceDescription
      realEstateAgencies
      realEstateAgents
      inspectionDatetimes
      informationStatementUrl
      builtYear
      heroPhotoUrl
      imageUrl
      photoUrls
      floorArea
      latitude
      longitude
      campaignAttributes
      floorPlanUrls
    }
  }
}
`;
