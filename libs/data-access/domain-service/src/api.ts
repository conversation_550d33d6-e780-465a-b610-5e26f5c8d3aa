import { getEnvVar, post } from '@gp/util/data-service';

import { campaignSearchSchema, getCampaignByIdSchema } from './schema';

export const getDomainServiceAPIs = (token?: string) => {
  // for easy switch when running locally
  const baseURL = `${
    getEnvVar('GP_API_BASE_URL') || process.env['API_BASE_URL']
  }/v1/property-domain-service/graphql`;
  return {
    getPublicPropertiesForSale: async (
      areas: string[],
      query: {
        exclusiveListingOnly?: boolean;
        types?: (
          | 'HOUSE'
          | 'UNIT'
          | 'TOWNHOUSE'
          | 'APARTMENT'
          | 'VILLA'
          | 'RETIREMENT_LIVING'
          | 'LAND'
          | 'ACREAGE'
          | 'RURAL'
          | 'BLOCK_OF_UNITS'
          | 'OTHER'
        )[];
        minPrice?: number;
        maxPrice?: number;
        minBedrooms?: number;
        maxBedrooms?: number;
        minBathrooms?: number;
        maxBathrooms?: number;
        minCarSpaces?: number;
        maxCarSpaces?: number;
        minLandSize?: number;
        maxLandSize?: number;
      },
      offset?: number,
      limit?: number
    ) => {
      try {
        let propertyType: string[] | undefined = undefined;
        if (query.types && query.types.length > 0) {
          const tempPropertyTypes: string[] = [];
          query.types.forEach((type) => {
            if (type === 'HOUSE')
              tempPropertyTypes.push('HOUSE', 'DUPLEX_SEMI_DETACHED', 'TERRACE');
            if (type === 'UNIT') tempPropertyTypes.push('UNIT', 'FLAT', 'FLATS');
            if (type === 'TOWNHOUSE') tempPropertyTypes.push('TOWNHOUSE');
            if (type === 'APARTMENT')
              tempPropertyTypes.push('APARTMENT', 'STUDIO', 'SERVICED_APARTMENT');
            if (type === 'VILLA') tempPropertyTypes.push('VILLA');
            if (type === 'RETIREMENT_LIVING') tempPropertyTypes.push('RETIREMENT');
            if (type === 'LAND') tempPropertyTypes.push('LAND');
            if (type === 'ACREAGE') tempPropertyTypes.push('ACREAGE_SEMI_RURAL');
            if (type === 'RURAL') tempPropertyTypes.push('FARM');
            if (type === 'BLOCK_OF_UNITS') tempPropertyTypes.push('BLOCK_OF_UNITS');
            if (type === 'OTHER')
              tempPropertyTypes.push(
                'OTHER',
                'BUSINESS',
                'COMMERCIAL',
                'WAREHOUSE',
                'COMMUNITY',
                'ALPINE',
                'STORAGE_UNIT'
              );
          });
          propertyType = tempPropertyTypes;
        }
        const { parsedBody: searchResults } = await post<
          {
            operationName: string;
            query: string;
            variables: {
              bathroomsGte?: number;
              bathroomsLte?: number;
              bedroomsGte?: number;
              bedroomsLte?: number;
              campaignMarketTypeIn?: string[];
              campaignStatus?: 'CURRENT' | 'NOT_CURRENT';
              campaignType?: string;
              carSpacesGte?: number;
              carSpacesLte?: number;
              landAreaGte?: number;
              landAreaLte?: number;
              priceGuideMaxLte?: number;
              priceGuideMinGte?: number;
              propertyTypeIn?: string[];
              suburbSlugIn?: string[];
              limit: number;
              offset?: number;
            };
          },
          {
            data: {
              allCampaigns: {
                pagination: { totalCount: number; totalPages: number };
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                Campaigns: any[];
              };
            };
          }
        >(baseURL, {
          operationName: 'Campaigns',
          query: campaignSearchSchema,
          variables: {
            bathroomsGte: query.minBathrooms,
            bathroomsLte: query.maxBathrooms,
            bedroomsGte: query.minBedrooms,
            bedroomsLte: query.maxBedrooms,
            campaignMarketTypeIn: ['ON_MARKET'],
            campaignStatus: 'CURRENT',
            campaignType: 'SALE',
            carSpacesGte: query.minCarSpaces,
            carSpacesLte: query.maxCarSpaces,
            landAreaGte: query.minLandSize,
            landAreaLte: query.maxLandSize,
            priceGuideMaxLte: query.maxPrice,
            priceGuideMinGte: query.minPrice,
            propertyTypeIn: propertyType,
            suburbSlugIn: areas,
            limit: limit || 25,
            offset,
          },
        });
        return searchResults;
      } catch (e) {
        console.error(e);
        return {
          data: { allCampaigns: { pagination: { totalCount: 0, totalPages: 0 }, Campaigns: [] } },
        };
      }
    },
    getPublicPropertyById: async (id: string) => {
      try {
        const { parsedBody: searchResults } = await post<
          {
            operationName: string;
            query: string;
            variables: {
              getCampaignId: string;
            };
          },
          {
            data: {
              getCampaign: {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                Campaign: any;
              };
            };
          }
        >(baseURL, {
          operationName: 'Campaign',
          query: getCampaignByIdSchema,
          variables: {
            getCampaignId: id,
          },
        });
        return searchResults;
      } catch (e) {
        console.error(e);
        return {
          data: { getCampaign: { Campaign: {} } },
        };
      }
    },
  };
};
