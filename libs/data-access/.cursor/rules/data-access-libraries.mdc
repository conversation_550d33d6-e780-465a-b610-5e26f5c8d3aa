# Data Access Libraries Guidelines

## Overview
Data access libraries provide typed APIs for external services, databases, and third-party integrations. They handle data fetching, caching, schema validation, and API communication patterns.

## Import Pattern
```typescript
import { ServiceApi, ServiceSchema } from '@gp/data-access/service-name';
```

## Available Data Access Libraries

### 1. Agents Service (`@gp/data-access/agents-service`)
Integration with agents service API.

**Exports:**
- API functions for agent operations
- Type definitions and schemas
- Constants and configuration

**Usage:**
```typescript
import { getAgents, AgentSchema } from '@gp/data-access/agents-service';

const agents = await getAgents({ location: 'Sydney' });
const validatedAgent = AgentSchema.parse(agentData);
```

### 2. Broker (`@gp/data-access/broker`)
Broker data access and Contentful CMS integration.

**Exports:**
- Broker API functions
- Contentful queries
- Broker schemas and types
- Constants

**Usage:**
```typescript
import { 
  getBrokers, 
  getBrokerBySlug, 
  <PERSON><PERSON>rSchema,
  BROKER_CONTENT_TYPE 
} from '@gp/data-access/broker';

// Fetch brokers from API
const brokers = await getBrokers({ state: 'NSW' });

// Get broker from Contentful
const broker = await getBrokerBySlug('john-smith');

// Validate data
const validBroker = BrokerSchema.parse(brokerData);
```

### 3. Buyers Agent Engagement (`@gp/data-access/buyers-agent-engagement`)
Buyers agent service integration.

**Usage:**
```typescript
import { 
  submitBuyersAgentEngagement,
  BuyersAgentEngagementSchema 
} from '@gp/data-access/buyers-agent-engagement';

const result = await submitBuyersAgentEngagement({
  customerDetails: { ... },
  propertyRequirements: { ... }
});
```

### 4. Conveyancing (`@gp/data-access/conveyancing`)
Conveyancing service integration with customer data.

**Usage:**
```typescript
import { 
  getConveyancingQuote,
  submitConveyancingRequest,
  ConveyancingQuoteSchema 
} from '@gp/data-access/conveyancing';

const quote = await getConveyancingQuote({
  propertyValue: 800000,
  propertyType: 'house',
  state: 'NSW'
});
```

### 5. CoreLogic (`@gp/data-access/corelogic`)
CoreLogic property data integration with authentication.

**Exports:**
- Property valuation APIs
- Property search functions
- Authentication utilities
- CoreLogic schemas

**Usage:**
```typescript
import { 
  getCorelogicProperty,
  searchCorelogicProperties,
  authenticateCorelogic,
  CorelogicPropertySchema 
} from '@gp/data-access/corelogic';

// Authenticate first
await authenticateCorelogic();

// Get property data
const property = await getCorelogicProperty('12345');
const searchResults = await searchCorelogicProperties({
  suburb: 'Sydney',
  propertyType: 'house'
});
```

### 6. Customer (`@gp/data-access/customer`)
Customer data management and API integration.

**Usage:**
```typescript
import { 
  getCustomer,
  updateCustomer,
  CustomerSchema 
} from '@gp/data-access/customer';

const customer = await getCustomer(customerId);
const updatedCustomer = await updateCustomer(customerId, updateData);
```

### 7. Customer Funnel (`@gp/data-access/customer-funnel`)
Funnel-specific entities and schemas for customer journeys.

**Exports:**
- Buying situation entity
- Current loan entity
- Personal details entity
- Property entities (new purchase, refinance)
- Preference entities
- Validation schemas

**Usage:**
```typescript
import { 
  BuyingSituationEntity,
  PersonalDetailsEntity,
  NewPurchasePropertyEntity,
  validateFunnelData 
} from '@gp/data-access/customer-funnel';

// Create funnel entities
const buyingSituation = new BuyingSituationEntity({
  isFirstHomeBuyer: true,
  hasDeposit: true,
  depositAmount: 100000
});

const personalDetails = new PersonalDetailsEntity({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>'
});

// Validate funnel data
const isValid = validateFunnelData(funnelData);
```

### 8. Domain Service (`@gp/data-access/domain-service`)
Domain.com.au integration for campaigns and property search.

**Usage:**
```typescript
import { 
  getCampaignById,
  searchDomainProperties,
  DomainCampaignSchema 
} from '@gp/data-access/domain-service';

const campaign = await getCampaignById('campaign-123');
const properties = await searchDomainProperties({
  suburb: 'Melbourne',
  minPrice: 500000,
  maxPrice: 1000000
});
```

### 9. Home Loan Health Check (`@gp/data-access/home-loan-health-check`)
Health check service for existing home loans.

**Usage:**
```typescript
import { 
  submitHealthCheck,
  HealthCheckSchema,
  LoanDetailsEntity 
} from '@gp/data-access/home-loan-health-check';

const loanDetails = new LoanDetailsEntity({
  currentRate: 3.5,
  loanAmount: 500000,
  remainingTerm: 25
});

const healthCheck = await submitHealthCheck(loanDetails);
```

### 10. Insights Articles (`@gp/data-access/insights-articles`)
CMS integration for articles and insights content.

**Usage:**
```typescript
import { 
  getArticles,
  getArticleBySlug,
  ArticleSchema 
} from '@gp/data-access/insights-articles';

const articles = await getArticles({
  category: 'home-loans',
  limit: 10
});

const article = await getArticleBySlug('first-home-buyer-guide');
```

### 11. Property Hub (`@gp/data-access/property-hub`)
Comprehensive property data access with CoreLogic integration.

**Usage:**
```typescript
import { 
  getProperty,
  searchProperties,
  getPropertyValuation,
  PropertySchema 
} from '@gp/data-access/property-hub';

const property = await getProperty('property-123');
const searchResults = await searchProperties({
  suburb: 'Brisbane',
  bedrooms: 3,
  maxPrice: 800000
});

const valuation = await getPropertyValuation('property-123');
```

### 12. Store (`@gp/data-access/store`)
Store location and information data access.

**Usage:**
```typescript
import { 
  getStores,
  getStoreBySlug,
  findNearbyStores,
  StoreSchema 
} from '@gp/data-access/store';

const stores = await getStores({ state: 'VIC' });
const store = await getStoreBySlug('melbourne-cbd');
const nearbyStores = await findNearbyStores(-37.8136, 144.9631, 10);
```

## Data Access Patterns

### 1. API Client Structure
```typescript
// Base API client setup
export class ServiceApiClient {
  private baseUrl: string;
  private apiKey: string;
  
  constructor() {
    this.baseUrl = process.env.SERVICE_API_URL;
    this.apiKey = process.env.SERVICE_API_KEY;
  }
  
  private async makeRequest<T>(endpoint: string, options?: RequestOptions): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options?.headers
      },
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async get<T>(endpoint: string): Promise<T> {
    return this.makeRequest<T>(endpoint, { method: 'GET' });
  }
  
  async post<T>(endpoint: string, data: unknown): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
}
```

### 2. Schema Validation
```typescript
import { z } from 'zod';

// Define schemas for data validation
export const PropertySchema = z.object({
  id: z.string(),
  address: z.string(),
  suburb: z.string(),
  state: z.string(),
  postcode: z.string(),
  bedrooms: z.number().min(0),
  bathrooms: z.number().min(0),
  carSpaces: z.number().min(0),
  landSize: z.number().optional(),
  price: z.number().positive(),
  propertyType: z.enum(['house', 'unit', 'townhouse', 'land']),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number()
  }).optional()
});

export type Property = z.infer<typeof PropertySchema>;

// Validation function
export function validateProperty(data: unknown): Property {
  return PropertySchema.parse(data);
}
```

### 3. Error Handling
```typescript
export class DataAccessError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'DataAccessError';
  }
}

export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  errorMessage: string
): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    if (error instanceof Response) {
      throw new DataAccessError(
        `${errorMessage}: ${error.statusText}`,
        error.status,
        error as any
      );
    }
    throw new DataAccessError(errorMessage, undefined, error as Error);
  }
}
```

### 4. Caching Strategy
```typescript
import { LRUCache } from 'lru-cache';

const cache = new LRUCache<string, any>({
  max: 500,
  ttl: 1000 * 60 * 5 // 5 minutes
});

export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>
): Promise<T> {
  const cached = cache.get(key);
  if (cached) {
    return cached as T;
  }
  
  const data = await fetchFn();
  cache.set(key, data);
  return data;
}
```

## Entity Patterns

### 1. Entity Classes
```typescript
export class CustomerEntity {
  constructor(
    public readonly id: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly email: string,
    public readonly phone?: string
  ) {}
  
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
  
  toJSON() {
    return {
      id: this.id,
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      phone: this.phone,
      fullName: this.fullName
    };
  }
  
  static fromApiResponse(data: any): CustomerEntity {
    return new CustomerEntity(
      data.id,
      data.firstName,
      data.lastName,
      data.email,
      data.phone
    );
  }
}
```

### 2. Repository Pattern
```typescript
export interface PropertyRepository {
  findById(id: string): Promise<Property | null>;
  findBySuburb(suburb: string): Promise<Property[]>;
  search(criteria: SearchCriteria): Promise<Property[]>;
  create(property: CreatePropertyData): Promise<Property>;
  update(id: string, updates: UpdatePropertyData): Promise<Property>;
  delete(id: string): Promise<void>;
}

export class ApiPropertyRepository implements PropertyRepository {
  constructor(private apiClient: PropertyApiClient) {}
  
  async findById(id: string): Promise<Property | null> {
    try {
      const data = await this.apiClient.get(`/properties/${id}`);
      return PropertySchema.parse(data);
    } catch (error) {
      if (error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  }
  
  async search(criteria: SearchCriteria): Promise<Property[]> {
    const data = await this.apiClient.get('/properties/search', criteria);
    return data.map(item => PropertySchema.parse(item));
  }
}
```

## Best Practices

### 1. Type Safety
```typescript
// Always use TypeScript interfaces and types
export interface BrokerSearchParams {
  state?: string;
  suburb?: string;
  specialties?: string[];
  rating?: number;
  limit?: number;
  offset?: number;
}

export interface BrokerSearchResponse {
  brokers: Broker[];
  total: number;
  hasMore: boolean;
}
```

### 2. Environment Configuration
```typescript
// Use environment variables for configuration
export const CONFIG = {
  CORELOGIC_API_URL: process.env.CORELOGIC_API_URL || 'https://api.corelogic.com.au',
  CORELOGIC_API_KEY: process.env.CORELOGIC_API_KEY,
  CONTENTFUL_SPACE_ID: process.env.CONTENTFUL_SPACE_ID,
  CONTENTFUL_ACCESS_TOKEN: process.env.CONTENTFUL_ACCESS_TOKEN,
  CACHE_TTL: parseInt(process.env.CACHE_TTL || '300'), // 5 minutes
} as const;
```

### 3. Retry Logic
```typescript
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        throw lastError;
      }
      
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
  
  throw lastError!;
}
```

### 4. Request/Response Logging
```typescript
export function logApiCall(
  method: string,
  url: string,
  duration: number,
  statusCode?: number
) {
  console.log(`API ${method} ${url} - ${duration}ms - ${statusCode || 'ERROR'}`);
}
```

## Testing Patterns

### 1. Mock API Responses
```typescript
// Mock data for testing
export const mockBroker: Broker = {
  id: 'broker-123',
  firstName: 'John',
  lastName: 'Smith',
  email: '<EMAIL>',
  specialties: ['first-home-buyer', 'investment'],
  rating: 4.8,
  reviewCount: 127
};

// Mock API client
export class MockBrokerApiClient {
  async getBrokers(): Promise<Broker[]> {
    return [mockBroker];
  }
  
  async getBrokerById(id: string): Promise<Broker | null> {
    return id === 'broker-123' ? mockBroker : null;
  }
}
```

### 2. Schema Testing
```typescript
describe('BrokerSchema', () => {
  it('should validate valid broker data', () => {
    const validData = {
      id: 'broker-123',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      specialties: ['first-home-buyer']
    };
    
    expect(() => BrokerSchema.parse(validData)).not.toThrow();
  });
  
  it('should reject invalid email', () => {
    const invalidData = {
      id: 'broker-123',
      firstName: 'John',
      lastName: 'Smith',
      email: 'invalid-email',
      specialties: ['first-home-buyer']
    };
    
    expect(() => BrokerSchema.parse(invalidData)).toThrow();
  });
});
```

## Integration Guidelines

### 1. With API Libraries
```typescript
// Data access libraries provide data to API libraries
import { getBrokers } from '@gp/data-access/broker';

@Injectable()
export class BrokersService {
  async findBrokers(query: BrokerQuery) {
    return getBrokers(query); // Use data access layer
  }
}
```

### 2. With Feature Libraries
```typescript
// Feature libraries consume data access libraries
import { getProperties } from '@gp/data-access/property-hub';

export function PropertySearch() {
  const [properties, setProperties] = useState([]);
  
  useEffect(() => {
    const loadProperties = async () => {
      const results = await getProperties(searchCriteria);
      setProperties(results);
    };
    
    loadProperties();
  }, [searchCriteria]);
  
  return <PropertyList properties={properties} />;
}
```

### 3. With Utility Libraries
```typescript
// Use utility libraries for common operations
import { formatCurrency } from '@gp/util/intl';
import { logger } from '@gp/util/logging';

export async function getPropertyWithFormatting(id: string) {
  logger.info('Fetching property', { id });
  
  const property = await getProperty(id);
  
  return {
    ...property,
    formattedPrice: formatCurrency(property.price)
  };
}
```