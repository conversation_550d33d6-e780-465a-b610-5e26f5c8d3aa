'use client';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';

import { AnchorWithTracking, Image } from '@gp/ui/components';

const background = {
  borderRadius: '6px',
  backgroundImage: "url('/growth-product-assets/competition/door-to-more-bg.png')",
  backgroundPosition: 'bottom left',
  width: '100%',
};

export enum CompetitionVariant {
  BUYERS_AGENT = 'BUYERS_AGENT',
  MORTGAGE_BROKER = 'MORTGAGE_BROKER',
}

export interface CompetitionBannerProps {
  variant?: CompetitionVariant;
}

export default function CompetitionBanner({
  variant = CompetitionVariant.MORTGAGE_BROKER,
}: CompetitionBannerProps) {
  const isBuyersAgent = variant === CompetitionVariant.BUYERS_AGENT;
  return (
    <Box mb="xl">
      <AnchorWithTracking
        label="test"
        href={isBuyersAgent ? '/buyers-agent/engagement/' : '/book-appointment/'}
        underline="never"
        useNextLink
      >
        <Group align="center" visibleFrom="sm" style={background}>
          <Flex gap="sm" align="center" direction={{ xxs: 'column', md: 'row' }}>
            <Stack px="lg" gap="8" flex="2" pr="xl">
              <Title c="#FFF" size="h2" order={3} component="p">
                Ready to win?
              </Title>
              <Title fw="400" c="#FFF" size="h3" component="p">
                Attend an appointment with an Aussie {isBuyersAgent ? 'Property Expert' : 'Broker'}{' '}
                for your chance to win.^
              </Title>
            </Stack>
            <Image
              src="competition/door-to-more-lockup.png"
              w={{ sm: '100%', md: '300' }}
              h="240"
              alt="Door to More banner"
              flex="1"
            />
          </Flex>
        </Group>
        <Stack gap="md" hiddenFrom="sm">
          <Group style={background} justify="center">
            <Stack px="lg" gap="8" pr="xl">
              <Image
                src="competition/door-to-more-lockup.png"
                w={{ sm: '300' }}
                h="240"
                alt="Door to More banner"
              />
            </Stack>
          </Group>
          <Stack gap="8" justify="center">
            <Title size="h2" order={3} component="p" ta="center">
              Ready to win?
            </Title>
            <Title fw="400" size="h4" component="p" ta="center">
              Attend an appointment with an Aussie {isBuyersAgent ? 'Property Expert' : 'Broker'}{' '}
              for your chance to win.^
            </Title>
          </Stack>
        </Stack>
      </AnchorWithTracking>
    </Box>
  );
}
