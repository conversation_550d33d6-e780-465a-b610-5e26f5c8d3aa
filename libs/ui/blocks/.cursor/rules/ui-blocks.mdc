# UI Blocks Library Guidelines

## Overview
UI Blocks are high-level, composed components that combine multiple UI components to create complete page sections. These blocks are designed for rapid page construction and consistent layouts across the Growth Product applications.

## Import Pattern
```typescript
import { BlockName } from '@gp/ui/blocks';
```

## Block Categories

### 1. Page Header Blocks

#### Hero
Complete hero section with heading, subtitle, CTA buttons, and hero image.

**Props:**
- `heading` (required): Main hero title
- `subtitle`: Supporting text
- `buttons`: Array of CTA button configurations
- `trustBar`: Whether to show product review widget
- `heroImage`: Image configuration object
- `backgroundColour`: Background color (default: 'grape.10')
- `headingColour`: Heading text color (default: 'white')
- `subtitleColour`: Subtitle text color (default: 'white')

**Types:**
```typescript
interface ImageType {
  url: string;
  description: string;
}

interface ButtonProps {
  label: string;
  href: string;
}

interface HeroProps {
  heading: string;
  subtitle?: string;
  buttons?: ButtonProps[];
  trustBar?: boolean;
  heroImage: ImageType;
  backgroundColour?: string;
  headingColour?: string;
  subtitleColour?: string;
}
```

**Usage:**
```typescript
<Hero
  heading="Find Your Perfect Home Loan"
  subtitle="Get expert guidance and competitive rates with Aussie"
  heroImage={{
    url: "/hero-image.jpg",
    description: "Happy family with house keys"
  }}
  buttons={[
    { label: "Get Started", href: "/apply" },
    { label: "Learn More", href: "/about" }
  ]}
  trustBar={true}
  backgroundColour="grape.8"
/>
```

**Features:**
- Responsive layout (stacked on mobile, side-by-side on desktop)
- Automatic analytics tracking for CTA buttons
- Integrated ProductReviewWidget for trust signals
- Customizable colors and styling
- SEO-optimized heading structure

### 2. Content Blocks

#### FeatureBlock
Two-column feature section with image and content.

**Props:**
- `title` (required): Feature title
- `description` (required): Feature description
- `imageUrl` (required): Feature image URL
- `imagePlacement`: 'left' | 'right' (default: 'left')
- `cta`: Call-to-action button text
- `ctaLink`: CTA button link
- `fallbackImageUrl`: Fallback image if main image fails
- `position`: Analytics position identifier
- `children`: Additional content elements
- `wide`: Whether to use wide layout on smaller screens

**Usage:**
```typescript
<FeatureBlock
  title="Borrowing Power Calculator"
  description="Discover how much you could borrow with our easy-to-use calculator"
  imageUrl="/calculator-feature.jpg"
  imagePlacement="right"
  cta="Try Calculator"
  ctaLink="/calculators/borrowing-power"
  position="Features Section"
  wide={true}
/>
```

**Features:**
- Responsive image placement
- Automatic image optimization
- Integrated CTA tracking
- Support for custom content via children
- Fallback image handling

#### AccordionBlock
FAQ/expandable content section with multiple accordion items.

**Props:**
- `items` (required): Array of accordion items
- `maxWidth` (required): Container max width
- `description`: Optional header content
- `accordionProps`: Additional Mantine Accordion props
- `uppercaseTitles`: Whether to uppercase accordion titles

**Types:**
```typescript
interface AccordionBlockItemProps {
  details: ReactNode;
  summary: string;
}

interface AccordionBlockProps {
  accordionProps?: AccordionProps<boolean>;
  description?: ReactNode;
  items: Array<AccordionBlockItemProps>;
  maxWidth: CSSProperties['maxWidth'];
  uppercaseTitles?: boolean;
}
```

**Usage:**
```typescript
<AccordionBlock
  maxWidth="800px"
  description={
    <Title order={2}>Frequently Asked Questions</Title>
  }
  items={[
    {
      summary: "How much can I borrow?",
      details: <Text>Your borrowing capacity depends on...</Text>
    },
    {
      summary: "What documents do I need?",
      details: <Text>You'll need proof of income...</Text>
    }
  ]}
  uppercaseTitles={false}
/>
```

**Features:**
- Automatic analytics tracking for accordion interactions
- Customizable styling and layout
- Support for rich content in accordion panels
- Responsive design
- SEO-friendly heading structure

### 3. Card Collection Blocks

#### VerticalCardsBlock
Grid of vertical cards with icons, headings, and optional links.

**Props:**
- `title` (required): Section title
- `subtitle`: Section subtitle
- `verticalCards`: Array of card configurations
- `hasTopDivider`: Whether to show top divider

**Types:**
```typescript
interface ImageType {
  url: string;
  description: string;
}

interface TrackingProps {
  position: string;
  purpose: string;
}

interface VerticalCardProps {
  heading: string;
  subHeading?: string;
  icon?: ImageType;
  link?: string;
  trackingProps?: TrackingProps;
}
```

**Usage:**
```typescript
<VerticalCardsBlock
  title="Our Services"
  subtitle="Everything you need for your home buying journey"
  hasTopDivider={true}
  verticalCards={[
    {
      heading: "Home Loans",
      subHeading: "Competitive rates and expert guidance",
      icon: { url: "/home-loan-icon.png", description: "Home loan icon" },
      link: "/home-loans",
      trackingProps: {
        position: "Services Section",
        purpose: "Service Navigation"
      }
    },
    {
      heading: "Refinancing",
      subHeading: "Save money with better rates",
      icon: { url: "/refinance-icon.png", description: "Refinance icon" },
      link: "/refinance"
    }
  ]}
/>
```

**Features:**
- Responsive grid layout
- Automatic analytics tracking for card interactions
- Optional divider styling
- Flexible card content
- Icon optimization

#### ArticlesCarouselBlock
Carousel of article cards with images and content.

**Props:**
- `articles` (required): Array of article objects
- `title`: Section title
- `description`: Section description
- `tagId` (required): Tag identifier for analytics
- `pillarTagId`: Pillar tag identifier
- `name`: Component name for tracking
- `slugToMatch`: Slug matching pattern (future use)

**Types:**
```typescript
interface Article {
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}
```

**Usage:**
```typescript
<ArticlesCarouselBlock
  title="Latest Articles"
  description="Stay informed with our latest insights"
  tagId="home-loans"
  name="Home Loan Articles"
  articles={[
    {
      title: "First Home Buyer Guide",
      description: "Everything you need to know about buying your first home",
      imageUrl: "/article-1.jpg",
      link: "/articles/first-home-buyer-guide"
    },
    {
      title: "Investment Property Tips",
      description: "Smart strategies for property investment",
      imageUrl: "/article-2.jpg", 
      link: "/articles/investment-property-tips"
    }
  ]}
/>
```

**Features:**
- Responsive carousel with configurable slide sizes
- Automatic image optimization
- Integrated read-more functionality
- Analytics tracking for article interactions
- Empty state handling

### 4. Promotional Blocks

#### CompetitionBanner
Promotional banner for competitions and special offers.

**Props:**
- `variant`: CompetitionVariant.BUYERS_AGENT | CompetitionVariant.MORTGAGE_BROKER

**Types:**
```typescript
enum CompetitionVariant {
  BUYERS_AGENT = 'BUYERS_AGENT',
  MORTGAGE_BROKER = 'MORTGAGE_BROKER'
}
```

**Usage:**
```typescript
<CompetitionBanner variant={CompetitionVariant.MORTGAGE_BROKER} />

<CompetitionBanner variant={CompetitionVariant.BUYERS_AGENT} />
```

**Features:**
- Pre-configured promotional content
- Responsive design (different layouts for mobile/desktop)
- Automatic tracking for banner interactions
- Background image integration
- Variant-specific messaging

## Block Composition Patterns

### 1. Landing Page Structure
```typescript
// Typical landing page composition
<>
  <Hero
    heading="Your Home Loan Journey Starts Here"
    subtitle="Expert guidance every step of the way"
    heroImage={{ url: "/hero.jpg", description: "Happy family" }}
    buttons={[{ label: "Get Started", href: "/apply" }]}
    trustBar={true}
  />
  
  <FeatureBlock
    title="Calculate Your Borrowing Power"
    description="See how much you could borrow in minutes"
    imageUrl="/calculator.jpg"
    cta="Try Calculator"
    ctaLink="/calculators/borrowing-power"
  />
  
  <VerticalCardsBlock
    title="Our Services"
    verticalCards={serviceCards}
  />
  
  <AccordionBlock
    maxWidth="800px"
    items={faqItems}
  />
</>
```

### 2. Content Hub Structure
```typescript
// Article/content hub composition
<>
  <Hero
    heading="Home Loan Insights"
    subtitle="Expert advice and market updates"
    heroImage={{ url: "/insights-hero.jpg", description: "Market insights" }}
    backgroundColour="blue.8"
  />
  
  <ArticlesCarouselBlock
    title="Latest Articles"
    tagId="home-loans"
    articles={featuredArticles}
  />
  
  <ArticlesCarouselBlock
    title="Investment Tips"
    tagId="investment"
    articles={investmentArticles}
  />
</>
```

## Best Practices

### 1. Content Strategy
- Use Hero blocks for primary page messaging
- Combine FeatureBlocks to showcase key features/benefits
- Use AccordionBlocks for FAQ sections and detailed information
- Implement ArticlesCarouselBlocks for content discovery

### 2. Analytics Implementation
- All blocks include automatic analytics tracking
- Provide meaningful position and purpose props
- Use consistent naming conventions for tracking
- Leverage built-in component tracking features

### 3. Performance Optimization
- Blocks automatically optimize images
- Use appropriate image sizes and formats
- Implement lazy loading where applicable
- Minimize bundle size with tree-shaking

### 4. Responsive Design
- All blocks are mobile-first responsive
- Test across different screen sizes
- Use responsive props for fine-tuning
- Consider mobile-specific layouts

### 5. Accessibility
- Blocks follow WCAG 2.1 guidelines
- Use proper heading hierarchy
- Provide meaningful alt text for images
- Ensure keyboard navigation support

### 6. SEO Optimization
- Use semantic HTML structure
- Implement proper heading hierarchy (H1 → H2 → H3)
- Provide descriptive content
- Optimize images with proper alt attributes

## Common Use Cases

### 1. Product Landing Pages
```typescript
<Hero /> + <FeatureBlock /> + <VerticalCardsBlock /> + <AccordionBlock />
```

### 2. Content Hubs
```typescript
<Hero /> + <ArticlesCarouselBlock /> × multiple
```

### 3. Service Pages
```typescript
<Hero /> + <FeatureBlock /> × multiple + <CompetitionBanner />
```

### 4. FAQ Pages
```typescript
<Hero /> + <AccordionBlock /> × multiple
```

## Migration Guide

When migrating from custom sections to blocks:

1. **Identify Content Patterns**: Map existing sections to appropriate blocks
2. **Extract Content**: Separate content from layout logic
3. **Configure Props**: Set up required and optional props
4. **Test Responsive**: Verify mobile and desktop layouts
5. **Validate Analytics**: Ensure tracking events fire correctly
6. **Performance Check**: Monitor bundle size and loading performance

## Testing

Each block includes comprehensive testing:
- Unit tests for component logic
- Integration tests for user interactions
- Accessibility tests for WCAG compliance
- Visual regression tests for UI consistency
- Analytics tests for tracking events

Use testing utilities:
```typescript
import { render, screen } from '@testing-library/react';
import { Hero } from '@gp/ui/blocks';

test('renders hero with required props', () => {
  render(
    <Hero
      heading="Test Heading"
      heroImage={{ url: "/test.jpg", description: "Test image" }}
    />
  );
  expect(screen.getByText('Test Heading')).toBeInTheDocument();
});
```