# UI Components Library Guidelines

## Overview
This library provides a comprehensive set of pre-built, tracked, and themed components for the Growth Product applications. All components are designed with analytics tracking, accessibility, and consistent theming in mind.

## Import Pattern
```typescript
import { ComponentName } from '@gp/ui/components';
```

## Component Categories

### 1. Interactive Components with Tracking

#### ButtonWithTracking
Enhanced button component with built-in analytics tracking.

**Props:**
- `label` (required): Button text for analytics
- `position`: Context/location for tracking
- `purpose`: Intent/purpose for tracking
- `category`: Event category for analytics
- All standard Mantine Button props

**Usage:**
```typescript
<ButtonWithTracking
  label="Get Started"
  position="Hero Section"
  purpose="Lead Generation"
  variant="primary"
  onClick={handleClick}
>
  Get Started
</ButtonWithTracking>
```

#### LinkButtonWithTracking
Link-styled button with tracking capabilities.

**Usage:**
```typescript
<LinkButtonWithTracking
  label="Learn More"
  position="Article Card"
  href="/learn-more"
>
  Learn More
</LinkButtonWithTracking>
```

#### AnchorWithTracking
Enhanced anchor element with analytics.

**Usage:**
```typescript
<AnchorWithTracking
  label="External Link"
  position="Footer"
  href="https://example.com"
>
  Visit Site
</AnchorWithTracking>
```

### 2. Form Components

#### FormWithTracking
Form wrapper with automatic form analytics.

**Props:**
- `name` (required): Form identifier for tracking
- `form`: Mantine form instance
- `handleSubmit`: Submit handler
- `handleValidationFailure`: Validation error handler
- `shouldWaitForTracking`: Whether to wait for analytics

**Usage:**
```typescript
<FormWithTracking
  name="Contact Form"
  form={form}
  handleSubmit={handleSubmit}
>
  {/* Form fields */}
</FormWithTracking>
```

#### CheckboxWithTracking
Checkbox with analytics tracking.

**Usage:**
```typescript
<CheckboxWithTracking
  label="I agree to terms"
  position="Registration Form"
  {...form.getInputProps('terms')}
/>
```

#### CheckboxCardWithTracking
Card-style checkbox component.

**Usage:**
```typescript
<CheckboxCardWithTracking
  label="Premium Plan"
  description="Advanced features included"
  position="Pricing Section"
  {...form.getInputProps('plan')}
/>
```

#### SegmentedControlWithTracking
Segmented control with tracking.

**Usage:**
```typescript
<SegmentedControlWithTracking
  data={[
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' }
  ]}
  position="Settings"
  {...form.getInputProps('option')}
/>
```

### 3. Input Components

#### Autocomplete
Enhanced autocomplete with custom styling.

**Usage:**
```typescript
<Autocomplete
  data={suggestions}
  placeholder="Search..."
  label="Location"
  {...form.getInputProps('location')}
/>
```

#### MultSelectAutocomplete
Multi-select autocomplete component.

**Types:**
```typescript
interface Option {
  value: string;
  label: string;
}
```

**Usage:**
```typescript
<MultSelectAutocomplete
  data={options}
  placeholder="Select multiple options"
  {...form.getInputProps('selections')}
/>
```

#### FieldLabel
Consistent field label component.

**Usage:**
```typescript
<FieldLabel required>
  Email Address
</FieldLabel>
```

### 4. Display Components

#### Card
Flexible card component with multiple layouts.

**Props:**
- `type`: 'horizontal' | 'vertical' | 'badge'
- `title` (required): Card title
- `description` (required): Card description
- `image` (required): Image URL
- `badge`: Optional badge element

**Usage:**
```typescript
<Card
  type="vertical"
  title="Home Loan Calculator"
  description="Calculate your borrowing power"
  image="/calculator-icon.png"
  badge={<Badge>Popular</Badge>}
/>
```

#### VerticalCard
Specialized vertical card layout.

**Usage:**
```typescript
<VerticalCard
  title="Service Title"
  description="Service description"
  image="/service-icon.png"
/>
```

#### ImageCard
Card component optimized for image display.

**Usage:**
```typescript
<ImageCard
  title="Property Image"
  image="/property.jpg"
  description="Beautiful 3BR home"
/>
```

### 5. Navigation Components

#### TabsWithTracking
Tabs component with analytics.

**Usage:**
```typescript
<TabsWithTracking
  defaultValue="tab1"
  position="Product Details"
>
  <Tabs.List>
    <Tabs.Tab value="tab1">Overview</Tabs.Tab>
    <Tabs.Tab value="tab2">Features</Tabs.Tab>
  </Tabs.List>
  <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
  <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
</TabsWithTracking>
```

#### Carousel
Feature-rich carousel with tracking and responsive design.

**Props:**
- `slideSize`: Responsive slide sizes or number
- `position`: Location for tracking
- `controlPosition`: 'bottom' | 'center'
- `pageIndicatorPosition`: 'left' | 'right' | 'hidden'
- `variant`: 'default' | 'hover'

**Usage:**
```typescript
<Carousel
  slideSize={{ xs: 85, sm: 50, md: 33.33, lg: 28.57 }}
  position="Featured Products"
  controlPosition="bottom"
  name="Product Carousel"
>
  <Carousel.Slide>Slide 1</Carousel.Slide>
  <Carousel.Slide>Slide 2</Carousel.Slide>
</Carousel>
```

### 6. Modal Components

#### ModalWithTracking
Modal with automatic display/close tracking.

**Props:**
- `name` (required): Modal identifier for tracking
- `position`: Context for tracking
- `footer`: Optional footer content
- `footerStyle`: Footer styling

**Usage:**
```typescript
<ModalWithTracking
  opened={opened}
  onClose={close}
  name="Contact Modal"
  position="Header"
  footer={
    <ButtonWithTracking
      label="Submit"
      onClick={handleSubmit}
    />
  }
>
  Modal content
</ModalWithTracking>
```

#### InfoModal
Pre-configured info modal with trigger button.

**Props:**
- `title` (required): Modal title
- `content` (required): Modal content
- `icon`: Custom trigger icon
- `buttonLabel`: Close button text

**Usage:**
```typescript
<InfoModal
  title="Help Information"
  content={<Text>Helpful information here</Text>}
  buttonLabel="Got it"
/>
```

### 7. Feedback Components

#### AlertWithTracking
Alert component with analytics.

**Usage:**
```typescript
<AlertWithTracking
  title="Success!"
  message="Your form has been submitted"
  type="success"
  position="Form Feedback"
/>
```

#### Loader
Custom loading component.

**Usage:**
```typescript
<Loader />
```

#### ProgressBar
Custom progress bar component.

**Props:**
- `progress`: Number between 0 and 1

**Usage:**
```typescript
<ProgressBar progress={0.75} />
```

### 8. Data Visualization

#### AreaChart
Themed area chart component.

**Props:**
- `series`: Array of data series names
- All Mantine AreaChart props

**Usage:**
```typescript
<AreaChart
  data={chartData}
  series={['revenue', 'profit']}
  dataKey="month"
/>
```

#### BarChart
Themed bar chart component.

**Usage:**
```typescript
<BarChart
  data={chartData}
  series={[{ name: 'sales', color: 'grape.7' }]}
  dataKey="month"
/>
```

#### DonutChart
Themed donut chart component.

**Usage:**
```typescript
<DonutChart
  data={[
    { name: 'Category A', value: 400, color: 'grape.7' },
    { name: 'Category B', value: 300, color: 'blue.6' }
  ]}
/>
```

### 9. Content Components

#### SectionHeader
Consistent section header component.

**Usage:**
```typescript
<SectionHeader
  title="Featured Products"
  subtitle="Discover our most popular offerings"
/>
```

#### ReadMoreWithArrow
Expandable content with arrow indicator.

**Usage:**
```typescript
<ReadMoreWithArrow
  content="Long content that can be expanded..."
  maxLength={150}
/>
```

#### Collapse
Collapsible content container.

**Usage:**
```typescript
<Collapse in={opened}>
  <Text>Collapsible content</Text>
</Collapse>
```

### 10. Utility Components

#### Image
Optimized image component with loading states.

**Usage:**
```typescript
<Image
  src="/image.jpg"
  alt="Description"
  fetchPriority="high"
  loading="eager"
/>
```

#### PageTracking
Page view tracking component.

**Usage:**
```typescript
<PageTracking pageName="Home Page" />
```

#### AppDownloadButtons
Pre-configured app download buttons.

**Usage:**
```typescript
<AppDownloadButtons
  position="Footer"
  iosUrl="https://apps.apple.com/app/id123"
  androidUrl="https://play.google.com/store/apps/details?id=com.app"
/>
```

#### AccordionItemWithTracking
Accordion item with tracking.

**Usage:**
```typescript
<AccordionItemWithTracking
  value="faq-1"
  title="Frequently Asked Question"
  position="FAQ Section"
>
  Answer content
</AccordionItemWithTracking>
```

#### BadgeWithIconAndText
Badge component with icon and text.

**Usage:**
```typescript
<BadgeWithIconAndText
  icon={<CheckIcon />}
  text="Verified"
  variant="success"
/>
```

#### ProductReviewWidget
Product review display widget.

**Usage:**
```typescript
<ProductReviewWidget
  rating={4.5}
  reviewCount={1234}
  productName="Home Loan"
/>
```

## Best Practices

### 1. Analytics Tracking
- Always provide meaningful `label` props for tracking components
- Use descriptive `position` props to identify component location
- Include `purpose` when the intent isn't obvious from context

### 2. Accessibility
- All components follow WCAG 2.1 guidelines
- Use semantic HTML elements where appropriate
- Provide proper ARIA labels and descriptions

### 3. Performance
- Components are optimized for bundle size
- Use lazy loading for heavy components
- Implement proper memoization where needed

### 4. Styling
- Components inherit theme styles automatically
- Use theme tokens instead of custom styles
- Follow responsive design patterns

### 5. Error Handling
- Components include proper error boundaries
- Provide fallback states for loading/error conditions
- Validate props with TypeScript

## Migration Notes

When migrating from plain Mantine components:
1. Replace component imports with tracked versions
2. Add required tracking props (`label`, `position`, etc.)
3. Update prop names if they differ from Mantine
4. Test analytics events in development

## Testing

All components include:
- Unit tests for functionality
- Accessibility tests
- Analytics tracking tests
- Visual regression tests

Use the provided test utilities:
```typescript
import { render, screen } from '@testing-library/react';
import { ComponentName } from '@gp/ui/components';

test('renders component correctly', () => {
  render(<ComponentName label="Test" />);
  expect(screen.getByText('Test')).toBeInTheDocument();
});
```