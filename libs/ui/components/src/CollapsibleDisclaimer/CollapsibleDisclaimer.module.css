.details {
  border: none;
  margin-bottom: 0;
}

.summary {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 16px 0;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  gap: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'tt-commons-pro, sans-serif';
  list-style: none;
}

.icon {
  font-size: 12px;
  color: #6f42c1;
  line-height: 1;
  transition: transform 0.2s ease;
}

.details[open] .icon {
  transform: rotate(90deg);
}

.details:not([open]) .icon {
  transform: rotate(0deg);
}

.details summary::-webkit-details-marker {
  display: none;
}

.details summary::marker {
  display: none;
}

.content {
  padding: 20px 0;
  padding-left: 24px;
  background-color: #ffffff;
} 