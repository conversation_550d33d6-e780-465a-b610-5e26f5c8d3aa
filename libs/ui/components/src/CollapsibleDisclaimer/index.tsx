'use client';

import React, { useEffect, useState } from 'react';
import { Box, Stack } from '@mantine/core';

import styles from './CollapsibleDisclaimer.module.css';

interface CollapsibleDisclaimerProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

export function CollapsibleDisclaimer({
  title,
  children,
  defaultOpen = false,
}: CollapsibleDisclaimerProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch by not rendering details until mounted
  if (!mounted) {
    return (
      <Box
        style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '40px 20px',
          backgroundColor: '#ffffff',
        }}
      >
        <Stack gap="lg">
          <div className={styles.summary}>
            <span className={styles.icon}>►</span>
            <span style={{ fontFamily: 'tt-commons-pro, sans-serif' }}>{title}</span>
          </div>
        </Stack>
      </Box>
    );
  }

  return (
    <Box
      style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '40px 20px',
        backgroundColor: '#ffffff',
      }}
    >
      <Stack gap="lg">
        <details className={styles.details} open={isOpen}>
          <summary
            className={styles.summary}
            onClick={(e) => {
              e.preventDefault();
              setIsOpen(!isOpen);
            }}
          >
            <span className={styles.icon}>►</span>
            <span style={{ fontFamily: 'tt-commons-pro, sans-serif' }}>{title}</span>
          </summary>
          {isOpen && <div className={styles.content}>{children}</div>}
        </details>
      </Stack>
    </Box>
  );
}
