'use client';

import { forwardRef, useContext } from 'react';
import { Tabs, TabsProps } from '@mantine/core';
import { AuthenticationStatus } from '@lendi/analytics-web';
import { Status, useSession } from '@lendi/lala-react';

import { LDContext } from '@gp/shared/launchdarkly';

interface TabWithTrackingProps extends TabsProps {
  position?: string;
  name?: string;
  linkType?: string;
  type?: string;
  variantId?: string;
  tabLabels?: Record<string, string>;
}

const TabsWithTracking = forwardRef<HTMLDivElement, TabWithTrackingProps>((props, ref) => {
  const {
    position = '',
    name,
    type,
    variantId,
    tabLabels,
    onChange,
    ...otherProps
  } = props as TabWithTrackingProps;
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const authenticationStatus = isAuthenticated
    ? AuthenticationStatus.AUTHENTICATED
    : AuthenticationStatus.UNAUTHENTICATED;
  const { analytics } = useContext(LDContext);

  const handleClick = (value: string) => {
    analytics?.trackEvent({
      event_name: 'Tab Clicked',
      position,
      tabName: value,
      text: value,
      ...(type && { type }),
      ...(name && { name }),
      ...(variantId && { variantId }),
      ...(tabLabels && { tabSelected: tabLabels[value] }),
      authenticationStatus,
    });
    onChange && onChange(value);
  };
  return (
    <Tabs {...otherProps} onChange={(value) => handleClick(value as string)} ref={ref}>
      {props.children}
    </Tabs>
  );
});

export default TabsWithTracking;
