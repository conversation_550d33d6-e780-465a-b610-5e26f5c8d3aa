import type { SVGProps } from 'react';

export default function SvgHomeSearch(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 25 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#212529"
        fillRule="evenodd"
        d="M11.793 2.626a1 1 0 0 1 1.414 0l9 9a1 1 0 1 1-1.414 1.414L12.5 4.747l-6.65 6.65a1 1 0 0 1 .65.936v7a1 1 0 0 0 1 1h1v-5a3 3 0 0 1 3-3h2a1 1 0 1 1 0 2h-2a1 1 0 0 0-1 1v5h1.7a1 1 0 1 1 0 2H7.5a3 3 0 0 1-3-3v-6h-1a1 1 0 0 1-.707-1.707zM18.5 16.333a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-4 2a4 4 0 1 1 7.446 2.032l1.261 1.261a1 1 0 1 1-1.414 1.414l-1.26-1.26a4 4 0 0 1-6.032-3.446"
        clipRule="evenodd"
      />
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M11.793 2.626a1 1 0 0 1 1.414 0l9 9a1 1 0 1 1-1.414 1.414L12.5 4.747l-6.65 6.65a1 1 0 0 1 .65.936v7a1 1 0 0 0 1 1h1v-5a3 3 0 0 1 3-3h2a1 1 0 1 1 0 2h-2a1 1 0 0 0-1 1v5h1.7a1 1 0 1 1 0 2H7.5a3 3 0 0 1-3-3v-6h-1a1 1 0 0 1-.707-1.707zM18.5 16.333a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-4 2a4 4 0 1 1 7.446 2.032l1.261 1.261a1 1 0 1 1-1.414 1.414l-1.26-1.26a4 4 0 0 1-6.032-3.446"
        clipRule="evenodd"
      />
    </svg>
  );
}
