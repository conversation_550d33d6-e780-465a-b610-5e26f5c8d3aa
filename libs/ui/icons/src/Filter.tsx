import type { SVGProps } from 'react';

export default function SvgFilter(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#4B1F68"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 18h-6M10 18H5M19 12h-8M8 12H5M19 6h-3M13 6H5M13 20v-4M8 14v-4M16 8V4"
      />
    </svg>
  );
}
