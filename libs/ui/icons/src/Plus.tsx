import type { SVGProps } from 'react';

export default function SvgPlus(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 16 16"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1D1D1D"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.333}
        d="M8 3.334v9.333M3.335 8h9.333"
      />
    </svg>
  );
}
