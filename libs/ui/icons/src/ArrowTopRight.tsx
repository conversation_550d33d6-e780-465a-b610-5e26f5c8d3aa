import type { SVGProps } from 'react';

export default function SvgArrowTopRight(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path stroke="#4B1F68" strokeLinecap="round" strokeWidth={1.5} d="m8 16 8-8" />
      <path
        stroke="#4B1F68"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M9 8h7v7"
      />
    </svg>
  );
}
