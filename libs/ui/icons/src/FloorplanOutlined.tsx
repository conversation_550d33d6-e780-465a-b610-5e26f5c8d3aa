import type { SVGProps } from 'react';

export default function SvgFloorplanOutlined(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path fill="#fff" d="M0 0h24v24H0z" />
      <path
        stroke="#000"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M16 15.5c-2.4 0-3 2.333-3 3.5h-3m6 0h1.5a1.5 1.5 0 0 0 1.5-1.5V10m-9-5h7.5A1.5 1.5 0 0 1 19 6.5V10m-9-5H6.5A1.5 1.5 0 0 0 5 6.5v11A1.5 1.5 0 0 0 6.5 19H10m0-14v5m0 3v-3m0 0h3m3 0h3m-9 6v3"
      />
    </svg>
  );
}
