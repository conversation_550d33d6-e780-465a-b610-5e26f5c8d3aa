import type { SVGProps } from 'react';

export default function SvgFloorArea(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1D1D1D"
        strokeLinecap="round"
        strokeWidth={1.5}
        d="m5 14.833 5.25 3.5M5 14.833v2.334m0-2.334 2.333-.583M19 14.833l-5.25 3.5m5.25-3.5v2.334m0-2.334-2.333-.583"
      />
      <path
        stroke="#1D1D1D"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="m19 9.583-7 4.667-7-4.667 7-4.666z"
      />
    </svg>
  );
}
