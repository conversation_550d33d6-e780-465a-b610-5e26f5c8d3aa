import type { SVGProps } from 'react';

export default function SvgLogoIcon(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 49 48"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#FFD11F"
        d="M45.484 38.424a.7.7 0 0 1-.699.699H4.195a.7.7 0 0 1-.699-.699v-1.748a.7.7 0 0 1 .7-.703h40.59a.7.7 0 0 1 .698.703zM45.484 32.49c0 .385-.313.7-.699.7H4.195a.7.7 0 0 1-.699-.7v-1.753c0-.385.311-.699.7-.699h40.59a.7.7 0 0 1 .698.7z"
      />
      <path
        fill="#4B1F68"
        d="m44.956 24.747.006-.009-18.72-15.522s-.84-.781-1.68-.781c-.797 0-1.71.797-1.71.797L4.04 24.747h.009c-.326.269-.541.667-.541 1.114 0 .801.674 1.456 1.5 1.456h38.96l.005-.003q.009.002.024.003c.829 0 1.502-.655 1.502-1.456 0-.447-.216-.845-.543-1.114m-5.421-.578c0 .13-.11.239-.243.239v.001H9.745l.014-.002-.031.001a.24.24 0 0 1-.238-.24c0-.083.049-.159.12-.2l14.346-11.834s.354-.307.62-.307c.245 0 .595.326.595.326l14.27 11.835a.23.23 0 0 1 .094.18"
      />
    </svg>
  );
}
