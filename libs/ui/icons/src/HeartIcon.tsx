import type { SVGProps } from 'react';

export default function SvgHeartIcon(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#4B1F68"
        strokeLinejoin="round"
        strokeLinecap="round"
        strokeWidth={1.5}
        d="M15.906 6C12.976 6 12.5 8.806 12 8.806S11.024 6 8.094 6s-3.417 2.658-2.93 4.21c.343 1.084 3.554 4.466 5.45 6.399a1.936 1.936 0 0 0 2.772 0c1.897-1.933 5.107-5.315 5.45-6.4.487-1.55 0-4.209-2.93-4.209Z"
      />
    </svg>
  );
}
