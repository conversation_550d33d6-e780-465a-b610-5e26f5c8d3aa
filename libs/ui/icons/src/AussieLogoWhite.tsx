import type { SVGProps } from 'react';

export default function SvgAussieLogoWhite(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 89 88"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#FFD11F"
        d="M82.593 70.447c0 .706-.575 1.281-1.281 1.281H6.897a1.28 1.28 0 0 1-1.282-1.281v-3.206c0-.712.57-1.287 1.282-1.287h74.415c.706 0 1.281.575 1.281 1.287zM82.593 59.567c0 .704-.575 1.283-1.281 1.283H6.897a1.283 1.283 0 0 1-1.282-1.284v-3.213c0-.707.57-1.282 1.282-1.282h74.415c.706 0 1.281.575 1.281 1.282z"
      />
      <path
        fill="#fff"
        d="m81.62 45.37.01-.015-34.318-28.457s-1.542-1.433-3.082-1.433c-1.462 0-3.134 1.462-3.134 1.462L6.606 45.37h.017c-.597.494-.992 1.223-.992 2.043 0 1.468 1.237 2.67 2.748 2.67h71.43l.009-.005c.008 0 .026.005.044.005 1.518 0 2.754-1.202 2.754-2.67 0-.82-.398-1.55-.996-2.043m-9.94-1.06c0 .24-.201.44-.445.44v.002H17.067s.011-.005.024-.005l-.057.002a.44.44 0 0 1-.435-.439c0-.153.089-.291.22-.368l26.3-21.695s.649-.563 1.137-.563c.449 0 1.09.597 1.09.597L71.51 43.979c.1.076.17.199.17.331"
      />
    </svg>
  );
}
