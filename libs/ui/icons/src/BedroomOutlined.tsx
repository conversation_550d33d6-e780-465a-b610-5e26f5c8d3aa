import type { SVGProps } from 'react';

export default function SvgBedroomOutlined(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1E1C1B"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M10.286 16.25V9.1m0 0h7.428c.607 0 1.188.275 1.617.762.428.488.669 1.15.669 1.839v4.55M10.286 9.1H4m16 7.15H4m16 0v3.25m-16 0v-13"
      />
    </svg>
  );
}
