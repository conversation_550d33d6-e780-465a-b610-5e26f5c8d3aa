import type { SVGProps } from 'react';

export default function SvgShield(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 25 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#212529"
        fillRule="evenodd"
        d="M11.837 2.251a1 1 0 0 1 1.326 0 11 11 0 0 0 7.791 2.75 1 1 0 0 1 1.005.717 13 13 0 0 1-9.208 16.25 1 1 0 0 1-.502 0A13 13 0 0 1 3.04 5.718a1 1 0 0 1 1.006-.717 11 11 0 0 0 7.791-2.75m-7.065 4.76A11 11 0 0 0 12.5 19.964 11 11 0 0 0 20.228 7.01 13 13 0 0 1 12.5 4.297a13 13 0 0 1-7.728 2.714m11.435 2.282a1 1 0 0 1 0 1.414l-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414l1.293 1.293 3.293-3.293a1 1 0 0 1 1.414 0"
        clipRule="evenodd"
      />
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M11.837 2.251a1 1 0 0 1 1.326 0 11 11 0 0 0 7.791 2.75 1 1 0 0 1 1.005.717 13 13 0 0 1-9.208 16.25 1 1 0 0 1-.502 0A13 13 0 0 1 3.04 5.718a1 1 0 0 1 1.006-.717 11 11 0 0 0 7.791-2.75m-7.065 4.76A11 11 0 0 0 12.5 19.964 11 11 0 0 0 20.228 7.01 13 13 0 0 1 12.5 4.297a13 13 0 0 1-7.728 2.714m11.435 2.282a1 1 0 0 1 0 1.414l-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414l1.293 1.293 3.293-3.293a1 1 0 0 1 1.414 0"
        clipRule="evenodd"
      />
    </svg>
  );
}
