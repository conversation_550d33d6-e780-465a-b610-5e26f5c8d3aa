import type { SVGProps } from 'react';

export default function SvgEdit(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 16 16"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#4B1F68"
        fillRule="evenodd"
        d="M11.14 1.938a2.067 2.067 0 0 1 2.922 2.923l-.843.846a.7.7 0 0 1-.177.177l-4.57 4.586a.67.67 0 0 1-.472.196H6A.667.667 0 0 1 5.333 10V8c0-.177.07-.347.196-.473l4.586-4.57a.7.7 0 0 1 .178-.177l.846-.842m-.466 2.345L6.667 8.277v1.056h1.056l3.993-4.007zm1.984.098-1.04-1.039.463-.46a.733.733 0 1 1 1.038 1.036zm-10.072.204A2 2 0 0 1 4 4h.667a.667.667 0 0 1 0 1.333H4A.667.667 0 0 0 3.333 6v6a.667.667 0 0 0 .667.666h6a.667.667 0 0 0 .667-.666v-.667a.667.667 0 1 1 1.333 0V12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 .586-1.415"
        clipRule="evenodd"
      />
    </svg>
  );
}
