import type { SVGProps } from 'react';

export default function SvgLandsizeOutlined(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#000"
        strokeLinecap="round"
        strokeWidth={1.5}
        d="m5 15.335 5.25 3.5M5 15.334v2.333m0-2.333 2.333-.584M19 15.335l-5.25 3.5m5.25-3.5v2.333m0-2.333-2.333-.584"
      />
      <path
        stroke="#000"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="m19 10.084-7 4.667-7-4.667 7-4.667z"
      />
    </svg>
  );
}
