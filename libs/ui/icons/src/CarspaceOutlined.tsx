import type { SVGProps } from 'react';

export default function SvgCarspaceOutlined(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1E1C1B"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M4 10.917h16M19 16.334v1.625a.52.52 0 0 1-.176.383.64.64 0 0 1-.424.159h-1.8a.64.64 0 0 1-.424-.159.52.52 0 0 1-.176-.383v-1.625M8 16.334v1.625a.52.52 0 0 1-.176.383.63.63 0 0 1-.424.159H5.6a.63.63 0 0 1-.424-.159.52.52 0 0 1-.176-.383v-1.625M7 13.084h2M15 13.084h2"
      />
      <path
        stroke="#1E1C1B"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="m19 11.236-2.18-5.353a.63.63 0 0 0-.214-.278.55.55 0 0 0-.319-.105H7.714a.55.55 0 0 0-.319.105.63.63 0 0 0-.214.278L5 11.236v5.098h14z"
      />
    </svg>
  );
}
