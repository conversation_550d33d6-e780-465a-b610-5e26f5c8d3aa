import type { SVGProps } from 'react';

export default function SvgBank(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M11.606 2.08a1 1 0 0 1 .788 0l7 3a1 1 0 0 1-.788 1.84L12 4.087 5.394 6.919a1 1 0 0 1-.788-1.838zM2 10a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2v9a1 1 0 1 1 0 2H3a1 1 0 1 1 0-2v-9a1 1 0 0 1-1-1m3 1v9h14v-9zm3 2a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1m4 0a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1m4 0a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1"
        clipRule="evenodd"
      />
    </svg>
  );
}
