import type { SVGProps } from 'react';

export default function SvgDollar(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1D1D1D"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15.72 8.3a2.8 2.8 0 0 0-2.52-1.4h-2.8a2.8 2.8 0 0 0 0 5.6h2.8a2.8 2.8 0 0 1 0 5.6h-2.8a2.8 2.8 0 0 1-2.52-1.4M11.8 5.5v14"
      />
    </svg>
  );
}
