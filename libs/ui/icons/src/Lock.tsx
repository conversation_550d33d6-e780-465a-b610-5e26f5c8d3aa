import type { SVGProps } from 'react';

export default function SvgLock(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 16 16"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#ABABA9"
        d="M12 5.334h-.667V4a3.335 3.335 0 0 0-6.666 0v1.334H4c-.733 0-1.333.6-1.333 1.333v6.667c0 .733.6 1.333 1.333 1.333h8c.733 0 1.333-.6 1.333-1.333V6.667c0-.733-.6-1.333-1.333-1.333m-4 6c-.734 0-1.333-.6-1.333-1.334 0-.733.6-1.333 1.333-1.333s1.333.6 1.333 1.333c0 .734-.6 1.334-1.333 1.334m2.066-6H5.934V4c0-1.14.927-2.066 2.067-2.066S10.066 2.86 10.066 4z"
      />
    </svg>
  );
}
