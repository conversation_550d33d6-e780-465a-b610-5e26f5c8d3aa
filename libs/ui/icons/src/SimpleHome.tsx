import type { SVGProps } from 'react';

export default function SvgSimpleHome(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1D1D1D"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 12.42a2 2 0 0 1 .698-1.519l5-4.285a2 2 0 0 1 2.604 0l5 4.285A2 2 0 0 1 19 12.42v5.08a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2z"
        clipRule="evenodd"
      />
      <path
        stroke="#1D1D1D"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13 19.5v-6h-3v2M13 13.5h1v6"
      />
    </svg>
  );
}
