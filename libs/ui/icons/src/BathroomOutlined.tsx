import type { SVGProps } from 'react';

export default function SvgBathroomOutlined(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        stroke="#1E1C1B"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M7.555 16.876V18.5M15.555 16.876V18.5M6.666 11.188V7.08c0-.419.14-.82.39-1.117.25-.296.59-.463.943-.463.354 0 .693.167.943.463s.39.698.39 1.117M17.333 10.376H12.89v2.437h4.444z"
      />
      <path
        stroke="#1E1C1B"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M17.143 11.188h2.286c.151 0 .297.054.404.151a.5.5 0 0 1 .167.366v2.068c0 .823-.361 1.612-1.004 2.194s-1.515.908-2.425.908H7.43c-.91 0-1.782-.326-2.425-.908S4 14.596 4 13.773v-2.068c0-.137.06-.269.167-.366a.6.6 0 0 1 .404-.151h8"
      />
    </svg>
  );
}
