import type { SVGProps } from 'react';

export default function SvgHeartHandshake(
  props: {
    size?: number;
  } & SVGProps<SVGSVGElement>
) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 25"
      width={props.size || 16}
      height={props.size}
      {...props}
    >
      <path
        fill="#212529"
        fillRule="evenodd"
        d="M8.412 5.344a4 4 0 0 0-3.214 6.844l.006.007L12 18.925l1.095-1.083-1.302-1.302a1 1 0 0 1 1.414-1.414l1.309 1.309 1.09-1.081-1.313-1.314a1 1 0 0 1 1.414-1.414l1.32 1.32 1.092-1.08-1.576-1.576a2.18 2.18 0 0 0-3.086 0l-1 1a2.77 2.77 0 0 1-3.914 0L8 11.747A2 2 0 0 1 8 8.92l2.614-2.614a4 4 0 0 0-2.202-.96m4.227 1.765-3.225 3.224.543.543c.3.3.787.3 1.086 0l1-1a4.18 4.18 0 0 1 5.914 0l1.49 1.49A4 4 0 0 0 12.8 6.94q-.072.095-.16.169m7.496 6.575a1 1 0 0 0 .066-.06 6 6 0 0 0-8.197-8.759 6 6 0 0 0-7.969-.058A6 6 0 0 0 3.8 13.62l7.497 7.425a1 1 0 0 0 1.407 0z"
        clipRule="evenodd"
      />
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M8.412 5.344a4 4 0 0 0-3.214 6.844l.006.007L12 18.925l1.095-1.083-1.302-1.302a1 1 0 0 1 1.414-1.414l1.309 1.309 1.09-1.081-1.313-1.314a1 1 0 0 1 1.414-1.414l1.32 1.32 1.092-1.08-1.576-1.576a2.18 2.18 0 0 0-3.086 0l-1 1a2.77 2.77 0 0 1-3.914 0L8 11.747A2 2 0 0 1 8 8.92l2.614-2.614a4 4 0 0 0-2.202-.96m4.227 1.765-3.225 3.224.543.543c.3.3.787.3 1.086 0l1-1a4.18 4.18 0 0 1 5.914 0l1.49 1.49A4 4 0 0 0 12.8 6.94q-.072.095-.16.169m7.496 6.575a1 1 0 0 0 .066-.06 6 6 0 0 0-8.197-8.759 6 6 0 0 0-7.969-.058A6 6 0 0 0 3.8 13.62l7.497 7.425a1 1 0 0 0 1.407 0z"
        clipRule="evenodd"
      />
    </svg>
  );
}
