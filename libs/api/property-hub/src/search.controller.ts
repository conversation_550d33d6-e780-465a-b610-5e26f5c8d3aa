import { <PERSON>, Get, ParseBoolPipe, Query, Res } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import {
  propertySchema,
  SEARCH_RESULT_TYPE,
  searchResultSchema,
} from '@gp/data-access/property-hub';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { SearchService } from './search.service';

class SearchResultOpenApiSchema extends createOpenApiSchemaFromZod(
  'SearchResult',
  searchResultSchema,
  {
    description:
      'The schema for a search result which could be a state, council, suburb, or address with a slug and name',
  }
) {}

class SearchPropertiesOpenApiSchema extends createOpenApiSchemaFromZod(
  'SearchProperties',
  propertySchema,
  {
    description:
      'The schema for a property search result which includes the basic property data from property hub RDS',
  }
) {}

@Controller('v1/property-hub')
export class SearchController {
  constructor(private searchService: SearchService) {}

  @ApiTags('Search')
  @ApiOperation({ description: 'Search for states, councils, suburbs, and addresses' })
  @ApiQuery({
    name: 'limit',
    description: 'The number of results to return, defaults to 10 if not specified',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'types',
    description: `The types of results to return, defaults to state,council,suburb,address if not specified`,
    required: false,
  })
  @ApiQuery({
    name: 'useV1',
    description: 'Override useNewPropertyTable with this boolean query param',
    required: false,
    type: 'boolean',
  })
  @ApiOkResponse({ description: 'Search results', type: SearchResultOpenApiSchema, isArray: true })
  @Get('search')
  async search(
    @Query('q') query: string,
    @Query('limit') limit = 10,
    @Query('types') searchTypes = 'state,council,suburb,address',
    @Query('useV1', new ParseBoolPipe({ optional: true })) useV1: boolean,
    @Res() res: Response
  ) {
    const types = searchTypes
      .split(',')
      .filter((type): type is SEARCH_RESULT_TYPE =>
        Object.values(SEARCH_RESULT_TYPE).includes(type as SEARCH_RESULT_TYPE)
      );
    let useNewProperty: boolean;
    if (useV1 !== undefined) {
      useNewProperty = !useV1;
    } else {
      useNewProperty = !!res.locals['useNewPropertyTable'];
    }
    return res.json(await this.searchService.search(query, limit, types, useNewProperty));
  }

  @ApiTags('Search')
  @ApiOperation({ description: 'Search for properties by address' })
  @ApiOkResponse({
    description: 'Search results',
    type: SearchPropertiesOpenApiSchema,
    isArray: true,
  })
  @ApiQuery({
    name: 'useV1',
    description: 'Override useNewPropertyTable with this boolean query param',
    required: false,
    type: 'boolean',
  })
  @Get('property-search')
  async searchProperties(
    @Query('q') query: string,
    @Query('useV1', new ParseBoolPipe({ optional: true })) useV1: boolean,
    @Res() res: Response
  ) {
    let useNewProperty: boolean;
    if (useV1 !== undefined) {
      useNewProperty = !useV1;
    } else {
      useNewProperty = !!res.locals['useNewPropertyTable'];
    }
    return res.json(await this.searchService.searchProperties(query, useNewProperty));
  }
}
