import { CopilotRuntime, copilotRuntimeNestEndpoint, OpenAIAdapter } from '@copilotkit/runtime';
import { Controller, Post, Req, Res } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiProduces, ApiTags } from '@nestjs/swagger';
import type { Request, Response } from 'express';

@Controller('v1/property-hub')
export class CopilotkitController {
  @ApiTags('Copilotkit')
  @ApiOperation({ description: 'Copilotkit endpoint' })
  @ApiBody({
    schema: {
      type: 'object',
    },
  })
  @ApiProduces('multipart/mixed')
  @Post('copilotkit')
  async copilotkit(@Req() req: Request, @Res() res: Response) {
    const serviceAdapter = new OpenAIAdapter();
    const runtime = new CopilotRuntime();
    const handler = copilotRuntimeNestEndpoint({
      runtime,
      serviceAdapter,
      endpoint: '/v1/property-hub/copilotkit',
    });
    await handler.handle(req, res);
    if (res.getHeader('Access-Control-Allow-Origin') && process.env['NODE_ENV'] === 'production') {
      // Remove the header in production to, as router will handle it
      res.removeHeader('Access-Control-Allow-Origin');
    }
  }
}
