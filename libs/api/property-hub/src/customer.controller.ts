import { Body, Controller, Delete, Get, Param, Post, Res } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';

import {
  type CustomerPropertyPayload,
  customerPropertyPayloadSchema,
  customerPropertySchema,
  type CustomerPropertySearch,
  CustomerPropertySearchSchema,
} from '@gp/data-access/property-hub';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { CustomerService } from './customer.service';

class CustomerPropertyOpenApiSchema extends createOpenApiSchemaFromZod(
  'CustomerProperty',
  customerPropertySchema,
  { description: 'The schema for a customer property' }
) {}

class CustomerPropertyRelationsOpenApiSchema extends createOpenApiSchemaFromZod(
  'CustomerPropertyRelations',
  customerPropertySchema
    .pick({
      propertyId: true,
      relationship: true,
      updatedAt: true,
    })
    .array(),
  { description: 'The schema for a customer property relations' }
) {}

class CustomerPropertyPayloadOpenApiSchema extends createOpenApiSchemaFromZod(
  'CustomerPropertyPayload',
  customerPropertyPayloadSchema,
  { description: 'The schema for a customer property payload' }
) {}

class CustomerPropertySearchPayloadOpenApiSchema extends createOpenApiSchemaFromZod(
  'CustomerPropertySearchPayload',
  CustomerPropertySearchSchema,
  { description: 'The schema for a customer properties search payload' }
) {}

class CustomerPropertySearchOpenApiSchema extends createOpenApiSchemaFromZod(
  'CustomerPropertySearch',
  CustomerPropertySearchSchema,
  { description: 'The schema for a customer property search' }
) {}

@Controller('v1/property-hub')
export class CustomerController {
  constructor(private CustomerService: CustomerService) {}

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Get('customer/:customerId/property-relations')
  @ApiOperation({ description: 'Get customer properties' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
  })
  @ApiOkResponse({
    description: 'The customer property relationships',
    type: CustomerPropertyRelationsOpenApiSchema,
    isArray: true,
  })
  async getCustomerPropertyRelations(
    @Param('customerId') customerId: string,
    @Res() res: Response
  ) {
    return res.json(
      await this.CustomerService.getCustomerPropertyRelations(
        customerId,
        res.locals['useNewPropertyTable']
      )
    );
  }

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Get('customer/:customerId/properties')
  @ApiOperation({ description: 'Get customer properties' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
  })
  @ApiOkResponse({
    description: 'The customer properties',
    type: CustomerPropertyOpenApiSchema,
    isArray: true,
  })
  async getCustomerProperties(@Param('customerId') customerId: string, @Res() res: Response) {
    return res.json(
      await this.CustomerService.getCustomerProperties(
        customerId,
        res.locals['useNewPropertyTable']
      )
    );
  }

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Post('customer/:customerId/properties')
  @ApiOperation({ description: 'Upsert a customer property' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
  })
  @ApiBody({ type: CustomerPropertyPayloadOpenApiSchema })
  async upsertCustomerProperty(
    @Param('customerId') customerId: string,
    @Body() payload: CustomerPropertyPayload,
    @Res() res: Response
  ) {
    return res.json(
      await this.CustomerService.upsertCustomerProperty(
        customerId,
        payload,
        res.locals['useNewPropertyTable']
      )
    );
  }

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Delete('customer/:customerId/properties/:propertyId')
  @ApiOperation({ description: 'Delete a customer property' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
  })
  @ApiParam({
    name: 'propertyId',
    description: 'The property Id',
  })
  async deleteCustomerProperty(
    @Param('customerId') customerId: string,
    @Param('propertyId') propertyId: string,
    @Res() res: Response
  ) {
    return res.json(
      await this.CustomerService.deleteCustomerProperty(
        customerId,
        propertyId,
        res.locals['useNewPropertyTable']
      )
    );
  }

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Get('customer/:customerId/property-searches')
  @ApiOperation({ description: 'Get customer property search criteria history' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
    required: true,
  })
  @ApiOkResponse({
    description: 'The customer property search criteria history',
    type: CustomerPropertySearchOpenApiSchema,
    isArray: true,
  })
  async getCustomerPropertySearches(@Param('customerId') customerId: string) {
    return this.CustomerService.getCustomerPropertySearches(customerId);
  }

  @ApiBearerAuth()
  @ApiTags('Customer')
  @Post('customer/:customerId/property-searches')
  @ApiOperation({ description: 'Upsert a customer property search criteria' })
  @ApiParam({
    name: 'customerId',
    description: 'The customer Id',
  })
  @ApiBody({ type: CustomerPropertySearchPayloadOpenApiSchema })
  async upsertCustomerPropertySearch(
    @Param('customerId') customerId: string,
    @Body() payload: CustomerPropertySearch
  ) {
    return this.CustomerService.upsertCustomerPropertySearch(
      customerId,
      CustomerPropertySearchSchema.parse(payload)
    );
  }
}
