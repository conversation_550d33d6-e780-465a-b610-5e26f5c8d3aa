import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { z } from 'zod';

import {
  avmSchema,
  coreLogicDisclaimerSchema,
  propertyImagerySchema,
  propertyZoneAndRisksSchema,
} from '@gp/data-access/corelogic';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { CoreLogicService } from './corelogic.service';

class PropertyImageryOpenApiSchema extends createOpenApiSchemaFromZod(
  'PropertyImagery',
  propertyImagerySchema,
  { description: 'The schema for property imagery' }
) {}

class PropertyZoneAndRisksOpenApiSchema extends createOpenApiSchemaFromZod(
  'PropertyZoneAndRisks',
  propertyZoneAndRisksSchema,
  { description: 'The schema for property zone and risks' }
) {}

class AVMOpenApiSchema extends createOpenApiSchemaFromZod('AVM', avmSchema, {
  description: 'The schema for an AVM',
}) {}

class AVMDisclaimerOpenApiSchema extends createOpenApiSchemaFromZod(
  'AVMDisclaimer',
  z.object({
    estimation: coreLogicDisclaimerSchema,
    confidence: coreLogicDisclaimerSchema,
  }),
  { description: 'The schema for the AVM disclaimer from CoreLogic' }
) {}

class CopyrightsDisclaimerOpenApiSchema extends createOpenApiSchemaFromZod(
  'CopyrightsDisclaimer',
  coreLogicDisclaimerSchema,
  { description: 'The schema for the copyrights disclaimer from CoreLogic' }
) {}

class DataDisclaimerOpenApiSchema extends createOpenApiSchemaFromZod(
  'DataDisclaimer',
  coreLogicDisclaimerSchema,
  { description: 'The schema for the data disclaimer from CoreLogic' }
) {}

class StatesDisclaimerOpenApiSchema extends createOpenApiSchemaFromZod(
  'StatesDisclaimer',
  z.object({
    nsw: coreLogicDisclaimerSchema,
    vic: coreLogicDisclaimerSchema,
    qld: coreLogicDisclaimerSchema,
    sa: coreLogicDisclaimerSchema,
    wa: coreLogicDisclaimerSchema,
    act: coreLogicDisclaimerSchema,
    tas: coreLogicDisclaimerSchema,
  }),
  { description: 'The schema for the states disclaimer from CoreLogic' }
) {}

@Controller('v1/property-hub')
export class CoreLogicController {
  constructor(private coreLogicService: CoreLogicService) {}

  @ApiBearerAuth()
  @ApiTags('CoreLogic')
  @Get('property/:corelogicId/images')
  @ApiOperation({ description: 'Get property images by the property CoreLogic id' })
  @ApiParam({
    name: 'corelogicId',
    description: 'The CoreLogic id for the property',
  })
  @ApiOkResponse({
    description: 'The property images',
    type: PropertyImageryOpenApiSchema,
  })
  async getPropertyImages(@Param('corelogicId') corelogicId: string) {
    return this.coreLogicService.getPropertyImages(corelogicId);
  }

  @ApiBearerAuth()
  @ApiTags('CoreLogic')
  @Get('property/:corelogicId/zone-and-risks')
  @ApiOperation({ description: 'Get property zone and risks by the property CoreLogic id' })
  @ApiParam({
    name: 'corelogicId',
    description: 'The CoreLogic id for the property',
  })
  @ApiOkResponse({
    description: 'The property zone and risks',
    type: PropertyZoneAndRisksOpenApiSchema,
  })
  async getPropertyZoneAndRisks(@Param('corelogicId') corelogicId: string) {
    return this.coreLogicService.getPropertyZoneAndRisks(corelogicId);
  }

  @ApiBearerAuth()
  @ApiTags('CoreLogic')
  @Get('property/:corelogicId/avm')
  @ApiOperation({ description: 'Get AVM by the property CoreLogic id' })
  @ApiParam({
    name: 'corelogicId',
    description: 'The CoreLogic id for the property',
  })
  @ApiQuery({
    name: 'days',
    description:
      'Maximum age of cached AVM data in days. If specified, only returns cached data updated within this many days ago. Defaults to 7, which means only use cache if updated within the last 7 days.',
    required: false,
  })
  @ApiOkResponse({ description: 'The property AVM from CoreLogic', type: AVMOpenApiSchema })
  async getAVM(
    @Param('corelogicId') corelogicId: string,
    @Query('days') days: string,
    @Req() request: Request
  ) {
    return this.coreLogicService.getAVM(corelogicId, parseInt(days), request);
  }

  @ApiTags('Disclaimer')
  @Get('disclaimers/avm')
  @ApiOperation({ description: 'Get the CoreLogic AVM disclaimers about estimate and confidence' })
  @ApiOkResponse({ description: 'The AVM disclaimers', type: AVMDisclaimerOpenApiSchema })
  async getAVMDisclaimers() {
    return this.coreLogicService.getAVMDisclaimers();
  }

  @ApiTags('Disclaimer')
  @Get('disclaimers/copyrights')
  @ApiOperation({ description: 'Get the CoreLogic copyrights disclaimer' })
  @ApiOkResponse({
    description: 'The copyrights disclaimer',
    type: CopyrightsDisclaimerOpenApiSchema,
  })
  async getDisclaimer() {
    return this.coreLogicService.getCopyrightsDisclaimer();
  }

  @ApiTags('Disclaimer')
  @Get('disclaimers/data')
  @ApiOperation({ description: 'Get the CoreLogic data disclaimer' })
  @ApiOkResponse({ description: 'The data disclaimer', type: DataDisclaimerOpenApiSchema })
  async getDataDisclaimer() {
    return this.coreLogicService.getDataDisclaimer();
  }

  @ApiTags('Disclaimer')
  @Get('disclaimers/states')
  @ApiOperation({ description: 'Get the CoreLogic states disclaimer' })
  @ApiOkResponse({ description: 'The states disclaimer', type: StatesDisclaimerOpenApiSchema })
  async getStateDisclaimers() {
    return this.coreLogicService.getStateDisclaimers();
  }
}
