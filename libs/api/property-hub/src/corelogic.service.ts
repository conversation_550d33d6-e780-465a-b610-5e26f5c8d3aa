import { Injectable } from '@nestjs/common';
import { Request } from 'express';

import {
  getAVMConfidenceDisclaimer,
  getAVMDisclaimer,
  getCLDisclaimer,
  getCLGeneralDisclaimer,
  getEstimatedPropertyRent,
  getEstimatedPropertyValue,
  getPropertyImages,
  getPropertyZoneAndRisks,
  getStateDisclaimer,
} from '@gp/data-access/corelogic';
import { getAVMCache, updateAVMCache } from '@gp/database/property-hub';

@Injectable()
export class CoreLogicService {
  async getAVM(corelogicId: string, days = 30, req: Request) {
    const cachedAVM = await getAVMCache(corelogicId, 30);
    if (cachedAVM) {
      return cachedAVM;
    }
    console.info(`getting non cached avm for ${corelogicId}`, {
      ...req,
      headers: {
        ...req.headers,
        authorization: req.headers.authorization ? '[REDACTED]' : '[NO AUTH]',
      },
    });
    const [estimatedValue, estimatedRent] = await Promise.all([
      getEstimatedPropertyValue(corelogicId),
      getEstimatedPropertyRent(corelogicId),
    ]);
    await updateAVMCache(corelogicId, { estimatedValue, estimatedRent });
    return {
      estimatedValue,
      estimatedRent,
    };
  }

  async getEstimatedPropertyValue(corelogicId: string) {
    return getEstimatedPropertyValue(corelogicId);
  }

  async getEstimatedPropertyRent(corelogicId: string) {
    return getEstimatedPropertyRent(corelogicId);
  }

  async getPropertyImages(corelogicId: string) {
    return getPropertyImages(corelogicId);
  }

  async getPropertyZoneAndRisks(corelogicId: string) {
    return getPropertyZoneAndRisks(corelogicId);
  }

  async getAVMDisclaimers() {
    const [estimation, confidence] = await Promise.all([
      getAVMDisclaimer(),
      getAVMConfidenceDisclaimer(),
    ]);
    return {
      estimation,
      confidence,
    };
  }

  async getCopyrightsDisclaimer() {
    return getCLDisclaimer();
  }

  async getDataDisclaimer() {
    return getCLGeneralDisclaimer();
  }

  async getStateDisclaimers() {
    const [nsw, vic, qld, sa, wa, act, tas] = await Promise.all([
      getStateDisclaimer('nsw'),
      getStateDisclaimer('vic'),
      getStateDisclaimer('qld'),
      getStateDisclaimer('sa'),
      getStateDisclaimer('wa'),
      getStateDisclaimer('act'),
      getStateDisclaimer('tas'),
    ]);

    return { nsw, vic, qld, sa, wa, act, tas };
  }
}
