import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { Brand } from '@lendi/lala-utils';

import { getLDSingleKindContext, getServerFlag } from '@gp/shared/server-launchdarkly';

import { AreaController } from './area.controller';
import { AreaService } from './area.service';
import { CoreLogicController } from './corelogic.controller';
import { CoreLogicService } from './corelogic.service';
import { CustomerController } from './customer.controller';
import { CustomerService } from './customer.service';
import { PropertyController } from './property.controller';
import { PropertyService } from './property.service';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';

@Module({
  controllers: [
    SearchController,
    CustomerController,
    CoreLogicController,
    PropertyController,
    AreaController,
  ],
  providers: [AreaService, CoreLogicService, CustomerService, PropertyService, SearchService],
  exports: [AreaService, CoreLogicService, CustomerService, PropertyService, SearchService],
})
export class PropertyHubModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(async (req: Request, res: Response, next: NextFunction) => {
        const targetId = req.cookies['targetId'];
        const authsession = req.cookies['authsession']; // sessionId,userId
        const [_, userId] = (authsession || '').split(',');

        const context = getLDSingleKindContext(Brand.Aussie, targetId, userId);
        const useNewPropertyTable = await getServerFlag('use-new-property-table', context, false);
        res.locals['useNewPropertyTable'] = useNewPropertyTable;
        next();
      })
      .forRoutes(SearchController, CustomerController, PropertyController, AreaController);
  }
}
