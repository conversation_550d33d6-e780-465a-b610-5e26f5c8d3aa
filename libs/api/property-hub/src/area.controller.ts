import { <PERSON>, Get, Param, Query, Res } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { z } from 'zod';

import {
  listedPropertySchema,
  LISTING_SEARCH_PROPERTY_TYPE,
  soldPropertySchema,
  SUGGESTION_TYPE,
} from '@gp/data-access/corelogic';
import { councilSchema, STATE, stateSchema, suburbSchema } from '@gp/data-access/property-hub';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { AreaService } from './area.service';

class StateOpenApiSchema extends createOpenApiSchemaFromZod('State', stateSchema, {
  description: 'The schema for a state in Australia',
}) {}

class AreaOpenApiSchema extends createOpenApiSchemaFromZod(
  'Area',
  z.union([councilSchema, suburbSchema]),
  {
    description: 'The schema for a council or suburb in Australia',
  }
) {}

class ListedPropertyOpenApiSchema extends createOpenApiSchemaFromZod(
  'ListedProperties',
  listedPropertySchema,
  { description: 'The schema for listed property' }
) {}

class SoldPropertyOpenApiSchema extends createOpenApiSchemaFromZod(
  'SoldProperties',
  soldPropertySchema,
  { description: 'The schema for sold property' }
) {}

@Controller('v1/property-hub')
export class AreaController {
  constructor(private areaService: AreaService) {}

  @ApiBearerAuth()
  @ApiTags('State, Council and Suburb')
  @Get(':state')
  @ApiOperation({ description: 'Get state profile by state abbreviation' })
  @ApiParam({
    name: 'state',
    description: 'The abbreviation for the state',
    enum: STATE,
  })
  @ApiOkResponse({ description: 'The found state', type: StateOpenApiSchema })
  @ApiNotFoundResponse({ description: 'State not found' })
  async getState(@Param('state') state: string) {
    return this.areaService.getState(state);
  }

  @ApiBearerAuth()
  @ApiTags('State, Council and Suburb')
  @Get(':state/:area')
  @ApiOperation({ description: 'Get council or suburb profile' })
  @ApiParam({
    name: 'state',
    description: 'The abbreviation for the state',
    enum: STATE,
    required: true,
  })
  @ApiParam({
    name: 'area',
    description: `The name of the area, which can be either a council or a suburb. If the name ends with "-council", it indicates a council; otherwise, it is a suburb.`,
    type: 'string',
    required: true,
  })
  @ApiOkResponse({ description: 'The found area', type: AreaOpenApiSchema })
  @ApiNotFoundResponse({ description: 'Council not found' })
  async getArea(@Param('state') state: string, @Param('area') area: string) {
    if (area.endsWith('-council')) {
      return this.areaService.getCouncil(`${state}/${area}`);
    }
    return this.areaService.getSuburb(`${state}/${area}`);
  }

  @ApiBearerAuth()
  @ApiTags('State, Council and Suburb')
  @Get(':state/:area/listed-properties')
  @ApiOperation({ description: 'Get listed properties' })
  @ApiParam({
    name: 'state',
    description: 'The abbreviation for the state',
    enum: STATE,
    required: true,
  })
  @ApiParam({
    name: 'area',
    description: `The name of the area, which can be either a council or a suburb. If the name ends with "-council", it indicates a council; otherwise, it is a suburb.`,
    type: 'string',
    required: true,
  })
  @ApiQuery({
    name: 'type',
    description: 'The type of property to filter by',
    required: false,
    enum: LISTING_SEARCH_PROPERTY_TYPE,
  })
  @ApiQuery({
    name: 'bedrooms',
    description: 'The number of bedrooms to filter by',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'bathrooms',
    description: 'The number of bathrooms to filter by',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'carSpaces',
    description: 'The number of car spaces to filter by',
    required: false,
    type: 'number',
  })
  @ApiOkResponse({
    description: 'The found listed properties',
    type: ListedPropertyOpenApiSchema,
    isArray: true,
  })
  @ApiNotFoundResponse({ description: 'Listed properties not found' })
  async getListedProperties(
    @Res() res: Response,
    @Param('state') state: string,
    @Param('area') area: string,
    @Query('type') type?: string,
    @Query('bedrooms') bedrooms?: number,
    @Query('bathrooms') bathrooms?: number,
    @Query('carSpaces') carSpaces?: number
  ) {
    const areaName = await this.areaService.getAreaName(`${state}/${area}`);
    return res.json(
      await this.areaService.getListedProperties(
        res.locals['useNewPropertyTable'],
        area.endsWith('-council') ? SUGGESTION_TYPE.COUNCIL : SUGGESTION_TYPE.SUBURB,
        areaName,
        { type, bedrooms, bathrooms, carSpaces }
      )
    );
  }

  @ApiBearerAuth()
  @ApiTags('State, Council and Suburb')
  @Get(':state/:area/sold-properties')
  @ApiOperation({ description: 'Get sold properties' })
  @ApiParam({
    name: 'state',
    description: 'The abbreviation for the state',
    enum: STATE,
    required: true,
  })
  @ApiParam({
    name: 'area',
    description: `The name of the area, which can be either a council or a suburb. If the name ends with "-council", it indicates a council; otherwise, it is a suburb.`,
    type: 'string',
    required: true,
  })
  @ApiQuery({
    name: 'type',
    description: 'The type of property to filter by',
    required: false,
    enum: LISTING_SEARCH_PROPERTY_TYPE,
  })
  @ApiQuery({
    name: 'bedrooms',
    description: 'The number of bedrooms to filter by',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'bathrooms',
    description: 'The number of bathrooms to filter by',
    required: false,
    type: 'number',
  })
  @ApiQuery({
    name: 'carSpaces',
    description: 'The number of car spaces to filter by',
    required: false,
    type: 'number',
  })
  @ApiOkResponse({
    description: 'The found sold properties',
    type: SoldPropertyOpenApiSchema,
    isArray: true,
  })
  @ApiNotFoundResponse({ description: 'Listed properties not found' })
  async getSoldProperties(
    @Res() res: Response,
    @Param('state') state: string,
    @Param('area') area: string,
    @Query('type') type?: string,
    @Query('bedrooms') bedrooms?: number,
    @Query('bathrooms') bathrooms?: number,
    @Query('carSpaces') carSpaces?: number
  ) {
    const areaName = await this.areaService.getAreaName(`${state}/${area}`);
    return res.json(
      await this.areaService.getSoldProperties(
        res.locals['useNewPropertyTable'],
        area.endsWith('-council') ? SUGGESTION_TYPE.COUNCIL : SUGGESTION_TYPE.SUBURB,
        areaName,
        { type, bedrooms, bathrooms, carSpaces }
      )
    );
  }
}
