import { Injectable } from '@nestjs/common';

import { getSuggestions, SUGGESTION_TYPE } from '@gp/data-access/corelogic';
import { type Property, SEARCH_RESULT_TYPE } from '@gp/data-access/property-hub';
import { getProperty, searchArea, setCoreLogicPropertiesWithSlug } from '@gp/database/property-hub';

@Injectable()
export class SearchService {
  async search(query: string, limit: number, types: SEARCH_RESULT_TYPE[], useNewProperty: boolean) {
    const isSearchingArea =
      types.includes(SEARCH_RESULT_TYPE.STATE) ||
      types.includes(SEARCH_RESULT_TYPE.COUNCIL) ||
      types.includes(SEARCH_RESULT_TYPE.SUBURB);
    const areas = isSearchingArea ? await searchArea(query, limit, types) : [];
    if (areas.length < limit && types.includes(SEARCH_RESULT_TYPE.ADDRESS)) {
      const suggestions = await getSuggestions(SUGGESTION_TYPE.ADDRESS, query, limit);
      const properties = await setCoreLogicPropertiesWithSlug(
        suggestions.map(({ suggestionId, suggestion }) => ({
          id: suggestionId,
          name: suggestion,
          type: SEARCH_RESULT_TYPE.ADDRESS,
        })),
        true,
        useNewProperty
      );

      return [...areas, ...properties].slice(0, limit);
    }
    return areas.slice(0, limit);
  }

  async searchProperties(query: string, useNewProperty: boolean) {
    const searchResults = await this.search(query, 5, [SEARCH_RESULT_TYPE.ADDRESS], useNewProperty);
    const properties: Property[] = [];
    await Promise.all(
      searchResults.map(async (result) => {
        const property = await getProperty(result.slug);
        if (property) {
          delete property.data.estimatedRent;
          delete property.data.estimatedValue;
          properties.push(property);
        }
      })
    );
    return properties;
  }
}
