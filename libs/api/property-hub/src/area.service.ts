import { Injectable, NotFoundException } from '@nestjs/common';

import { getListedProperties, getSoldProperties, SUGGESTION_TYPE } from '@gp/data-access/corelogic';
import {
  getAreaName,
  getCouncil,
  getState,
  getSuburb,
  getSuburbsByPostcode,
  setCoreLogicPropertiesWithSlug,
} from '@gp/database/property-hub';

@Injectable()
export class AreaService {
  async getState(slug: string) {
    const state = await getState(slug);
    if (!state) {
      throw new NotFoundException('State not found');
    }
    return state;
  }

  async getCouncil(slug: string) {
    const council = await getCouncil(slug);
    if (!council) {
      throw new NotFoundException('Council not found');
    }
    return council;
  }

  async getSuburb(slug: string) {
    const suburb = await getSuburb(slug);
    if (!suburb) {
      throw new NotFoundException('Suburb not found');
    }
    return suburb;
  }

  async getAreaName(slug: string) {
    const areaName = await getAreaName(slug);
    if (!areaName) {
      throw new NotFoundException('Area not found');
    }
    return areaName;
  }

  async getListedProperties(
    useNewProperty: boolean,
    areaType: SUGGESTION_TYPE.SUBURB | SUGGESTION_TYPE.COUNCIL,
    areaName: string,
    query?: {
      type?: string;
      bedrooms?: number;
      bathrooms?: number;
      carSpaces?: number;
    }
  ) {
    const properties = await getListedProperties(areaType, areaName, query, 20);
    return setCoreLogicPropertiesWithSlug(properties, undefined, useNewProperty);
  }

  async getSoldProperties(
    useNewProperty: boolean,
    areaType: SUGGESTION_TYPE.SUBURB | SUGGESTION_TYPE.COUNCIL,
    areaName: string,
    query?: {
      type?: string;
      bedrooms?: number;
      bathrooms?: number;
      carSpaces?: number;
    }
  ) {
    const properties = await getSoldProperties(areaType, areaName, query, 20);
    return setCoreLogicPropertiesWithSlug(properties, undefined, useNewProperty);
  }

  async getSuburbsByPostcode(postcode: string) {
    return getSuburbsByPostcode(postcode);
  }
}
