import { BadRequestException, Injectable } from '@nestjs/common';

import type { CustomerPropertyPayload, CustomerPropertySearch } from '@gp/data-access/property-hub';
import {
  deleteCustomerProperty,
  getCustomerProperties,
  getCustomerPropertyRelations,
  getCustomerPropertySearches,
  upsertCustomerProperty,
  upsertCustomerPropertySearch,
} from '@gp/database/property-hub';

@Injectable()
export class CustomerService {
  async getCustomerProperties(customerId: string, useNewProperty: boolean) {
    return getCustomerProperties(customerId, useNewProperty);
  }

  async getCustomerPropertyRelations(customerId: string, useNewProperty: boolean) {
    return getCustomerPropertyRelations(customerId, useNewProperty);
  }

  async upsertCustomerProperty(
    customerId: string,
    payload: CustomerPropertyPayload,
    useNewProperty: boolean
  ) {
    const { propertyId, relationship } = payload;
    return upsertCustomerProperty(customerId, propertyId, relationship, useNewProperty);
  }

  async deleteCustomerProperty(customerId: string, propertyId: string, useNewProperty: boolean) {
    return deleteCustomerProperty(customerId, propertyId, useNewProperty);
  }

  async getCustomerPropertySearches(customerId: string) {
    return getCustomerPropertySearches(customerId);
  }

  async upsertCustomerPropertySearch(customerId: string, payload: CustomerPropertySearch) {
    const { queryString, label, searchType, searchCategory, updatedAt, createdAt } = payload;
    if (!queryString || !label) {
      throw new BadRequestException(
        'queryString and label must be provided when upserting customer property search criteria'
      );
    }
    return upsertCustomerPropertySearch(
      customerId,
      queryString,
      label,
      searchType,
      searchCategory,
      updatedAt,
      createdAt
    );
  }
}
