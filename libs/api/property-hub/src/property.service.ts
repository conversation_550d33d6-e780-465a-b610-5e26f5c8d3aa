import { Injectable } from '@nestjs/common';
import { Request } from 'express';

import {
  getPropertyImages,
  getPropertyOTMStatus,
  getPropertyZoneAndRisks,
  LISTING_SEARCH_PROPERTY_TYPE,
} from '@gp/data-access/corelogic';
import { getDomainServiceAPIs } from '@gp/data-access/domain-service';
import {
  CUSTOMER_PROPERTY_RELATIONSHIP,
  LISTING_TYPE,
  propertiesForSaleSearchResultSchema,
  type Property,
  PROPERTY_TYPE_KAFKA,
  PROPERTY_TYPE_LABEL_MAPPING,
  propertySchema,
} from '@gp/data-access/property-hub';
import {
  getCustomerPropertyRelations,
  getPropertiesByIds,
  getSuburb,
  getSurroundingSuburbs,
  searchPropertiesForSale,
} from '@gp/database/property-hub';

import { CoreLogicService } from './corelogic.service';

@Injectable()
export class PropertyService {
  constructor(private coreLogicService: CoreLogicService) {}

  async getPropertiesByIds(
    ids: string[],
    useNewProperty: boolean,
    req: Request
  ): Promise<Property[]> {
    const properties = await getPropertiesByIds(ids, useNewProperty);
    return Promise.all(
      properties.map(async ({ data, corelogicId, ...rest }) => {
        const [otmStatus, { photos, floorPlans }, zoneAndRisks, { estimatedValue, estimatedRent }] =
          await Promise.all([
            getPropertyOTMStatus(corelogicId),
            getPropertyImages(corelogicId),
            getPropertyZoneAndRisks(corelogicId),
            corelogicId
              ? this.coreLogicService.getAVM(corelogicId, 7, req)
              : Promise.resolve({ estimatedValue: undefined, estimatedRent: undefined }),
          ]);
        return {
          ...rest,
          corelogicId,
          data: {
            ...data,
            ...(otmStatus.listed
              ? {
                  forSale: true,
                  listingType: LISTING_TYPE.ON_MARKET,
                  listedDate: otmStatus.listedDate,
                  auctionDateTime: otmStatus.auctionDatetime,
                  listingDescription: otmStatus.priceDescription,
                  realEstateAgents: [
                    {
                      name: otmStatus.agent,
                      phone: otmStatus.contact,
                      agency: {
                        name: otmStatus.agency,
                      },
                    },
                  ],
                }
              : {
                  forSale: false,
                  listingType: undefined,
                  listedDate: undefined,
                  auctionDateTime: undefined,
                  listingDescription: undefined,
                  realEstateAgents: undefined,
                  inspectionDateTimes: undefined,
                }),
            photos,
            floorPlans,
            zoneAndRisks,
            estimatedValue,
            estimatedRent,
          },
        };
      })
    );
  }

  async searchPublicPropertiesForSale(
    areas: string[],
    includeSurroundingSuburbs?: boolean,
    page?: number,
    limit?: number,
    types?: LISTING_SEARCH_PROPERTY_TYPE[],
    exclusiveListingOnly?: boolean,
    minPrice?: number,
    maxPrice?: number,
    minBedrooms?: number,
    maxBedrooms?: number,
    minBathrooms?: number,
    maxBathrooms?: number,
    minCarSpaces?: number,
    maxCarSpaces?: number,
    minLandSize?: number,
    maxLandSize?: number
  ) {
    let queryAreas = areas;
    if (includeSurroundingSuburbs) {
      queryAreas = await getSurroundingSuburbs(areas);
    }
    const { getPublicPropertiesForSale } = getDomainServiceAPIs();

    const res = await getPublicPropertiesForSale(
      queryAreas,
      {
        types,
        exclusiveListingOnly,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
        minBathrooms,
        maxBathrooms,
        minCarSpaces,
        maxCarSpaces,
        minLandSize,
        maxLandSize,
      },
      ((page || 1) - 1) * (limit || 24),
      limit
    );
    return propertiesForSaleSearchResultSchema.parse({
      totalPage: res.data.allCampaigns?.pagination?.totalPages || 1,
      total:
        res.data.allCampaigns?.pagination?.totalCount || res.data.allCampaigns.Campaigns.length,
      propertyList: res.data.allCampaigns.Campaigns.map((p) => {
        const photos = (p.photoUrls || [])
          .map((obj: { id: string; type: string; url: string }) => obj.url)
          .filter((p: string | undefined) => p);
        const floorPlanUrls = (p.floorPlanUrls || [])
          .map((obj: { id: string; type: string; url: string }) => obj.url)
          .filter((p: string | undefined) => p);
        return {
          id: p.propertyId || p.campaignId,
          slug: p.slug,
          data: {
            type: PROPERTY_TYPE_LABEL_MAPPING[p.propertyType as PROPERTY_TYPE_KAFKA],
            forSale: p.campaignType === 'SALE',
            listingType: p.campaignMarketType,
            campaignId: p.campaignId,
            bedrooms: p.bedrooms || 0,
            bathrooms: p.bathrooms || 0,
            carSpaces: p.carSpaces || 0,
            floorArea: p.floorArea || 0,
            landArea: p.landArea || 0,
            photo: p.heroPhotoUrl || p.imageUrl || null,
            photos: photos.length > 0 ? photos : null,
            primaryAddress: p.primaryAddress || p.address,
            otherAddresses: p.address ? [p.address] : null,
            subType: p.propertySubType,
            builtYear: p.builtYear,
            floorPlans: floorPlanUrls.length > 0 ? floorPlanUrls : null,
            listingLoopId: p.listingLoopId,
            listingDescription: p.priceDescription,
            realEstateAgents: p.realEstateAgents,
            propertyDescription: p.campaignDescription,
            priceGuide:
              p.priceGuideMin && p.priceGuideMax ? [p.priceGuideMin, p.priceGuideMax] : null,
            listedDate: p.firstListedDate,
            inspectionDateTimes: p.inspectionDatetimes,
            auctionDateTime: p.auctionDateTime,
            informationStatement: p.informationStatement,
            state: p.state,
            postcode: p.postcode,
            suburb: p.suburbText,
          },
        };
      }),
    });
  }

  async getPublicPropertyByCampaignId(id: string) {
    const { getPublicPropertyById } = getDomainServiceAPIs();

    const res = await getPublicPropertyById(id);
    const rowData = res.data.getCampaign.Campaign;
    const suburbSlug = rowData.suburbSlug;
    const suburb = await getSuburb(suburbSlug);
    const photos = (rowData.photoUrls || [])
      .map((obj: { id: string; type: string; url: string }) => obj.url)
      .filter((p: string | undefined) => p);
    const floorPlanUrls = (rowData.floorPlanUrls || [])
      .map((obj: { id: string; type: string; url: string }) => obj.url)
      .filter((p: string | undefined) => p);
    return propertySchema.parse({
      id: rowData.id,
      slug: rowData.slug,
      address: rowData.address || rowData.primaryAddress,
      location:
        !!rowData.latitude && !!rowData.longitude ? [rowData.latitude, rowData.longitude] : null,
      listed: true,
      listedDate: rowData.firstListedDate || null,
      suburb: suburb,
      data: {
        type: PROPERTY_TYPE_LABEL_MAPPING[rowData.propertyType as PROPERTY_TYPE_KAFKA],
        forSale: rowData.campaignType === 'SALE',
        listingType: rowData.campaignMarketType,
        campaignId: rowData.campaignId,
        bedrooms: rowData.bedrooms || 0,
        bathrooms: rowData.bathrooms || 0,
        carSpaces: rowData.carSpaces || 0,
        floorArea: rowData.floorArea || 0,
        landArea: rowData.landArea || 0,
        photo: rowData.heroPhotoUrl || rowData.imageUrl || null,
        photos: photos.length > 0 ? photos : null,
        primaryAddress: rowData.primaryAddress || rowData.address,
        otherAddresses: rowData.address ? [rowData.address] : null,
        subType: rowData.propertySubType,
        builtYear: rowData.builtYear,
        floorPlans: floorPlanUrls.length > 0 ? floorPlanUrls : null,
        listingLoopId: rowData.listingLoopId,
        listingDescription: rowData.priceDescription,
        realEstateAgents: rowData.realEstateAgents,
        propertyDescription: rowData.campaignDescription,
        priceGuide:
          rowData.priceGuideMin && rowData.priceGuideMax
            ? [rowData.priceGuideMin, rowData.priceGuideMax]
            : null,
        listedDate: rowData.firstListedDate,
        inspectionDateTimes: rowData.inspectionDatetimes,
        auctionDateTime: rowData.auctionDateTime,
        informationStatement: rowData.informationStatement,
      },
    });
  }

  async searchPropertiesForSale(
    useNewProperty: boolean,
    areas: string[],
    customerId?: string,
    includeSurroundingSuburbs?: boolean,
    page?: number,
    limit?: number,
    types?: LISTING_SEARCH_PROPERTY_TYPE[],
    exclusiveListingOnly?: boolean,
    minPrice?: number,
    maxPrice?: number,
    minBedrooms?: number,
    maxBedrooms?: number,
    minBathrooms?: number,
    maxBathrooms?: number,
    minCarSpaces?: number,
    maxCarSpaces?: number,
    minLandSize?: number,
    maxLandSize?: number
  ) {
    const { total, propertyList } = await searchPropertiesForSale(
      areas,
      useNewProperty,
      includeSurroundingSuburbs,
      {
        types,
        exclusiveListingOnly,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
        minBathrooms,
        maxBathrooms,
        minCarSpaces,
        maxCarSpaces,
        minLandSize,
        maxLandSize,
      },
      page,
      limit
    );

    const customerProperties = customerId
      ? await getCustomerPropertyRelations(customerId, useNewProperty)
      : [];

    return propertiesForSaleSearchResultSchema.parse({
      total,
      propertyList: propertyList.map(({ id, corelogicId, slug, data }) => {
        /**
         * If the property is not listed on the market and  the customer doesn't
         * have a relationship with the property after the property is listed,
         * then mask some information for the property.
         */
        const shouldMask =
          data.listingType !== LISTING_TYPE.ON_MARKET &&
          !customerProperties.find(
            ({ propertyId, relationship, updatedAt }) =>
              propertyId === id &&
              relationship === CUSTOMER_PROPERTY_RELATIONSHIP.LOOKING_TO_BUY &&
              (!data.listedDate || (updatedAt && updatedAt > new Date(data.listedDate)))
          );

        return { id, corelogicId, slug, data, shouldMask };
      }),
    });
  }
}
