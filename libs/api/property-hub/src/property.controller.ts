import {
  Controller,
  Get,
  ParseArrayPipe,
  ParseBoolPipe,
  ParseIntPipe,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';

import { LISTING_SEARCH_PROPERTY_TYPE } from '@gp/data-access/corelogic';
import { propertiesForSaleSearchResultSchema, propertySchema } from '@gp/data-access/property-hub';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';
import { jwtDecode } from '@gp/util/session';

import { PropertyService } from './property.service';

class PropertiesForSaleSearchResultOpenApiSchema extends createOpenApiSchemaFromZod(
  'PropertiesForSaleSearchResult',
  propertiesForSaleSearchResultSchema,
  {
    description:
      'The schema for a search result which has a total number of properties for sale and a list of (max 20) properties data for sale',
  }
) {}

class PropertyOpenApiSchema extends createOpenApiSchemaFromZod('Property', propertySchema, {
  description: 'The schema for a property',
}) {}

@Controller('v1/property-hub')
export class PropertyController {
  constructor(private PropertyService: PropertyService) {}

  @ApiBearerAuth()
  @ApiTags('Properties')
  @Get('properties')
  @ApiOperation({ description: 'Get properties by property ids' })
  @ApiQuery({
    name: 'ids',
    description: 'uuids of property',
  })
  @ApiQuery({
    name: 'useV1',
    description: 'Override useNewPropertyTable with this boolean query param',
    required: false,
    type: 'boolean',
  })
  @ApiOkResponse({
    description: 'A list of property data including AVM, listing status, zone and risks and photos',
    type: PropertyOpenApiSchema,
    isArray: true,
  })
  async getPropertiesByIds(
    @Query('ids', new ParseArrayPipe({ items: String, separator: ',' })) ids: string[],
    @Query('useV1', new ParseBoolPipe({ optional: true })) useV1: boolean,
    @Req() request: Request,
    @Res() res: Response
  ) {
    let useNewProperty: boolean;
    if (useV1 !== undefined) {
      useNewProperty = !useV1;
    } else {
      useNewProperty = !!res.locals['useNewPropertyTable'];
    }
    return res.json(await this.PropertyService.getPropertiesByIds(ids, useNewProperty, request));
  }

  @ApiBearerAuth()
  @ApiTags('Properties')
  @ApiOperation({ description: 'Get properties for sale by property filters' })
  @ApiQuery({
    name: 'areas',
    description: 'The list of suburb slugs.',
  })
  @ApiQuery({
    name: 'types',
    description:
      'Array of property types: House | Unit | Townhouse | Apartment | Villa | Retirement living | Land | Acreage | Rural | Block of units | Other',
    required: false,
    enum: LISTING_SEARCH_PROPERTY_TYPE,
    isArray: true,
  })
  @ApiQuery({
    name: 'exclusiveListingOnly',
    required: false,
    description:
      'Boolean for indicating whether search properties for exclusive listing (pre-market and off-market) only',
  })
  @ApiQuery({
    name: 'includeSurroundingSuburbs',
    required: false,
    description: 'Boolean for indicating whether search properties for surrounding areas',
  })
  @ApiQuery({
    name: 'minPrice',
    description: 'Min amount of property price filter',
    required: false,
  })
  @ApiQuery({
    name: 'maxPrice',
    description: 'Max amount of property price filter',
    required: false,
  })
  @ApiQuery({
    name: 'minBedrooms',
    description: 'Number of minimum Bedrooms',
    required: false,
  })
  @ApiQuery({
    name: 'maxBedrooms',
    description: 'Number of maximum Bedrooms',
    required: false,
  })
  @ApiQuery({
    name: 'minBathrooms',
    description: 'Number of minimum Bathrooms',
    required: false,
  })
  @ApiQuery({
    name: 'maxBathrooms',
    description: 'Number of maximum Bathrooms',
    required: false,
  })
  @ApiQuery({
    name: 'minCarSpaces',
    description: 'Number of minimum CarSpaces',
    required: false,
  })
  @ApiQuery({
    name: 'maxCarSpaces',
    description: 'Number of maximum CarSpaces',
    required: false,
  })
  @ApiQuery({
    name: 'minLandSize',
    description: 'Min Number of landsize filter',
    required: false,
  })
  @ApiQuery({
    name: 'maxLandSize',
    description: 'Max Number of landsize filter',
    required: false,
  })
  @ApiQuery({
    name: 'page',
    description: 'Number of current page',
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page, 20 by default',
    required: false,
  })
  @ApiOkResponse({
    description: 'Search results',
    type: PropertiesForSaleSearchResultOpenApiSchema,
    isArray: false,
  })
  @Get('properties/for-sale')
  async getPropertiesForSale(
    @Req() req: Request,
    @Res() res: Response,
    @Query('areas', new ParseArrayPipe({ items: String, separator: ',' })) areas: string[],
    @Query('includeSurroundingSuburbs', new ParseBoolPipe({ optional: true }))
    includeSurroundingSuburbs?: boolean,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('types', new ParseArrayPipe({ items: String, optional: true }))
    types?: LISTING_SEARCH_PROPERTY_TYPE[],
    @Query('exclusiveListingOnly', new ParseBoolPipe({ optional: true }))
    exclusiveListingOnly?: boolean,
    @Query('minPrice', new ParseIntPipe({ optional: true })) minPrice?: number,
    @Query('maxPrice', new ParseIntPipe({ optional: true })) maxPrice?: number,
    @Query('minBedrooms', new ParseIntPipe({ optional: true })) minBedrooms?: number,
    @Query('maxBedrooms', new ParseIntPipe({ optional: true })) maxBedrooms?: number,
    @Query('minBathrooms', new ParseIntPipe({ optional: true })) minBathrooms?: number,
    @Query('maxBathrooms', new ParseIntPipe({ optional: true })) maxBathrooms?: number,
    @Query('minCarSpaces', new ParseIntPipe({ optional: true })) minCarSpaces?: number,
    @Query('maxCarSpaces', new ParseIntPipe({ optional: true })) maxCarSpaces?: number,
    @Query('minLandSize', new ParseIntPipe({ optional: true })) minLandSize?: number,
    @Query('maxLandSize', new ParseIntPipe({ optional: true })) maxLandSize?: number
  ) {
    const { customerId } = jwtDecode(req.headers.authorization);

    return res.json(
      await this.PropertyService.searchPropertiesForSale(
        res.locals['useNewPropertyTable'],
        areas,
        customerId,
        includeSurroundingSuburbs,
        page,
        limit,
        types,
        exclusiveListingOnly,
        minPrice,
        maxPrice,
        minBedrooms,
        maxBedrooms,
        minBathrooms,
        maxBathrooms,
        minCarSpaces,
        maxCarSpaces,
        minLandSize,
        maxLandSize
      )
    );
  }

  @ApiTags('public', 'Properties')
  @ApiOperation({ description: 'Get properties for sale by property filters' })
  @ApiQuery({
    name: 'areas',
    description: 'The list of suburb slugs.',
  })
  @ApiQuery({
    name: 'types',
    description:
      'Array of property types: House | Unit | Townhouse | Apartment | Villa | Retirement living | Land | Acreage | Rural | Block of units | Other',
    required: false,
    enum: LISTING_SEARCH_PROPERTY_TYPE,
    isArray: true,
  })
  @ApiQuery({
    name: 'exclusiveListingOnly',
    required: false,
    description:
      'Boolean for indicating whether search properties for exclusive listing (pre-market and off-market) only',
  })
  @ApiQuery({
    name: 'includeSurroundingSuburbs',
    required: false,
    description: 'Boolean for indicating whether search properties for surrounding areas',
  })
  @ApiQuery({
    name: 'minPrice',
    description: 'Min amount of property price filter',
    required: false,
  })
  @ApiQuery({
    name: 'maxPrice',
    description: 'Max amount of property price filter',
    required: false,
  })
  @ApiQuery({
    name: 'minBedrooms',
    description: 'Number of minimum Bedrooms',
    required: false,
  })
  @ApiQuery({
    name: 'maxBedrooms',
    description: 'Number of maximum Bedrooms',
    required: false,
  })
  @ApiQuery({
    name: 'minBathrooms',
    description: 'Number of minimum Bathrooms',
    required: false,
  })
  @ApiQuery({
    name: 'maxBathrooms',
    description: 'Number of maximum Bathrooms',
    required: false,
  })
  @ApiQuery({
    name: 'minCarSpaces',
    description: 'Number of minimum CarSpaces',
    required: false,
  })
  @ApiQuery({
    name: 'maxCarSpaces',
    description: 'Number of maximum CarSpaces',
    required: false,
  })
  @ApiQuery({
    name: 'minLandSize',
    description: 'Min Number of landsize filter',
    required: false,
  })
  @ApiQuery({
    name: 'maxLandSize',
    description: 'Max Number of landsize filter',
    required: false,
  })
  @ApiQuery({
    name: 'page',
    description: 'Number of current page',
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page, 20 by default',
    required: false,
  })
  @ApiOkResponse({
    description: 'Search results',
    type: PropertiesForSaleSearchResultOpenApiSchema,
    isArray: false,
  })
  @Get('public/properties/for-sale')
  async getPublicPropertiesForSale(
    @Query('areas', new ParseArrayPipe({ items: String, separator: ',' })) areas: string[],
    @Query('includeSurroundingSuburbs', new ParseBoolPipe({ optional: true }))
    includeSurroundingSuburbs?: boolean,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('types', new ParseArrayPipe({ items: String, optional: true }))
    types?: LISTING_SEARCH_PROPERTY_TYPE[],
    @Query('exclusiveListingOnly', new ParseBoolPipe({ optional: true }))
    exclusiveListingOnly?: boolean,
    @Query('minPrice', new ParseIntPipe({ optional: true })) minPrice?: number,
    @Query('maxPrice', new ParseIntPipe({ optional: true })) maxPrice?: number,
    @Query('minBedrooms', new ParseIntPipe({ optional: true })) minBedrooms?: number,
    @Query('maxBedrooms', new ParseIntPipe({ optional: true })) maxBedrooms?: number,
    @Query('minBathrooms', new ParseIntPipe({ optional: true })) minBathrooms?: number,
    @Query('maxBathrooms', new ParseIntPipe({ optional: true })) maxBathrooms?: number,
    @Query('minCarSpaces', new ParseIntPipe({ optional: true })) minCarSpaces?: number,
    @Query('maxCarSpaces', new ParseIntPipe({ optional: true })) maxCarSpaces?: number,
    @Query('minLandSize', new ParseIntPipe({ optional: true })) minLandSize?: number,
    @Query('maxLandSize', new ParseIntPipe({ optional: true })) maxLandSize?: number
  ) {
    return await this.PropertyService.searchPublicPropertiesForSale(
      areas,
      includeSurroundingSuburbs,
      page,
      limit,
      types,
      exclusiveListingOnly,
      minPrice,
      maxPrice,
      minBedrooms,
      maxBedrooms,
      minBathrooms,
      maxBathrooms,
      minCarSpaces,
      maxCarSpaces,
      minLandSize,
      maxLandSize
    );
  }

  @ApiTags('public', 'Properties')
  @ApiOperation({ description: 'Get public property by campaign id' })
  @ApiQuery({
    name: 'id',
    description: 'The id of campaign format as uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Search results',
    type: PropertiesForSaleSearchResultOpenApiSchema,
    isArray: false,
  })
  @Get('public/listing/for-sale')
  async getPublicPropertyByCampaignId(@Query('id') id: string) {
    return await this.PropertyService.getPublicPropertyByCampaignId(id);
  }
}
