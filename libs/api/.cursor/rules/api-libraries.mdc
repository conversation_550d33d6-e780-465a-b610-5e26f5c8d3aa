# API Libraries Guidelines

## Overview
API libraries contain NestJS controllers, services, and modules that define REST API endpoints for various business domains. These libraries follow NestJS patterns and provide typed APIs for frontend consumption.

## Import Pattern
```typescript
import { ServiceName, ControllerName } from '@gp/api/domain-name';
```

## Available API Libraries

### 1. Brokers API (`@gp/api/brokers`)
Manages broker-related operations and data.

**Exports:**
- `BrokersController`: REST endpoints for broker operations
- `BrokersModule`: NestJS module configuration
- `BrokersService`: Business logic for broker operations

**Usage:**
```typescript
import { BrokersController, BrokersService, BrokersModule } from '@gp/api/brokers';

// In app.module.ts
@Module({
  imports: [BrokersModule],
})
export class AppModule {}

// Using the service
@Injectable()
export class SomeService {
  constructor(private brokersService: BrokersService) {}
  
  async getBrokerData(id: string) {
    return this.brokersService.findById(id);
  }
}
```

### 2. Property Hub API (`@gp/api/property-hub`)
Comprehensive property-related API endpoints and services.

**Exports:**
- `PropertyHubModule`: Main module
- `AreaController` & `AreaService`: Area/location operations
- `CopilotKitController`: AI copilot integration
- `CorelogicController` & `CorelogicService`: Corelogic integration
- `CustomerController` & `CustomerService`: Customer operations
- `PropertyController` & `PropertyService`: Property operations
- `SearchController` & `SearchService`: Search functionality

**Usage:**
```typescript
import { 
  PropertyHubModule,
  PropertyController,
  PropertyService,
  SearchService 
} from '@gp/api/property-hub';

// Property operations
@Controller('properties')
export class MyPropertyController {
  constructor(
    private propertyService: PropertyService,
    private searchService: SearchService
  ) {}
  
  @Get('search')
  async searchProperties(@Query() query: SearchQuery) {
    return this.searchService.searchProperties(query);
  }
}
```

### 3. Stores API (`@gp/api/stores`)
Store location and information management.

**Exports:**
- `StoresController`: Store endpoints
- `StoresModule`: Module configuration
- `StoresService`: Store business logic

**Usage:**
```typescript
import { StoresController, StoresService, StoresModule } from '@gp/api/stores';

// Find nearby stores
@Injectable()
export class LocationService {
  constructor(private storesService: StoresService) {}
  
  async findNearbyStores(lat: number, lng: number) {
    return this.storesService.findNearby(lat, lng);
  }
}
```

## API Development Patterns

### 1. Controller Structure
```typescript
@Controller('resource-name')
export class ResourceController {
  constructor(private resourceService: ResourceService) {}
  
  @Get()
  async findAll(@Query() query: FindAllQuery) {
    return this.resourceService.findAll(query);
  }
  
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.resourceService.findById(id);
  }
  
  @Post()
  async create(@Body() createDto: CreateResourceDto) {
    return this.resourceService.create(createDto);
  }
  
  @Put(':id')
  async update(@Param('id') id: string, @Body() updateDto: UpdateResourceDto) {
    return this.resourceService.update(id, updateDto);
  }
  
  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.resourceService.remove(id);
  }
}
```

### 2. Service Structure
```typescript
@Injectable()
export class ResourceService {
  constructor(
    private dataAccessService: ResourceDataAccess,
    private logger: Logger
  ) {}
  
  async findAll(query: FindAllQuery): Promise<Resource[]> {
    try {
      return await this.dataAccessService.findAll(query);
    } catch (error) {
      this.logger.error('Failed to fetch resources', error);
      throw new InternalServerErrorException('Failed to fetch resources');
    }
  }
  
  async findById(id: string): Promise<Resource> {
    const resource = await this.dataAccessService.findById(id);
    if (!resource) {
      throw new NotFoundException(`Resource with ID ${id} not found`);
    }
    return resource;
  }
}
```

### 3. Module Structure
```typescript
@Module({
  imports: [
    DataAccessModule, // Import required data access modules
    LoggingModule,
  ],
  controllers: [ResourceController],
  providers: [ResourceService],
  exports: [ResourceService], // Export services for use in other modules
})
export class ResourceModule {}
```

## Best Practices

### 1. Error Handling
```typescript
// Use appropriate HTTP exceptions
throw new NotFoundException('Resource not found');
throw new BadRequestException('Invalid input data');
throw new UnauthorizedException('Access denied');
throw new InternalServerErrorException('Server error');
```

### 2. Validation
```typescript
// Use DTOs with validation decorators
export class CreateResourceDto {
  @IsString()
  @IsNotEmpty()
  name: string;
  
  @IsEmail()
  email: string;
  
  @IsOptional()
  @IsString()
  description?: string;
}
```

### 3. Documentation
```typescript
// Use Swagger decorators
@ApiTags('resources')
@Controller('resources')
export class ResourceController {
  @ApiOperation({ summary: 'Get all resources' })
  @ApiResponse({ status: 200, description: 'Resources retrieved successfully' })
  @Get()
  async findAll() {
    // Implementation
  }
}
```

### 4. Authentication & Authorization
```typescript
// Protect endpoints with guards
@UseGuards(JwtAuthGuard)
@Controller('protected-resource')
export class ProtectedController {
  @UseGuards(RoleGuard)
  @Roles('admin')
  @Delete(':id')
  async adminDelete(@Param('id') id: string) {
    // Only admins can delete
  }
}
```

## Integration Guidelines

### 1. With Data Access Libraries
```typescript
// API libraries should use data-access libraries for data operations
import { BrokerDataAccess } from '@gp/data-access/broker';

@Injectable()
export class BrokersService {
  constructor(private brokerDataAccess: BrokerDataAccess) {}
  
  async findBrokers(query: BrokerQuery) {
    return this.brokerDataAccess.findBrokers(query);
  }
}
```

### 2. With Database Libraries
```typescript
// Use database libraries for direct DB operations when needed
import { PropertyHubDatabase } from '@gp/database/property-hub';

@Injectable()
export class PropertyService {
  constructor(private propertyDb: PropertyHubDatabase) {}
  
  async complexQuery() {
    return this.propertyDb.executeRawQuery(sql);
  }
}
```

### 3. With Utility Libraries
```typescript
// Leverage utility libraries for common operations
import { Logger } from '@gp/util/logging';
import { formatCurrency } from '@gp/util/intl';

@Injectable()
export class CalculationService {
  constructor(private logger: Logger) {}
  
  calculateLoan(amount: number): string {
    this.logger.info('Calculating loan amount', { amount });
    return formatCurrency(amount);
  }
}
```

## Testing Patterns

### 1. Controller Testing
```typescript
describe('ResourceController', () => {
  let controller: ResourceController;
  let service: ResourceService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [ResourceController],
      providers: [
        {
          provide: ResourceService,
          useValue: {
            findAll: jest.fn(),
            findById: jest.fn(),
          },
        },
      ],
    }).compile();
    
    controller = module.get<ResourceController>(ResourceController);
    service = module.get<ResourceService>(ResourceService);
  });
  
  it('should return all resources', async () => {
    const resources = [{ id: '1', name: 'Test' }];
    jest.spyOn(service, 'findAll').mockResolvedValue(resources);
    
    expect(await controller.findAll()).toBe(resources);
  });
});
```

### 2. Service Testing
```typescript
describe('ResourceService', () => {
  let service: ResourceService;
  let dataAccess: ResourceDataAccess;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ResourceService,
        {
          provide: ResourceDataAccess,
          useValue: {
            findAll: jest.fn(),
            findById: jest.fn(),
          },
        },
      ],
    }).compile();
    
    service = module.get<ResourceService>(ResourceService);
    dataAccess = module.get<ResourceDataAccess>(ResourceDataAccess);
  });
  
  it('should throw NotFoundException when resource not found', async () => {
    jest.spyOn(dataAccess, 'findById').mockResolvedValue(null);
    
    await expect(service.findById('invalid-id')).rejects.toThrow(NotFoundException);
  });
});
```

## Common Use Cases

### 1. CRUD Operations
Use API libraries when you need to:
- Create REST endpoints for resources
- Handle HTTP requests and responses
- Validate request data
- Implement business logic
- Integrate with data access layers

### 2. External Integrations
Use API libraries for:
- Third-party service integrations (Corelogic, etc.)
- Webhook endpoints
- API gateway patterns
- Service-to-service communication

### 3. Complex Business Logic
Implement in API services:
- Multi-step business processes
- Data transformation and aggregation
- Complex validation rules
- Business rule enforcement

## Performance Considerations

### 1. Caching
```typescript
@Injectable()
export class CachedService {
  @Cacheable({ ttl: 300 }) // 5 minutes
  async getExpensiveData(id: string) {
    return this.dataAccess.complexQuery(id);
  }
}
```

### 2. Pagination
```typescript
@Get()
async findAll(@Query() query: PaginationQuery) {
  const { page = 1, limit = 10 } = query;
  return this.service.findAllPaginated(page, limit);
}
```

### 3. Async Operations
```typescript
@Post('bulk-process')
async bulkProcess(@Body() items: Item[]) {
  // Process in background
  this.queueService.add('bulk-process', items);
  return { message: 'Processing started' };
}
```