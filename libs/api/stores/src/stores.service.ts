import { Injectable, NotFoundException } from '@nestjs/common';

import { AreaService } from '@gp/api/property-hub';
import {
  getAllStores,
  getStoreById,
  getStoreBySlug,
  getStoresBySuburb,
} from '@gp/data-access/store';

@Injectable()
export class StoresService {
  constructor(private areaService: AreaService) {}

  async getAllStores() {
    return getAllStores();
  }

  async getStoreById(id: string) {
    const store = getStoreById(id);
    if (!store) {
      throw new NotFoundException(`Store with id ${id} not found`);
    }
    return store;
  }

  async getStoreBySlug(slug: string) {
    const store = getStoreBySlug(slug);
    if (!store) {
      throw new NotFoundException(`Store with slug ${slug} not found`);
    }
    return store;
  }

  async getStoresByPostcode(postcode: string) {
    const suburbs = await this.areaService.getSuburbsByPostcode(postcode);
    return await getStoresBySuburb(
      suburbs.map(({ name, postcode }) => `${name} ${postcode}`).join(',')
    );
  }

  async getStoresBySuburb(suburb: string) {
    return getStoresBySuburb(suburb);
  }
}
