import { BadRequestException, Controller, Get, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';

import { basicStoreSchema, storeSchema } from '@gp/data-access/store';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { StoresService } from './stores.service';

class BasicStoreOpenApiSchema extends createOpenApiSchemaFromZod(
  'StoreCollection',
  basicStoreSchema,
  { description: 'The schema for a store with basic information' }
) {}

class StoreOpenApiSchema extends createOpenApiSchemaFromZod('Store', storeSchema, {
  description: 'The schema for a store',
}) {}

@ApiTags('Stores')
@Controller('v1/aussie-stores')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Get()
  @ApiOperation({ description: 'Get stores according to the specified filters' })
  @ApiQuery({
    name: 'postcode',
    description: 'The postcode to filter stores by',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'suburb',
    description: 'The suburb to filter stores by',
    required: false,
    type: 'string',
  })
  @ApiOkResponse({
    description: 'Stores according to the specified filters',
    type: BasicStoreOpenApiSchema,
    isArray: true,
  })
  async getStores(@Query('postcode') postcode?: string, @Query('suburb') suburb?: string) {
    if (postcode) {
      return this.storesService.getStoresByPostcode(postcode);
    } else if (suburb) {
      return this.storesService.getStoresBySuburb(suburb);
    }
    return [];
  }

  @Get('all')
  @ApiOperation({ description: 'Get all stores' })
  @ApiOkResponse({
    description: 'All stores with basic information',
    type: BasicStoreOpenApiSchema,
    isArray: true,
  })
  async getAllStores() {
    return this.storesService.getAllStores();
  }

  @Get('details')
  @ApiOperation({ description: 'Get store by id or slug' })
  @ApiQuery({
    name: 'id',
    description: 'The id of the store',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'slug',
    description: 'The slug of the store',
    required: false,
    type: 'string',
  })
  @ApiOkResponse({ description: 'The found store', type: StoreOpenApiSchema })
  @ApiNotFoundResponse({ description: 'Store not found' })
  @ApiBadRequestResponse({ description: 'Either id or slug must be provided' })
  async getStore(@Query('id') id?: string, @Query('slug') slug?: string) {
    if (!id && !slug) {
      throw new BadRequestException('Either id or slug must be provided');
    }
    if (id) {
      return this.storesService.getStoreById(id);
    } else if (slug) {
      return this.storesService.getStoreBySlug(slug);
    }
    return undefined;
  }
}
