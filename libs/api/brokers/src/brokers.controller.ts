import { BadRequestException, Controller, Get, Param, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';

import { BasicBroker, basicBrokerSchema, brokerSchema, ROLE } from '@gp/data-access/broker';
import { createOpenApiSchemaFromZod } from '@gp/util/openapi';

import { BrokersService } from './brokers.service';

class BasicBrokerOpenApiSchema extends createOpenApiSchemaFromZod(
  'BasicBroker',
  basicBrokerSchema,
  { description: 'The schema for a broker with basic information' }
) {}

class BrokerOpenApiSchema extends createOpenApiSchemaFromZod('Broker', brokerSchema, {
  description: 'The schema for a broker',
}) {}

@ApiTags('Brokers')
@Controller('v1/aussie-brokers')
export class BrokersController {
  constructor(private brokersService: BrokersService) {}

  @Get()
  @ApiOperation({ description: 'Get stores according to the specified filters' })
  @ApiQuery({
    name: 'ids',
    description: 'A list of ids with comma separated values to filter brokers by',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'postcode',
    description: 'The postcode to filter brokers by',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'suburb',
    description: 'The suburb to filter brokers by',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'role',
    description: 'The role to filter brokers by',
    required: false,
    enum: ROLE,
  })
  @ApiQuery({
    name: 'shuffle',
    description: 'Whether to shuffle the brokers',
    required: false,
    type: 'boolean',
  })
  @ApiQuery({
    name: 'limit',
    description: 'The maximum number of brokers to return',
    required: false,
    type: 'number',
  })
  @ApiOkResponse({
    description: 'Stores according to the specified filters',
    type: BasicBrokerOpenApiSchema,
    isArray: true,
  })
  async getBrokers(
    @Query('ids') ids?: string,
    @Query('postcode') postcode?: string,
    @Query('suburb') suburb?: string,
    @Query('role') role?: ROLE,
    @Query('shuffle') shuffle?: boolean,
    @Query('limit') limit?: number
  ) {
    let brokers: BasicBroker[] = [];
    if (ids) {
      brokers = await this.brokersService.getBrokerByIds(ids);
    } else if (postcode) {
      brokers = await this.brokersService.getBrokersByPostcode(postcode);
    } else if (suburb) {
      brokers = await this.brokersService.getBrokersBySuburb(suburb);
    } else if (role) {
      brokers = await this.brokersService.getBrokersByRole(role);
    }
    if (shuffle) {
      brokers = brokers.sort(() => Math.random() - 0.5);
    }
    if (limit) {
      brokers = brokers.slice(0, limit);
    }
    return brokers;
  }

  @Get('all')
  @ApiOperation({ description: 'Get all brokers' })
  @ApiOkResponse({
    description: 'All brokers with basic information',
    type: BasicBrokerOpenApiSchema,
    isArray: true,
  })
  async getAllBrokers() {
    return this.brokersService.getAllBrokers();
  }

  @Get('store/:storeId')
  @ApiOperation({ description: 'Get all brokers for the specified store' })
  @ApiOkResponse({
    description: 'All brokers with basic information for the specified store',
    type: BasicBrokerOpenApiSchema,
    isArray: true,
  })
  async getBrokersByStoreId(@Param('storeId') storeId: string) {
    return this.brokersService.getBrokersByStoreId(storeId);
  }

  @Get('details')
  @ApiOperation({ description: 'Get broker by id or slug' })
  @ApiQuery({
    name: 'id',
    description: 'The id of the broker',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'slug',
    description: 'The slug of the store',
    required: false,
    type: 'string',
  })
  @ApiOkResponse({ description: 'The found broker', type: BrokerOpenApiSchema })
  @ApiNotFoundResponse({ description: 'Broker not found' })
  @ApiBadRequestResponse({ description: 'Either id or slug must be provided' })
  async getBroker(@Query('id') id?: string, @Query('slug') slug?: string) {
    if (!id && !slug) {
      throw new BadRequestException('Either id or slug must be provided');
    }
    if (id) {
      return this.brokersService.getBrokerById(id);
    } else if (slug) {
      return this.brokersService.getBrokerBySlug(slug);
    }
    return undefined;
  }
}
