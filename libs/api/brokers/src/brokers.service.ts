import { Injectable, NotFoundException } from '@nestjs/common';

import { AreaService } from '@gp/api/property-hub';
import { StoresService } from '@gp/api/stores';
import {
  getAllBrokers,
  getBrokerById,
  getBrokerBySlug,
  getBrokersByIds,
  getBrokersByRole,
  getBrokersBySuburb,
  ROLE,
} from '@gp/data-access/broker';

@Injectable()
export class BrokersService {
  constructor(private areaService: AreaService, private storesService: StoresService) {}

  async getAllBrokers() {
    return getAllBrokers();
  }

  async getBrokerById(id: string) {
    const broker = getBrokerById(id);
    if (!broker) {
      throw new NotFoundException(`Broker with id ${id} not found`);
    }
    return broker;
  }

  async getBrokerByIds(ids: string) {
    return getBrokersByIds(ids.split(','));
  }

  async getBrokerBySlug(slug: string) {
    const broker = getBrokerBySlug(slug);
    if (!broker) {
      throw new NotFoundException(`Broker with slug ${slug} not found`);
    }
    return broker;
  }

  async getBrokersByStoreId(storeId: string) {
    const store = await this.storesService.getStoreById(storeId);
    if (!store) return [];
    const brokerIds = store.relatedBrokers
      .filter(
        (broker) =>
          broker.status === 'ACTIVE' &&
          [ROLE.FRANCHISEE_BROKER, ROLE.MORTGAGE_BROKER, ROLE.FIELD_ASSISTANT].includes(
            broker.relationship as ROLE
          )
      )
      .map((broker) => broker.id);
    const brokers = await getBrokersByIds(brokerIds);
    // Sort the brokers according to the order of the brokerIds
    brokers.sort((a, b) => brokerIds.indexOf(a.id) - brokerIds.indexOf(b.id));
    return brokers;
  }

  async getBrokersByPostcode(postcode: string) {
    const suburbs = await this.areaService.getSuburbsByPostcode(postcode);
    return await getBrokersBySuburb(
      suburbs.map(({ name, postcode }) => `${name} ${postcode}`).join(',')
    );
  }

  async getBrokersBySuburb(suburb: string) {
    return getBrokersBySuburb(suburb);
  }

  async getBrokersByRole(role: ROLE) {
    return getBrokersByRole(role);
  }
}
