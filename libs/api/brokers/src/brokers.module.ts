import { Module } from '@nestjs/common';

import { AreaService } from '@gp/api/property-hub';
import { StoresService } from '@gp/api/stores';

import { BrokersController } from './brokers.controller';
import { BrokersService } from './brokers.service';

@Module({
  controllers: [BrokersController],
  providers: [BrokersService, AreaService, StoresService],
  exports: [BrokersService],
})
export class BrokersModule {}
