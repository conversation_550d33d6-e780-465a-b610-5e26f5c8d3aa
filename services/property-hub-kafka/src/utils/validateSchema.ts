import Ajv2019 from 'ajv/dist/2019';
import addFormats from 'ajv-formats';
import {
  AreaDeletedPayload,
  areaEventSchema,
  AreaPayload,
} from '@lendi/kafka-topics-property-hub/area-event';
import {
  Property,
  PropertyDeletedPayload,
  propertyEventSchema,
} from '@lendi/kafka-topics-property-hub/property-event';

const PROPERTY_SCHEMA_KEY = 'property';
const AREA_SCHEMA_KEY = 'area';

const ajv = new Ajv2019({
  strict: false,
});

//https://ajv.js.org/guide/managing-schemas.html performance consideration
addFormats(ajv);
ajv.addSchema(areaEventSchema, AREA_SCHEMA_KEY);
ajv.addSchema(propertyEventSchema, PROPERTY_SCHEMA_KEY);

type AreaPayloads = AreaPayload | AreaDeletedPayload;
type PropertyPayloads = PropertyDeletedPayload | Property;

interface ValidationMessage {
  type: string;
  version: string;
  payload: AreaPayloads | PropertyPayloads;
}

export function validatePropertyEventSchema(msg: ValidationMessage) {
  const validate = ajv.getSchema(PROPERTY_SCHEMA_KEY);
  return validate(msg);
}

export function validateAreaEventSchema(msg: ValidationMessage) {
  const validate = ajv.getSchema(AREA_SCHEMA_KEY);
  if (!validate(msg)) {
    throw new Error(
      `Schema validation failed with id: ${msg.payload?.slug}, errors: ${JSON.stringify(
        validate.errors
      )}`
    );
  }
}
