import {
  Area,
  Council,
  PrimarySchool,
  SecondarySchool,
  State,
  Suburb,
} from '@lendi/kafka-topics-property-hub/area-event';

import type { AreaData } from '@gp/data-access/property-hub';
import {
  AGE_RANGE,
  AREA_REPORT_PROPERTY_TYPE,
  HOUSEHOLD_COMPOSITION,
  INCOME_GROUP,
  OCCUPANT,
  OCCUPATION,
  SCHOOL_TYPE,
  SCHOOL_YEARS,
} from '@gp/data-access/property-hub';
import { schema } from '@gp/database/property-hub';

const { states, councils, suburbs } = schema;

export type InsertState = typeof states.$inferInsert;
export type InsertCouncil = typeof councils.$inferInsert;
export type InsertSuburb = typeof suburbs.$inferInsert;

export function transform(
  type: Area['type'],
  payload: State | Suburb | Council
): InsertState | InsertSuburb | InsertCouncil {
  switch (type) {
    case 'COUNCIL':
      return parseCouncil(payload as Council);
    case 'STATE':
      return parseState(payload as State);
    case 'SUBURB':
      return parseSuburb(payload as Suburb);
  }
}

const parseState = (payload: State) => {
  return {
    id: payload.id,
    slug: payload.slug,
    name: payload.name,
    data: parseAreaData(payload),
  } as InsertState;
};

const parseCouncil = (payload: Council) => {
  return {
    id: payload.id,
    slug: payload.slug,
    name: payload.name,
    stateSlug: payload.stateSlug,
    data: parseAreaData(payload),
  } as InsertCouncil;
};

const parseSuburb = (payload: Suburb) => {
  return {
    id: payload.id,
    slug: payload.slug,
    name: payload.name,
    stateSlug: payload.stateSlug,
    postcode: payload.postcode,
    otherPostcode: payload.otherPostcode,
    councilSlug: payload.councilSlug,
    surroundingSuburbs: payload.surroundingSuburbs,
    location: payload.location,
    data: parseAreaData(payload),
  } as InsertSuburb;
};

const parseAreaData = (payload: State | Suburb | Council) => {
  return {
    population: payload.population,
    propertyTypes: parseEnum(payload.propertyTypes, AREA_REPORT_PROPERTY_TYPE),
    ageRanges: parseEnum(payload.ageRanges, AGE_RANGE),
    householdCompositions: parseEnum(payload.householdCompositions, HOUSEHOLD_COMPOSITION),
    incomeGroups: parseEnum(payload.incomeGroups, INCOME_GROUP),
    occupantStatus: parseEnum(payload.occupantStatus, OCCUPANT),
    occupations: parseEnum(payload.occupantStatus, OCCUPATION),
    houseMedianPrice: payload.houseMedianPrice,
    houseMedianPriceHistory: payload.houseMedianPriceHistory as AreaData['houseMedianPriceHistory'],
    housePriceChangeLast12Months: payload.housePriceChangeLast12Months,
    houseMedianRent: payload.houseMedianRent,
    houseRentChangeLast12Months: payload.houseRentChangeLast12Months,
    unitMedianPrice: payload.unitMedianPrice,
    unitMedianPriceHistory: payload.unitMedianPriceHistory as AreaData['unitMedianPriceHistory'],
    unitPriceChangeLast12Months: payload.unitPriceChangeLast12Months,
    unitMedianRent: payload.unitMedianRent,
    unitRentChangeLast12Months: payload.unitRentChangeLast12Months,
    houseMedianDaysOnMarket: payload.houseMedianDaysOnMarket,
    unitMedianDaysOnMarket: payload.unitMedianDaysOnMarket,
    soldLast12Months: payload.soldLast12Months,
    propertiesAvailableLastMonth: payload.propertiesAvailableLastMonth,
    primarySchools: parseSchools(payload.primarySchools) as AreaData['primarySchools'],
    secondarySchools: parseSchools(payload.primarySchools) as AreaData['secondarySchools'],
    universities: payload.universities,
  } as AreaData;
};

function parseEnum<T, E>(payload: T, eunm: E) {
  if (!payload) return undefined;
  return Object.entries(payload).map((ent) => [eunm[ent[0]], ent[1]]);
}

function parseSchools(
  payload: PrimarySchool[] | SecondarySchool[]
): AreaData['primarySchools'] | AreaData['secondarySchools'] {
  return payload.map((school) => ({
    name: school.name,
    type: SCHOOL_TYPE[school.type],
    years: SCHOOL_YEARS[school.years],
  }));
}
