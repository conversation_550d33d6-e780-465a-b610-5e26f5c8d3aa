import { Logger } from 'pino';
import { getKafkaMessageDataInfo, PayloadEnvelope } from '@lendi/core-kafka-client';
import { KafkaMessageData } from '@lendi/core-kafka-client/lib/KafkaMessageData';
import { AreaEvent, AreaPayload } from '@lendi/kafka-topics-property-hub/area-event';

import { validateAreaEventSchema } from '../../utils';
import { createModuleLogger } from '../../worker/logger';
import { tracer } from '../../worker/tracer';
import { AreaEventHandler } from './handlers/';

export interface Handlers {
  eventHandler?: AreaEventHandler;
}

export interface Options {
  logger?: Logger;
}

export class Consumer {
  private logger: Logger;
  private readonly eventHandler: AreaEventHandler;
  private messageTypeToHandlerMethodMap: Map<
    string,
    | AreaEventHandler['handleCreated']
    | AreaEventHandler['handleDeleted']
    | AreaEventHandler['handleUpdated']
  >;

  constructor(handlers: Handlers = {}, options: Options = {}) {
    this.logger = options.logger ?? createModuleLogger('handle-kafka-message');
    this.eventHandler = handlers.eventHandler ?? new AreaEventHandler();

    this.messageTypeToHandlerMethodMap = new Map([
      ['AreaCreated', this.eventHandler.handleCreated],
      ['AreaUpdated', this.eventHandler.handleUpdated],
      ['AreaDeleted', this.eventHandler.handleDeleted],
    ]);
  }

  async consume(msg: PayloadEnvelope<AreaPayload> | null, kafkaMessageData: KafkaMessageData) {
    const messageInfo = getKafkaMessageDataInfo(kafkaMessageData);
    const { payload, type } = msg || {};

    if (!payload || !type) {
      this.logger.error({ messageInfo, type }, `No payload or no type`, kafkaMessageData.key);
      return;
    }

    const { id, slug, type: areaType } = payload;
    const logTags = { messageInfo, id, slug, areaType };

    this.logger.info(logTags, `Received message.`);
    const handlerMethod = this.messageTypeToHandlerMethodMap.get(type as AreaEvent['type']);

    if (!handlerMethod) {
      this.logger.warn(logTags, `Not handling event type ${type}`);
      return;
    }

    try {
      validateAreaEventSchema(msg);
      await handlerMethod(payload);
      this.logger.info(logTags, `Consumed message.`);
    } catch (e) {
      this.logger.error(e);
    }
  }
}

export const handleKafkaMessage = tracer.wrap(
  'property_hub_area_consumer_worker',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async (msg: PayloadEnvelope<any> | null, kafkaMessageData: KafkaMessageData) => {
    // since ConsumerHandler doesn't support Generics type, so PayloadEnvelope must use any
    await new Consumer().consume(msg, kafkaMessageData);
  }
);
