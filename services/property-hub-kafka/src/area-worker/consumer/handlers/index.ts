import { Logger } from 'pino';
import { AreaDeletedPayload, AreaPayload } from '@lendi/kafka-topics-property-hub/area-event';

import {
  deleteCouncil,
  deleteState,
  deleteSuburb,
  updateCouncil,
  updateState,
  updateSuburb,
  upsertCouncil,
  upsertState,
  upsertSuburb,
} from '@gp/database/property-hub';

import { createModuleLogger } from '../../../worker/logger';
import { InsertCouncil, InsertState, InsertSuburb, transform } from '../helpers';

export interface Options {
  logger?: Logger;
}

export class AreaEventHandler {
  private logger: Logger;

  constructor(options: Partial<Options> = {}) {
    this.logger = options.logger ?? createModuleLogger('area-event-handler');
    this.handleCreated = this.handleCreated.bind(this);
    this.handleUpdated = this.handleUpdated.bind(this);
    this.handleDeleted = this.handleDeleted.bind(this);
  }

  async handleCreated(payload: AreaPayload): Promise<void> {
    try {
      const areaForInsert = transform(payload.type, payload);
      switch (payload.type) {
        case 'STATE':
          await upsertState(areaForInsert as InsertState);
          break;
        case 'COUNCIL':
          await upsertCouncil(areaForInsert as InsertCouncil);
          break;
        case 'SUBURB':
          await upsertSuburb(areaForInsert as InsertSuburb);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Failed to insert ${payload.type}, ${payload.slug} with [${JSON.stringify(error)}].`
      );
      return;
    }
    this.logger.info(`Insert success ${payload.slug}, ${payload.id}`);
  }

  async handleUpdated(payload: AreaPayload): Promise<void> {
    try {
      const areaForUpdate = transform(payload.type, payload);
      switch (payload.type) {
        case 'STATE':
          await updateState(areaForUpdate as InsertState);
          break;
        case 'COUNCIL':
          await updateCouncil(areaForUpdate as InsertCouncil);
          break;
        case 'SUBURB':
          await updateSuburb(areaForUpdate as InsertSuburb);
          break;
      }
    } catch (error) {
      this.logger.error(`Failed to update area with [${JSON.stringify(error)}].`);
      return;
    }

    this.logger.info(`Update success success ${payload.slug}, ${payload.id}`);
  }

  async handleDeleted(payload: AreaDeletedPayload): Promise<void> {
    try {
      switch (payload.type) {
        case 'STATE':
          await deleteState(payload.slug, payload.id);
          break;
        case 'COUNCIL':
          await deleteCouncil(payload.slug, payload.id);
          break;
        case 'SUBURB':
          await deleteSuburb(payload.slug, payload.id);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Failed to delete area ${payload.slug}, ${payload.id} with [${JSON.stringify(error)}].`
      );
      return;
    }

    this.logger.info(`Delete success success ${payload.slug}, ${payload.id}`);
  }
}
