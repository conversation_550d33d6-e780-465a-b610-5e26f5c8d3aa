/* istanbul ignore file */
import '../worker/tracer'; // must be loaded before everything else

import {
  BatchConsumerHandler,
  ConsumerHandler,
  getClusterEnvironment,
  KafkaConsumer,
} from '@lendi/core-kafka-client';

import { config } from './config';
import { KafkaConfig } from './config/configDefinitions';
import { createModuleLogger } from './logger';

const logger = createModuleLogger('kafka-consumer-startup');

const kafkaConfigOriginal = config().kafka;
const kafkaConfig: KafkaConfig = {
  ...kafkaConfigOriginal,
  clusterCertificate: getClusterEnvironment(config().environment),
};

export const startConsumer = async (
  handleKafkaMessage: ConsumerHandler | BatchConsumerHandler,
  batch?: boolean
) => {
  try {
    const kafkaConsumer = new KafkaConsumer({ ...kafkaConfig, batch });

    if (kafkaConfig.topic.topicName.length > 0) {
      await kafkaConsumer.subscribeConsumer(
        [kafkaConfig.topic.topicName],
        handleKafkaMessage,
        [kafkaConfig.sourceReference],
        {
          fromBeginning: {
            topics: [kafkaConfig.topic.topicName],
            enabled: true,
          },
        }
      );
      logger.info('Consumer subscribed! topicName: [' + kafkaConfig.topic.topicName + ']');
    }

    logger.info('Consumer started!');
  } catch (ex) {
    logger.error(`Error Starting the consumer: ${ex}`);
  }
};
