/* istanbul ignore file */
import { Schema } from '@lendi/config';

import { ConfigSchema } from './configDefinitions';

const configuration: Schema<ConfigSchema> = {
  environment: {
    doc: 'This is the environment that the application has been deployed to',
    format: ['local', 'development', 'staging', 'production'],
    default: 'development',
    env: 'ENVIRONMENT',
  },
  port: {
    doc: 'The port to run the application on',
    format: 'port',
    default: '8080',
    env: 'PORT',
  },
  host: {
    doc: 'The hostname of the application',
    format: String,
    default: 'localhost',
    env: 'HOST',
  },
  logger: {
    name: {
      doc: 'This will be used when we instantiate logger',
      format: String,
      default: `worker-${process.env.WORKER_NAME}-${process.env.ENVIRONMENT}`,
    },
  },
  kafka: {
    brokers: {
      doc: 'Kafka broker address',
      format: 'parsed-array',
      default: ['localhost:9094'], // Environment variable will be a comma separated string and `parsed-array` will parse the value appropriately.
      env: 'KAFKA_BROKERS',
    },
    useSsl: {
      doc: 'Kafka option using SSL',
      format: Boolean,
      default: false,
      env: 'KAFKA_REQUIRE_SSL',
    },
    username: {
      doc: 'Kafka credentials username',
      format: String,
      default: '',
      env: 'KAFKA_USERNAME',
    },
    password: {
      doc: 'Kafka credentials password',
      format: String,
      default: '',
      env: 'KAFKA_PASSWORD',
    },
    groupId: {
      doc: 'Kafka group ID',
      format: String,
      default: `cms-service-${process.env.WORKER_NAME}-worker-${process.env.LCD_TAG}`,
    },
    sourceReference: {
      doc: 'Source Reference that will be attached kafka message header',
      format: String,
      default: `local`,
      env: 'LCD_TAG',
    },
    topic: {
      topicName: {
        doc: 'Kafka topicgoing to be consumed by the worker',
        format: String,
        default: '',
        env: 'KAFKA_TOPIC_NAME',
      },
    },
  },
  aws: {
    region: {
      doc: 'AWS Region',
      format: String,
      env: 'AWS_REGION',
      default: 'ap-southeast-2',
    },
  },
};

export default configuration;
