import { BaseConfig } from '@lendi/config';

import { ConfigSchema, KafkaConfig } from './configDefinitions';
import configuration from './configuration';

let baseConfig: BaseConfig<ConfigSchema, KafkaConfig> | null = null;

export function setupBaseConfig(): void {
  baseConfig = new BaseConfig<ConfigSchema, KafkaConfig>(configuration);
}

function getBaseConfig(): BaseConfig<ConfigSchema, KafkaConfig> | null {
  return baseConfig;
}

export function config(
  getter: () => BaseConfig<ConfigSchema, KafkaConfig> | null = getBaseConfig
): ConfigSchema {
  let configVal = getter();

  if (!configVal) {
    setupBaseConfig();
    configVal = getter();
  }

  if (configVal) {
    return configVal.getConfig();
  } else {
    throw Error('Config is not set');
  }
}
