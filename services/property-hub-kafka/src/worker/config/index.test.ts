import { config, setupBaseConfig } from './index';

describe('config', () => {
  it('config not init', () => {
    expect(() => {
      config(() => {
        return null;
      });
    }).toThrow(Error('Config is not set'));
  });

  it('should return the empty data (default config) when no env set.', () => {
    delete process.env.KAFKA_BROKERS;
    setupBaseConfig();
    expect(config().kafka!.brokers).toEqual(['localhost:9094']);
  });

  it('should parse the string to array when env is set', () => {
    process.env.KAFKA_BROKERS = 'A,B';
    setupBaseConfig();
    expect(config().kafka!.brokers).toEqual(['A', 'B']);
  });

  it('environment', () => {
    process.env.ENVIRONMENT = 'development';
    setupBaseConfig();
    expect(config().environment).toEqual('development');
  });

  it('port', () => {
    process.env.PORT = '8081';
    setupBaseConfig();
    expect(config().port).toEqual(8081);
  });

  it('host', () => {
    process.env.HOST = 'local';
    setupBaseConfig();
    expect(config().host).toEqual('local');
  });
});
