import { Property } from '@lendi/kafka-topics-property-hub/property-event';

import { AVM_CONFIDENCE } from '@gp/data-access/corelogic';
import { LISTING_TYPE, Property as PropertyType } from '@gp/data-access/property-hub';
import { schema } from '@gp/database/property-hub';

const { propertiesV2 } = schema;

type InsertProperty = typeof propertiesV2.$inferInsert & PropertyType;
export function transform(propertyPayload: Property): InsertProperty {
  const parsedAddress = parseAddress(propertyPayload.primaryAddress);
  const result: InsertProperty = {
    id: propertyPayload.id,
    slug: propertyPayload.slug,
    address: propertyPayload.primaryAddress,
    suburbSlug: propertyPayload.suburbSlug,
    corelogicId: propertyPayload.sourceIds.corelogic,
    gnafId: propertyPayload.sourceIds.gnaf,
    listingLoopId: propertyPayload.sourceIds.listingLoop,
    reipId: propertyPayload.sourceIds.reip,
    data: {
      ...parsedAddress,
      priceGuide: propertyPayload.priceGuide,
      type: typeValues[propertyPayload.type],
      primaryAddress: propertyPayload.primaryAddress,
      otherAddresses: propertyPayload.otherAddresses,
      subType: propertyPayload.subType,
      bedrooms: propertyPayload.bedrooms,
      bathrooms: propertyPayload.bathrooms,
      carSpaces: propertyPayload.carSpaces,
      builtYear: Number(propertyPayload.builtYear),
      photo: propertyPayload.photo,
      photos: propertyPayload.photos,
      floorPlans: propertyPayload.floorPlans,
      landArea: propertyPayload.landArea,
      floorArea: propertyPayload.floorArea,
      listingLoopId: propertyPayload.sourceIds.listingLoop,
      forSale: !!propertyPayload.listingType,
      listingType: listingTypeValues[propertyPayload.listingType],
      listingDescription: propertyPayload.listingDescription, // Do we need to wash the data here?
      realEstateAgents: propertyPayload.realEstateAgents,
      propertyDescription: propertyPayload.propertyDescription, // Do we need to wash the data here?
      listedDate: propertyPayload.listedDate,
      inspectionDateTimes: propertyPayload.inspectionDateTimes,
      auctionDateTime: propertyPayload.auctionDateTime,
      lastSoldDate: propertyPayload.lastSoldDate,
      lastSoldDescription: propertyPayload.lastSoldDescription, // Do we need to wash the data here?
      lastSoldPrice: propertyPayload.lastSoldPrice,
      estimatedValue: propertyPayload.estimatedValue
        ? {
            ...propertyPayload.estimatedValue,
            confidence: estimatedValueConfidenceValues[propertyPayload.estimatedValue.confidence],
          }
        : undefined,
      estimatedRent: propertyPayload.estimatedRent,
      timeline: propertyPayload.timeline,
      schools: propertyPayload.schools,
      informationStatement: propertyPayload.informationStatement,
    },
  };
  if (
    Array.isArray(propertyPayload.location) &&
    isNumeric(propertyPayload.location[0]) &&
    isNumeric(propertyPayload.location[1])
  ) {
    result.location = propertyPayload.location as [number, number];
  }
  return result;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isNumeric(n: any) {
  const parsedNumber = Number(n);
  return !isNaN(parsedNumber) && isFinite(parsedNumber);
}

const parseAddress = (address: string) => {
  let postcode: string | undefined = undefined;
  let state: string | undefined = undefined;
  let suburb = '';
  let streetAddress = '';
  const addressChunk = address?.split(', ');
  const statePostcode = addressChunk?.pop();
  suburb = addressChunk?.pop();
  streetAddress = addressChunk?.join(', ');
  if (statePostcode) {
    state = statePostcode.split(' ')[0];
    postcode = statePostcode.split(' ')[1];
  }
  return {
    postcode,
    state,
    suburb,
    streetAddress,
  };
};

const typeValues = {
  ACREAGE_SEMI_RURAL: 'Acreage semi rural',
  ALPINE: 'Alpine',
  APARTMENT: 'Apartment',
  BLOCK_OF_UNITS: 'Block of units',
  BUSINESS: 'Business',
  COMMERCIAL: 'Commercial',
  COMMUNITY: 'Community',
  DUPLEX_SEMI_DETACHED: 'Duplex semi detached',
  FARM: 'Farm',
  FLAT: 'Flat',
  FLATS: 'Flats',
  HOUSE: 'House',
  LAND: 'Land',
  OTHER: 'Other',
  RETIREMENT: 'Retirement',
  SERVICED_APARTMENT: 'Serviced apartment',
  STORAGE_UNIT: 'Storage_Unit',
  STUDIO: 'Studio',
  TERRACE: 'Terrace',
  TOWNHOUSE: 'Townhouse',
  UNIT: 'Unit',
  VILLA: 'Villa',
  WAREHOUSE: 'Warehouse',
};

const listingTypeValues = {
  ON_MARKET: LISTING_TYPE.ON_MARKET,
  PRE_MARKET: LISTING_TYPE.PRE_MARKET,
  OFF_MARKET: LISTING_TYPE.OFF_MARKET,
};

const estimatedValueConfidenceValues = {
  HIGH: AVM_CONFIDENCE.HIGH,
  MEDIUM_HIGH: AVM_CONFIDENCE.MEDIUM_HIGH,
  MEDIUM: AVM_CONFIDENCE.MEDIUM,
  MEDIUM_LOW: AVM_CONFIDENCE.MEDIUM_LOW,
  LOW: AVM_CONFIDENCE.LOW,
};
