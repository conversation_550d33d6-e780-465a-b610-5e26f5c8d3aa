import { Logger } from 'pino';
import { BatchConsumerHandler } from '@lendi/core-kafka-client';
import { PropertyEvent } from '@lendi/kafka-topics-property-hub/property-event';

import { validatePropertyEventSchema } from '../../utils/';
import { createModuleLogger } from '../../worker/logger';
import { tracer } from '../../worker/tracer';
import { Handlers, KafkaMessage, KafkaUtils, Options } from '../type';
import { PropertyEventHandler } from './handlers';

export class Consumer {
  private readonly eventHandler: PropertyEventHandler;
  private logger: Logger;

  constructor(handlers: Handlers = {}, options: Options = {}) {
    this.eventHandler =
      (handlers.eventHandler as PropertyEventHandler) ?? new PropertyEventHandler();
    this.logger = options.logger ?? createModuleLogger('handle-kafka-message');
  }

  async consume(messages: KafkaMessage, kafkaUtils: KafkaUtils) {
    this.logger.info(
      { count: messages.length },
      `Processing batch of messages for partition: ${messages[0].messageData.partition}`
    );

    if (!kafkaUtils.isRunning() || kafkaUtils.isStale()) {
      this.logger.error(
        {
          count: messages.length,
          partition: messages[0].messageData.partition,
          start: messages[0].messageData.partition,
        },
        `kafka is running: ${kafkaUtils.isRunning()} or is stale: ${kafkaUtils.isStale()}`
      );
      return;
    }

    const messagesByType = {
      upsert: [] as KafkaMessage,
      deleted: [] as KafkaMessage,
      invalid: [] as {
        key: string;
        offset: string;
      }[],
    };

    for (const kafkaMessage of messages) {
      const { message } = kafkaMessage;
      if (!message.payload || !message.type || !message.version) {
        messagesByType.invalid.push({
          key: kafkaMessage.messageData.key,
          offset: kafkaMessage.messageData.offset,
        });
        continue;
      }
      try {
        const isPayloadValid = validatePropertyEventSchema(message);
        if (!isPayloadValid) {
          messagesByType.invalid.push({
            key: kafkaMessage.messageData.key,
            offset: kafkaMessage.messageData.offset,
          });
        }
        switch (message.type) {
          case 'PropertyCreated':
          case 'PropertyUpdated':
            messagesByType.upsert.push(kafkaMessage);
            break;
          case 'PropertyDeleted':
            messagesByType.deleted.push(kafkaMessage);
            break;
        }
      } catch (e) {
        messagesByType.invalid.push({
          key: kafkaMessage.messageData.key,
          offset: kafkaMessage.messageData.offset,
        });
      }
    }
    if (messagesByType.invalid.length > 0) {
      this.logger.error(
        { ids: messagesByType.invalid.map((info) => info.key) },
        `Messages are invalid for messages (ids)`
      );
      await kafkaUtils.commitOffsetsIfNecessary({
        topics: [
          {
            topic: process.env['KAFKA_TOPIC_NAME'],
            partitions: messagesByType.invalid.map((message) => ({
              partition: messages[0].messageData.partition,
              offset: message.offset,
            })),
          },
        ],
      });
      await kafkaUtils.heartbeat();
    }

    try {
      await Promise.allSettled([
        this.eventHandler.handleUpsert(
          messagesByType.upsert,
          kafkaUtils.heartbeat,
          kafkaUtils.commitOffsetsIfNecessary
        ),
        this.eventHandler.handleDeleted(
          messagesByType.deleted.map((message) => message.message.payload)
        ),
      ]);
    } catch (e) {
      this.logger.error(e);
    }
  }
}

export const handleKafkaMessage = tracer.wrap('property_hub_property_consumer_worker', (async (
  messages,
  kafkaUtils
) => {
  // since ConsumerHandler doesn't support Generics type, so PayloadEnvelope must use any
  await new Consumer().consume(messages, kafkaUtils);
}) as BatchConsumerHandler<PropertyEvent['payload']>);
