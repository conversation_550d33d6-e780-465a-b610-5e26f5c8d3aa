import { Logger } from 'pino';
import { PropertyDeletedPayload } from '@lendi/kafka-topics-property-hub/property-event';

import { deleteProperties, upsertProperties } from '@gp/database/property-hub';

import { createModuleLogger } from '../../../worker/logger';
import { KafkaMessage, KafkaUtils } from '../../type';
import { transform } from '../helpers';

export interface Options {
  logger?: Logger;
}

export class PropertyEventHandler {
  private logger: Logger;

  constructor(options: Partial<Options> = {}) {
    this.logger = options.logger ?? createModuleLogger('property-event-handler');
    this.handleUpsert = this.handleUpsert.bind(this);
    this.handleDeleted = this.handleDeleted.bind(this);
  }

  async handleUpsert(
    messages: KafkaMessage,
    heartBeat: () => Promise<void>,
    commit: KafkaUtils['commitOffsetsIfNecessary']
  ): Promise<void> {
    if (messages.length <= 0) return;
    const messagesLength = messages.length;
    let running = 0;
    while (running < messagesLength) {
      const chunkMessages = messages.slice(running, running + 500);
      try {
        const properties = [];
        chunkMessages.forEach((message) => {
          if (!properties.some((p) => p.id === message.messageData.key)) {
            properties.push(transform(message.message.payload));
          }
        });
        const successIds: { id: string }[] = await upsertProperties(properties, this.logger);
        this.logger.info(
          `Upsert ${successIds.length} / ${chunkMessages.length} properties successfully`
        );
        // insert returning only return success ids, filtering unsuccess ids and log
        if (successIds.length !== properties.length) {
          const errorIds = [];
          chunkMessages.forEach((message) => {
            const isSuccess = successIds.some((o) => o.id === message.messageData.key);
            if (!isSuccess) {
              errorIds.push(message.messageData.key);
            }
          });
          this.logger.error({ ids: errorIds }, `Upsert properties unsuccessfully`);
        }
        await commit({
          topics: [
            {
              topic: process.env['KAFKA_TOPIC_NAME'],
              partitions: chunkMessages.map((message) => ({
                partition: message.messageData.partition,
                offset: message.messageData.offset,
              })),
            },
          ],
        });
        await heartBeat();
      } catch (error) {
        this.logger.error(`Failed to insert property with [${JSON.stringify(error)}].`);
        this.logger.error({ ids: chunkMessages.map((m) => m.messageData.key) });
      }
      running += 500;
    }
  }

  async handleDeleted(payloads: PropertyDeletedPayload[]): Promise<void> {
    if (payloads.length <= 0) return;
    const slugs = payloads.map((payload) => payload.slug);
    const ids = payloads.map((payload) => payload.id);
    try {
      await deleteProperties(slugs, ids);
    } catch (error) {
      this.logger.error(`Failed to delete property with [${JSON.stringify(error)}].`);
      return;
    }

    this.logger.info(`Delete success ${slugs.toString()}, ${ids.toString()}`);
  }
}
