{"name": "property-hub-kafka", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "services/property-hub-kafka/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "property-hub-kafka:build"}, "configurations": {"development": {"buildTarget": "property-hub-kafka:build:development"}, "production": {"buildTarget": "property-hub-kafka:build:production"}}}, "start": {"executor": "nx:run-commands", "options": {"command": "node dist/{args.worker}-worker/main", "cwd": "services/property-hub-kafka"}}}}