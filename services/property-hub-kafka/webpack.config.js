const { NxAppWebpackPlugin } = require('@nx/webpack/app-plugin');
const { join } = require('path');

console.log(`Building for ${process.env['CONSUMER_NAME']} consumer`);

let buildPath = process.env['CONSUMER_NAME'] === 'AREA' ? 'area-worker' : 'property-worker';

module.exports = {
  output: {
    path: join(__dirname, `./dist/${buildPath}`),
  },
  devServer: {
    port: process.env['CONSUMER_NAME'] === 'AREA' ? 5865 : 5864,
  },
  plugins: [
    new NxAppWebpackPlugin({
      target: 'node',
      compiler: 'tsc',
      main: `./src/${buildPath}/main.ts`,
      tsConfig: './tsconfig.app.json',
      assets: ['./src/assets'],
      optimization: false,
      outputHashing: 'none',
      sourceMap: true,
      watch: true,
    }),
  ],
};
