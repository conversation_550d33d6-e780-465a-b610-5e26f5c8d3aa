version: '3.3'
services:
  generic-image:
    build:
      context: ../../
      dockerfile: services/property-hub-kafka/ci/generic/Dockerfile
      args:
        NPM_TOKEN: $NPM_TOKEN
    image: genericimage

  area-worker:
    image: genericimage
    ports:
      - '5861:5861'
    env_file: ./envs/.env.development
    environment:
      NODE_ENV: development
      ENVIRONMENT: development
      LOCAL: local
      KAFKA_BROKERS: kafka:9092
      LCD_TAG: ${LCD_TAG}
      ACCEPTED_SOURCES: ${LCD_TAG}
      KAFKA_TOPIC_NAME: area-event
      WORKER_NAME: gp-area-worker
      CONSUMER_NAME: AREA
    volumes:
      - ./src:/usr/growth-product/services/property-hub-kafka/src
    command: /bin/sh -c "pnpm property-hub-kafka:serve"
    depends_on:
      - zookeeper
      - kafka
      - generic-image
    networks:
      - property-hub-service

  property-worker:
    image: genericimage
    ports:
      - '5862:5862'
    env_file: ./envs/.env.development
    environment:
      NODE_ENV: development
      ENVIRONMENT: development
      LOCAL: local
      KAFKA_BROKERS: kafka:9092
      LCD_TAG: ${LCD_TAG}
      ACCEPTED_SOURCES: ${LCD_TAG}
      KAFKA_TOPIC_NAME: property-event
      WORKER_NAME: gp-property-worker
      CONSUMER_NAME: PROPERTY
    volumes:
      - ./src:/usr/growth-product/services/property-hub-kafka/src
    command: /bin/sh -c "pnpm property-hub-kafka:serve"
    depends_on:
      - zookeeper
      - kafka
      - generic-image
    networks:
      - property-hub-service

  zookeeper:
    image: wurstmeister/zookeeper
    container_name: zookeeper
    networks:
      - property-hub-service
    ports:
      - 2181:2181

  kafka:
    image: harbor-core.lendi-paas-mgmt.net/public/leoweixw/kafka
    container_name: kafka
    ports:
      - 9092:9092
      - 9094:9094
    environment:
      KAFKA_ADVERTISED_LISTENERS: INSIDE://kafka:9092,OUTSIDE://${MY_IP}:9094
      KAFKA_LISTENERS: INSIDE://:9092,OUTSIDE://:9094
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INSIDE:PLAINTEXT,OUTSIDE:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: INSIDE
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
    depends_on:
      - zookeeper
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./examples/property-event/message.json:/message.json
    networks:
      - property-hub-service

networks:
  property-hub-service:
    driver: bridge
