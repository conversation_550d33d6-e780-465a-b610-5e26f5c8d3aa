plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: 'NPM_TOKEN'
  - lendi-au/ssm#0.5.5:
      ssmkey: 'platform/NPM_TOKEN'
      exportname: LENDI_NPM_TOKEN
  - docker#v3.7.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest # this image has the tools baked in already
      workdir: /app
      environment:
        - 'NPM_TOKEN'
        - 'LENDI_NPM_TOKEN'
        - 'BUILDKITE_BRANCH'
        - 'BUILDKITE_BUILD_NUMBER'
        - 'LENDI_ECR_REGISTRY'
        - 'DATADOG_API_KEY'
        - 'DATADOG_APP_KEY'
      propagate-environment: true

steps:
  - group: '🎯 Growth Product - Property hub kafka service'
    key: 'property-hub-kafka-service'
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ':docker: PROPERTY EVENT WORKER- Build'
        agents:
          queue: build
        key: 'property-event-worker:build'
        commands:
          - echo "⏳ Building Property Hub Kafka Service"
          - ./scripts/ci/build-image.sh gp-property-worker ./services/property-hub-kafka/ci/property-event-worker/Dockerfile
          - echo "🚀 Pushing Image for Property Hub Kafka Service"
          - ./scripts/ci/push-image.sh gp-property-worker
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins:
          - lendi-au/ssm#0.5.5:
              ssmkey: 'NPM_TOKEN'
          - lendi-au/ssm#0.5.5:
              ssmkey: 'platform/NPM_TOKEN'
              exportname: LENDI_NPM_TOKEN
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lcd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/liam'
      - label: ':docker: AREA EVENT WORKER - Build'
        agents:
          queue: build
        key: 'area-event-worker:build'
        commands:
          - echo "⏳ Building Property Hub Kafka Service"
          - ./scripts/ci/build-image.sh gp-area-worker ./services/property-hub-kafka/ci/area-event-worker/Dockerfile
          - echo "🚀 Pushing Image for Property Hub Kafka Service"
          - ./scripts/ci/push-image.sh gp-area-worker
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins:
          - lendi-au/ssm#0.5.5:
              ssmkey: 'NPM_TOKEN'
          - lendi-au/ssm#0.5.5:
              ssmkey: 'platform/NPM_TOKEN'
              exportname: LENDI_NPM_TOKEN
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lcd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/liam'
      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ':codedeploy: PROPERTY EVENT WORKER - Deploy (DEV)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/DEV/deploy'
        agents:
          queue: development
        key: 'property-event-worker:deploy-development'
        depends_on:
          - step: 'property-event-worker:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying PROPERTY EVENT WORKER (DEV)"
          - ./scripts/ci/deploy.sh development gp-property-worker ./services/property-hub-kafka/ci/property-event-worker/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - label: ':codedeploy: AREA EVENT WORKER - Deploy (DEV)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/DEV/deploy'
        agents:
          queue: development
        key: 'area-event-worker:deploy-development'
        depends_on:
          - step: 'area-event-worker:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying AREA EVENT WORKER (DEV)"
          - ./scripts/ci/deploy.sh development gp-area-worker ./services/property-hub-kafka/ci/area-event-worker/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - label: ':codedeploy: PROPERTY EVENT WORKER - Deploy (STG)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/STG/deploy'
        if: build.branch == 'main'
        agents:
          queue: staging
        key: 'property-event-worker:deploy-staging'
        depends_on:
          - step: 'property-event-worker:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying PROPERTY EVENT WORKER (STG)"
          - ./scripts/ci/deploy.sh staging gp-property-worker ./services/property-hub-kafka/ci/property-event-worker/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - label: ':codedeploy: AREA EVENT WORKER - Deploy (STG)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/STG/deploy'
        if: build.branch == 'main'
        agents:
          queue: staging
        key: 'area-event-worker:deploy-staging'
        depends_on:
          - step: 'area-event-worker:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying PROPERTY EVENT WORKER (STG)"
          - ./scripts/ci/deploy.sh staging gp-area-worker ./services/property-hub-kafka/ci/area-event-worker/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - block: ':question: Release Property Hub Kafka Service app to Production'
        if: build.branch == 'main'
        key: 'property-hub-kafka-service:deploy-production-check'
        prompt: 'Are you sure you want to release the Property Hub kafka service app to production?'
      - label: ':codedeploy: PROPERTY EVENT WORKER - Deploy (PROD)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/PROD/deploy'
        if: build.branch == 'main'
        agents:
          queue: production
        key: 'property-event-worker:deploy-production'
        depends_on:
          - step: 'property-event-worker:build'
          - step: 'property-hub-kafka-service:deploy-production-check'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying PROPERTY EVENT WORKER (PROD)"
          - ./scripts/ci/deploy.sh production gp-property-worker ./services/property-hub-kafka/ci/property-event-worker/lcd.yml
        plugins: *plugins
      - label: ':codedeploy: AREA EVENT WORKER - Deploy (PROD)'
        concurrency: 2
        concurrency_group: 'growth/propertyhubkafka/PROD/deploy'
        if: build.branch == 'main'
        agents:
          queue: production
        key: 'area-event-worker:deploy-production'
        depends_on:
          - step: 'area-event-worker:build'
          - step: 'property-hub-kafka-service:deploy-production-check'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying AREA EVENT WORKER (PROD)"
          - ./scripts/ci/deploy.sh production gp-area-worker ./services/property-hub-kafka/ci/area-event-worker/lcd.yml
        plugins: *plugins
