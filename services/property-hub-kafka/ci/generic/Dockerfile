# ====================================================================================
# Generic image for docker-compose as it doesn't use anything from real Dockerfiles
# ====================================================================================

FROM 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/lendi-alpine-base:alpine-3.20--node-22

ARG NPM_TOKEN
ARG LENDI_NPM_TOKEN
ARG LENDI_NPM_PROXY

WORKDIR /usr/growth-product
COPY ./ ./

RUN rm -rf /usr/growth-product/apps
RUN rm -rf /usr/growth-product/packages/theme
RUN rm -rf /usr/growth-product/node_modules
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NX_DAEMON=false
ENV NX_REJECT_UNKNOWN_LOCAL_CACHE=0
ENV BUILD_KITE_BRANCH ${BUILD_KITE_BRANCH}
ENV BUILDKITE_COMMIT ${BUILDKITE_COMMIT}
ENV NPM_TOKEN ${NPM_TOKEN}
ENV LENDI_NPM_TOKEN ${LENDI_NPM_TOKEN}
ENV LENDI_NPM_PROXY ${LENDI_NPM_PROXY}

# LZ4 compression needs python to build
RUN apk add --no-cache bash git gcc make openssh curl g++ python3 jq

RUN corepack enable
RUN corepack prepare pnpm@9.5.0 --activate
RUN echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
RUN pnpm install
RUN pnpm remove @mantine/carousel @mantine/charts @mantine/code-highlight @mantine/core @mantine/dates @mantine/form @mantine/hooks @mantine/modals @mantine/notifications @mantine/nprogress @mantine/spotlight
RUN pnpm remove @copilotkit/react-core @copilotkit/react-ui
RUN pnpm remove recharts @datadog/browser-rum framer-motion react react-dom launchdarkly-react-client-sdk react-intl embla-carousel-react react-router-dom react-router

# make sure lz4 binary is rebuilt for linux (if not running yarn install)
RUN pnpm rebuild lz4