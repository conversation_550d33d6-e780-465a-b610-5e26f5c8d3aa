resource "datadog_monitor" "worker_throughput" {
  name    = "${local.worker_name}: Throughput deviation"
  type    = "query alert"
  message = "Alert! Throughput deviation > 1 in the last 15 mins ${var.slack_channel}"

  query = "avg(last_4h):anomalies(sum:trace.${var.trace_name}_handleKafkaMessage.hits{service:${local.worker_name},env:${var.environment}}.as_count(), 'basic', 3, direction='both', alert_window='last_15m', interval=60, count_default_zero='true') >= 1"

  monitor_thresholds {
    critical            = 1
    critical_recovery   = 0
  }

  tags = ["env:${var.environment}","service:${local.worker_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_error_query" {
  name    = "${local.worker_name}: Service Error"
  type    = "log alert"
  message = "Alert! more than 10 errors in last 5 mins ${var.slack_channel}"

  query = "logs(\"service:${local.worker_name} status:error -@error:(\\\"The group is rebalancing, so a rejoin is needed\\\" OR \\\"The group member needs to have a valid member id before actually entering a consumer group\\\")\").index(\"*\").rollup(\"count\").last(\"5m\") > 10"

  monitor_thresholds {
    critical    = 10
  }

  tags = ["env:${var.environment}","service:${local.worker_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_apm_anomaly" {
  name    = "${local.worker_name}: High p90 latency"
  type    = "query alert"
  message = "`${local.worker_name}`latency.90th percentile latency is too high` ${var.slack_channel}"

  query = "avg(last_10m):avg:trace.notification_worker_handleKafkaMessage.duration.by.service.90p{env:${var.environment},service:${local.worker_name}} > 0.15"

  monitor_thresholds {
    critical            = 0.15
    critical_recovery   = 0
  }

  tags = ["env:${var.environment}","service:${local.worker_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_group_rebalance" {
  name    = "${local.worker_name}: non-service error"
  type    = "log alert"
  message = "Non-service error Alert! more than 5 errors in last 5 mins ${var.slack_channel}"
  
  query = "logs(\"service:${local.worker_name} status:error @error:(\\\"The group is rebalancing, so a rejoin is needed\\\" OR \\\"The group member needs to have a valid member id before actually entering a consumer group\\\")\").index(\"*\").rollup(\"count\").last(\"5m\") > 5"

  monitor_thresholds {
    critical    = 5
  }

  tags = ["env:${var.environment}","service:${local.worker_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_consumer_group_lag" {
  name    = "${local.worker_name}: Consumer Group Lag"
  type    = "query alert"
  message = "${local.worker_name} consumer group lag is greater than 10. ${var.slack_channel}"

  query = "avg(last_5m):avg:instaclustr.consumerGroupLag.count{consumergroup:${local.worker_name}} by {topic} >= 10"

  monitor_thresholds {
    critical    = 10
    warning     = 5
  }

  tags = ["env:${var.environment}","service:${local.worker_name}","team:${var.team}","project:${var.project}"]
}