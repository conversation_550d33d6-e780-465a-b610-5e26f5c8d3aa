variable "environment" {
  type        = string
  description = "Must be one of: development|staging|production"
  default     = "development"
}

variable "project" {
  type        = string
  description = "The name of your project/service in ECS"
}


variable "service_name" {
  type    = string
  default = ""
}

variable "slack_channel" {
  type        = string
  description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
  type    = string
  default = "growth"
}

variable "trace_name" {
  type        = string
  description = "trace name defined at the service level"
}


locals {
  worker_name_raw = "${var.service_name}-${var.environment}"
  worker_name = lower(local.worker_name_raw)
}