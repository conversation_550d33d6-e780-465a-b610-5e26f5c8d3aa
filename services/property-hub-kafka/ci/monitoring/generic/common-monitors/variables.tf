variable "environment" {
  type        = string
  description = "Must be one of: development|staging|production"
  default     = "development"
}

variable "project" {
  type        = string
  description = "The name of your project/service in ECS"
}

variable "slack_channel" {
  type        = string
  description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
  type    = string
  default = "growth"
}

locals {
  namespace = "notification-service-latest"
  service_name_raw = "${var.project}-${var.environment}"
  service_name = "${lower(local.service_name_raw)}"
}