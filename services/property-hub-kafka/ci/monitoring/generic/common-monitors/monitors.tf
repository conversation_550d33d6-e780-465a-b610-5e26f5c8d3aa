resource "datadog_monitor" "k8s-container-restart" {
  name    = "${local.service_name}: k8s-container-restart"
  type    = "query alert"
  message = "High number of pod restarts ${var.slack_channel}"
    
  query   = "sum(last_5m):avg:kubernetes_state.container.restarts{namespace:${local.namespace},env:${var.environment}} by {pod_name} > 5"

  monitor_thresholds {
    critical          = 5
    critical_recovery = 0
    warning           = 2
    warning_recovery  = 1
  }

  tags = ["env:${var.environment}","service:${local.service_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "service-logs-count" {
  name    = "${local.service_name}: service-logs-count"
  type    = "log alert"
  message = "Too many logs ${var.slack_channel} - service:${local.service_name}"
    
  query = "logs(\"kube_namespace:${local.namespace} env:${var.environment}\").index(\"*\").rollup(\"count\").by(\"service\").last(\"10m\") > 900"

  monitor_thresholds {
    critical          = 900
    warning           = 600
  }

  tags = ["env:${var.environment}","service:${local.service_name}","team:${var.team}","project:${var.project}"]
}