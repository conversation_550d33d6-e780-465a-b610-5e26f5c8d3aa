locals {
    slack_channel = "@slack-Datadog-${replace(var.slack_channel, "^#","")}"
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
    default     = "development"
}
variable "project" {
    type        = string
    description = "The name of your project/service in ECS"
}

variable "state_bucket" {
    type        = string
    description = "The S3 bucket where the state file will be stored"
}

variable "state_file" {
    type        = string
    description = "The location where the state file will be stored"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
    type        = string
    description = "The team maintaining the datadog alert"
    default     = "growth"
}
