variable "environment" {
  type        = string
  description = "Must be one of: development|staging|preproduction|production"
  default     = "development"
}

variable "project" {
  type        = string
  description = "The name of your project/service in ECS"
}

variable "slack_channel" {
  type        = string
  description = "The slack channel integration where you'll be receiving monitoring notifications"
}