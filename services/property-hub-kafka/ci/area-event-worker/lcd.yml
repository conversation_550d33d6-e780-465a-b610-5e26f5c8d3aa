team: growth

containers:
  - image: ${LENDI_ECR_REGISTRY}/${PROJECT}:${BUILDKITE_BUILD_NUMBER}
    name: area-worker
    access: none
    memory: 256
    healthCheck:
      - CMD-SHELL
      - node services/property-hub-kafka/dist/area-worker/assets/healthCheck.js || exit 1
    environment:
      development:
        SERVICE_ENVIRONMENT: development
        KAFKAJS_LOG_LEVEL: warn
        KAFKA_BROKERS: 10.225.43.107:9093,10.225.93.52:9093,10.225.171.232:9093
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '3'
      staging:
        SERVICE_ENVIRONMENT: staging
        KAFKAJS_LOG_LEVEL: warn
        KAFKA_BROKERS: 10.226.41.195:9093,10.226.109.172:9093,10.226.160.119:9093
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '10'
      production:
        SERVICE_ENVIRONMENT: production
        KAFKAJS_LOG_LEVEL: error
        KAFKA_BROKERS: *************:9093,*************:9093,*************:9093
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '10'
      default:
        KAFKA_USERNAME: property-hub-worker
        KAFKA_REQUIRE_SSL: 'true'
        WORKER_NAME: gp-area-worker
        KAFKA_TOPIC_NAME: area-event
        AWS_REGION: ap-southeast-2
        CONSUMER_NAME: AREA
        PROPERTY_HUB_DATABASE_HOST: growth-product/property-hub/rds/root/host
        PROPERTY_HUB_DATABASE_APP_PASSWORD: growth-product/property-hub/rds/root/password
    secrets:
      default:
        KAFKA_PASSWORD: kafka-users/property-hub-worker/password
        LAUNCHDARKLY_SDK_KEY: growth-product/launchdarkly/sdk-key
ecsTaskRoleStatements:
  - Effect: Allow
    Action:
      - 'rds-db:connect'
    Resource:
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-RGYOMU7FCANHWGTRIURPPIATBI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-AAJVPYQ5QHRJGXN5ENZ6LVWTZI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-T7D5GZGZFETTTY6DENT7HW3KWY/property_hub
