team: growth
desiredCount: 1
containers:
  - image: ${LENDI_ECR_REGISTRY}/${PROJECT}:${BUILDKITE_BUILD_NUMBER}
    name: app
    access: none
    memory:
      default: 1024
    cpu:
      default: 500
    healthCheck:
      - CMD-SHELL
      - exit 0
    environment:
      default:
        LSD_URL: ${LSD_URL}
        LSD_TAG: ${tag}
        LSD_BUCKET_NAME: platform-static-${ENVIRONMENT}
        LSD_BUCKET_KEY: ${LSD_BUCKET_KEY}
        LENDI_DISTRIBUTION_ID: ${LENDI_DISTRIBUTION_ID}
      development:
        SERVICE_ENVIRONMENT: development
        BASE_URL: 'https://www.aussie-dev.com.au'
      staging:
        SERVICE_ENVIRONMENT: staging
        BASE_URL: 'https://www.aussie-stg.com.au'
      production:
        SERVICE_ENVIRONMENT: production
        BASE_URL: 'https://www.aussie.com.au'
    secrets:
      default:
        PROPERTY_HUB_DATABASE_HOST: growth-product/property-hub/rds/root/host
        PROPERTY_HUB_DATABASE_APP_PASSWORD: growth-product/property-hub/rds/root/password
ecsTaskRoleStatements:
  - Effect: Allow
    Action:
      - 'rds-db:connect'
    Resource:
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-RGYOMU7FCANHWGTRIURPPIATBI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-AAJVPYQ5QHRJGXN5ENZ6LVWTZI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-T7D5GZGZFETTTY6DENT7HW3KWY/property_hub
  - Effect: Allow
    Action:
      - 's3:ListBucket'
      - 's3:GetBucketLocation'
    Resource:
      - arn:aws:s3:::platform-static-${ENVIRONMENT}
  - Effect: Allow
    Action:
      - 's3:PutObject'
      - 's3:GetObject'
      - 's3:DeleteObject'
      - 's3:PutObjectTagging'
      - 's3:GetObjectTagging'
      - 's3:DeleteObjectTagging'
    Resource:
      - arn:aws:s3:::platform-static-${ENVIRONMENT}/${LSD_BUCKET_KEY}/*
  - Effect: Allow
    Action:
      - 'cloudfront:CreateInvalidation'
    Resource:
      - arn:aws:cloudfront:*:*:distribution/${LENDI_DISTRIBUTION_ID}
