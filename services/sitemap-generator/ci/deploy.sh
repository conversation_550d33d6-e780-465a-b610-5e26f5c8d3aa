#!/usr/bin/env bash
environment=$1;
project=$2;
config_file=$3;

if [[ "$BUILDKITE_BRANCH" = "main" ]]; then
    tag="latest"
else
    tag=$BUILDKITE_BRANCH
fi

if [ "$env" = "production" ]; then
  distributionId="EORMIILIG8GVZ"
elif [ "$env" = "staging" ]; then
  distributionId="E1R929GQ85NSC2"
else
  distributionId="E13MEVTPULC1KO"
fi

branch_lowercase=$(echo "$tag" | tr '[A-Z]' '[a-z]')
export PROJECT=${project}
export tag=$branch_lowercase
export ENVIRONMENT=${environment}

export LSD_URL=$(lsd url --environment $environment --project aussie-property-sitemap --tag $tag)
export LSD_BUCKET_NAME="platform-static-${ENVIRONMENT}"
export LSD_BUCKET_KEY=$(echo "${LSD_URL%/}" | cut -d/ -f4-5)
export LENDI_DISTRIBUTION_ID=${distributionId}

lcd deploy --environment $environment --project $project --tag $tag --config-file $config_file
