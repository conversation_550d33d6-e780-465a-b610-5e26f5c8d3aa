
# ==================================================================================
# Build image
# =================================================================================
FROM 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/lendi-alpine-base:alpine-3.20--node-22 AS build-image

ARG NPM_TOKEN
ARG LENDI_NPM_TOKEN
ARG LENDI_NPM_PROXY

WORKDIR /usr/growth-product

COPY ./ ./

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NX_DAEMON=false
ENV NX_REJECT_UNKNOWN_LOCAL_CACHE=0
ENV BUILD_KITE_BRANCH ${BUILD_KITE_BRANCH}
ENV BUILDKITE_COMMIT ${BUILDKITE_COMMIT}
ENV NPM_TOKEN ${NPM_TOKEN}
ENV LENDI_NPM_TOKEN ${LENDI_NPM_TOKEN}
ENV LENDI_NPM_PROXY ${LENDI_NPM_PROXY}

RUN set -euo pipefail
RUN apk add --no-cache bash git openssh curl g++ make python3 jq
RUN corepack enable
RUN corepack prepare pnpm@9.5.0 --activate
RUN echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
RUN pnpm install
RUN pnpm run sitemap:build

FROM 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/lendi-alpine-base:alpine-3.20--node-22 AS deploy-img

WORKDIR /usr/growth-product

RUN set -euo pipefail
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NX_DAEMON=false
ENV NX_REJECT_UNKNOWN_LOCAL_CACHE=0
ENV BUILD_KITE_BRANCH ${BUILD_KITE_BRANCH}
ENV BUILDKITE_COMMIT ${BUILDKITE_COMMIT}
ENV NODE_ENV production

ENV COREPACK_DEFAULT_TO_LATEST=0

RUN corepack enable
RUN corepack prepare pnpm@9.5.0 --activate
RUN corepack pack pnpm@9.5.0

RUN addgroup -g 1001 -S appgroup && \
  adduser -u 1001 -S appuser -G appgroup

COPY --chown=appuser:appgroup --from=build-image /usr/growth-product/ /usr/growth-product/
RUN chmod +x ./services/sitemap-generator/ci/entrypoint.sh
RUN chmod +x ./services/sitemap-generator/dist/*

USER appuser

ENTRYPOINT ["./services/sitemap-generator/ci/entrypoint.sh"]