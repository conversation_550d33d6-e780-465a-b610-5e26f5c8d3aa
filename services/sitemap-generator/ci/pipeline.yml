plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: 'NPM_TOKEN'
  - lendi-au/ssm#0.5.5:
      ssmkey: 'platform/NPM_TOKEN'
      exportname: LENDI_NPM_TOKEN
  - lendi-au/npm-global#1.1.0:
      env: 'NPM_TOKEN'
      package: '@lendi/liam'
  - lendi-au/npm-global#1.1.0:
      env: 'NPM_TOKEN'
      package: '@lendi/lsd'
  - docker#v3.7.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest # this image has the tools baked in already
      workdir: /app
      environment:
        - 'NPM_TOKEN'
        - 'LENDI_NPM_TOKEN'
        - 'BUILDKITE_BRANCH'
        - 'BUILDKITE_BUILD_NUMBER'
        - 'LENDI_ECR_REGISTRY'
        - 'DATADOG_API_KEY'
        - 'DATADOG_APP_KEY'
      propagate-environment: true

steps:
  - group: '🎯 AUSSIE Sitemap'
    key: 'sitemap-generator'
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ':docker: sitemap - Build'
        agents:
          queue: build
        key: 'sitemap-generator:build'
        commands:
          - echo "⏳ Building Sitemap Generator"
          - ./scripts/ci/build-image.sh aussie-sitemap-generator ./services/sitemap-generator/ci/Dockerfile
          - echo "🚀 Pushing Image for Sitemap Generator"
          - ./scripts/ci/push-image.sh aussie-sitemap-generator
        plugins:
          - lendi-au/ssm#0.5.5:
              ssmkey: 'NPM_TOKEN'
          - lendi-au/ssm#0.5.5:
              ssmkey: 'platform/NPM_TOKEN'
              exportname: LENDI_NPM_TOKEN
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lcd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lsd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/liam'
      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ':codedeploy: Sitemap Generator - Deploy (DEV)'
        agents:
          queue: development
        key: 'sitemap-generator:deploy-development'
        depends_on:
          - step: 'sitemap-generator:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Sitemap Generator (DEV)"
          - ./services/sitemap-generator/ci/deploy.sh development aussie-sitemap-generator ./services/sitemap-generator/ci/lcd.yml
        plugins: *plugins
      - label: ':codedeploy: Sitemap Generator - Deploy (STG)'
        if: build.branch == 'main'
        agents:
          queue: staging
        key: 'sitemap-generator:deploy-staging'
        depends_on:
          - step: 'sitemap-generator:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Sitemap Generator (STG)"
          - ./services/sitemap-generator/ci/deploy.sh staging aussie-sitemap-generator ./services/sitemap-generator/ci/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - block: ':question: Release Sitemap Generator app to Production'
        if: build.branch == 'main'
        key: 'sitemap-generator:deploy-production-check'
        prompt: 'Are you sure you want to release the Sitemap Generator app to production?'
      - label: ':codedeploy: Sitemap Generator - Deploy (PROD)'
        if: build.branch == 'main'
        agents:
          queue: production
        key: 'sitemap-generator:deploy-production'
        depends_on:
          - step: 'sitemap-generator:build'
          - step: 'sitemap-generator:deploy-production-check'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Sitemap Generator (PROD)"
          - ./services/sitemap-generator/ci/deploy.sh production aussie-sitemap-generator ./services/sitemap-generator/ci/lcd.yml
        plugins: *plugins
