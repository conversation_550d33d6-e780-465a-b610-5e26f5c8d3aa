# kafka-service

The kafka service for both kafka area event and property event

## Structure

`/scripts` - Contains misc scripts for building and deploying the service.

`/build` - Contains the dockerfiles that are build as part of the deployable artifacts of this repo.

`/envs` - Contains all envs for different environments.

`/examples` - Contains examples for different events.

`/src` - Contains all source code that forms the shared and deployable artifacts of the repo.

`/src/{type}-worker` - Contains generic code and configuration which is used by any specialised worker.

`/src/types` - All kafka types. This is a temp folder with type files generated by kafka build. Should be removed after kafka topics published

`/infrastructure` - Contains templates for deploying the infrastructure and monitors required to run the service.

`/infrastructure/monitoring/generic` - Contains a general set of monitors required by each service.

## Building the whole repo

Please use `nvm use --lts` for install the repo (nodeJS v22)

### Starting the worker application (Locally)

This only lists down the prerequisites and commands to test the worker locally as of date. The document will be kept to up-to-date as we progress working on the boilerplate to create an actual cms-service consuming the real store-broker-events

#### Prerequisites

1. Install docker desktop for your OS [MAC](https://docs.docker.com/desktop/mac/install/), [WINDOWS](https://docs.docker.com/desktop/windows/install/). For more information please refer [DOCKER DOCUMENTATION](https://docs.docker.com/desktop/)
2. Once installed, make sure it is running. To know if it's running or not, open the terminal and type command

```
docker ps
```

The above command will give a list of all the running docker containers/ processes. If docker is not running the terminal will throw the below error:
`Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?`

3. Next step is to locally install [Kafka](https://formulae.brew.sh/formula/kafka). This will allow us to run `kafka producer` to test our `kafka consumer`. For more info read [KAFKA DOCUMENTATION](https://kafka.apache.org/documentation/)

4. `lazydocker` is commandline tool to monitor and manage docker containers

5. `nvm` is commandline tool to manage node versions

#### Build and run docker images

1. get your development poweruser role in slack lendi-trail channel,
2. setup your local DB. please setup ur local DB cuz all events you fired locally will update DB directly.
3. in libs/database/property-hub/src/db/client.ts update your local DB connection config and enable the host for `host.docker.internal`
4. run `docker:login` to login docker hub for accessing private docker images
5. make sure ur npm token is setup correctly, export NPM_TOKEN={your npm token}
6. run `pnpm build:docker:dev` This will build fresh docker image for local run. After that, `docker-compose up` is executed internally.
   - builds the `docker images` via `docker compose`,
   - starts `docker-compose`,
   - `builds` project through `pnpm` and
   - opens a watch `typescript compiler`.
7. another option is to run `pnpm start:docker:dev` This will execute `docker-compose up` only
8. service is up and running now
9. `pnpm stop:docker:dev` to stop cms-service

#### docker compose

- The `docker-compose.yaml` file spins up four containers

  - Generic Image
  - area-worker
  - property-worker
  - Zookeeper
  - Kafka

- We can look at them running in the `docker desktop` UI, under the `container` section or via `lazydocker` . Once the containers are ready, we can now start another terminal to pass events for our worker to consume

### how to get kafka_container_id

- your docker desktop Containers tab will provide all info you need.
- find your kafka container ID

#### Inject kafka message

In order to test the worker interaction with kafka, we fire events on local kafka instance using kafka-cli:

- open your docker desktop
- find your running kafka container in Containers tab
- open your kafka container, and go Exec tab
- check file ./opt/kafka_2.13-2.7.0/bin/kafka-console-producer.sh is existed
- prepare kafka event messages by
  - finding example data from services/property-hub-kafka/examples
  - copy the whole json
  - running a script in browser console `JSON.stringify(<the json obj you copied>)`
  - copy the result and make a new string: `"<id of your payload>":<json string of your payload>`
- run this command `./opt/kafka_2.13-2.7.0/bin/kafka-console-producer.sh --topic property-event --bootstrap-server localhost:9092 --property "parse.key=true" --property "key.separator=:" < message.json`
- checking https://docs.confluent.io/kafka/operations-tools/kafka-tools.html#kafka-console-producer-sh for how to produce message
- open your worker log on docker desktop and observe worker behaviour via log events
