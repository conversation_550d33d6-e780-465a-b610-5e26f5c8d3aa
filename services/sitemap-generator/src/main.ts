import { CloudFrontClient, CreateInvalidationCommand } from '@aws-sdk/client-cloudfront';
import chalk from 'chalk';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';
import pino from 'pino';
import { Readable } from 'stream';
import { pipeline } from 'stream/promises';
import { createGzip } from 'zlib';
import { getRegion, LendiEnvironment } from '@lendi/aws-environments';
import { setRequiredTags } from '@lendi/required-tags';
import satay from '@lendi/satay';

import { propertyHub } from '@gp/database/property-hub';

const logger = pino({
  name: 'sitemap-generator',
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
});

const BASE_URL = process.env.BASE_URL;
const environment =
  (process.env.SERVICE_ENVIRONMENT as LendiEnvironment) || LendiEnvironment.Development;
const output = logger.info.bind(logger);
const debug = logger.debug.bind(logger);

const generateSitemap = async (mainPath: string, tableName: string) => {
  logger.info('Generating sitemap...');
  const buildPath = path.join(__dirname, './build/');
  const entryPath = path.join(buildPath, `${mainPath}.xml`);
  if (!fs.existsSync(buildPath)) {
    fs.mkdirSync(buildPath, { recursive: true });
  }
  fs.writeFileSync(
    entryPath,
    '<?xml version="1.0" encoding="UTF-8"?><sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">'
  );
  const cursorName = `sitemap_${mainPath}_cursor`;
  await propertyHub.transaction(async (tx) => {
    await tx.execute(
      sql.raw(
        `DECLARE ${cursorName} NO SCROLL CURSOR FOR SELECT slug FROM ${tableName} ORDER BY id ASC`
      )
    );
    let page = 0;

    let hasNext = true;
    while (hasNext) {
      const filePath = path.join(buildPath, `${mainPath}-${page + 1}.xml`);

      const time = performance.now();
      const res = await tx.execute(sql.raw(`FETCH FORWARD 50000 FROM ${cursorName}`));
      logger.info(`Fetched ${res.rows.length} ${mainPath} in ${performance.now() - time} ms`);
      hasNext = res.rowCount > 0;
      if (res.rows.length > 0) {
        const gzip = createGzip();
        const destination = fs.createWriteStream(`${filePath}.gz`);
        const source = new Readable({
          async read() {
            this.push('<?xml version="1.0" encoding="UTF-8"?>');
            this.push('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');
            (res.rows as { slug: string }[]).forEach((property) => {
              const url = `${BASE_URL}/property/${property.slug
                .split('/')
                .map((s) => encodeURIComponent(s))
                .join('/')}/`;
              const lastModified = new Date().toISOString();
              this.push(
                `<url><loc>${url}</loc><lastmod>${lastModified}</lastmod><changefreq>weekly</changefreq><priority>0.5</priority></url>`
              );
            });
            this.push('</urlset>');
            this.push(null);
          },
          objectMode: true,
          encoding: 'utf8',
        });
        await pipeline(source, gzip, destination);
        page += 1;
        const link = `<sitemap><loc>${BASE_URL}/sitemaps/${mainPath}-${page}.xml.gz</loc><lastmod>${new Date().toISOString()}</lastmod></sitemap>`;
        fs.appendFileSync(entryPath, link);
      }
    }
  });
  fs.appendFileSync(entryPath, '</sitemapindex>');
};

const generateAllSitemaps = async () => {
  try {
    const res = await Promise.allSettled([
      generateSitemap('properties', 'properties_v2'),
      generateSitemap('states', 'states'),
      generateSitemap('councils', 'councils'),
      generateSitemap('suburbs', 'suburbs'),
    ]);
    if (res.every((r) => r.status === 'fulfilled')) {
      await uploadSitemap();
      logger.info('All sitemaps generated successfully.');
    }
  } catch (error) {
    logger.error(error, 'Error generating sitemaps');
    process.exit(1);
  }
};
interface VersionedS3Object {
  key: string;
  version: string;
}
const uploadSitemap = async () => {
  logger.info('Uploading sitemaps...');
  const buildPath = path.join(__dirname, './build/');
  const lsdUrl = process.env.LSD_URL;
  const lsdBucketName = process.env.LSD_BUCKET_NAME || 'platform-static--development';
  const lsdFolderKey = process.env.LSD_BUCKET_KEY;
  const doNotCacheRegex = RegExp('^$');
  const cacheWithRevalidationRegex = RegExp('(.*).xml|(.*).xml.gz');
  const filesNotMatchingCachePatternsRegex = RegExp('(^$)|((.*).xml|(.*).xml.gz)');
  const noCacheFiles: string[] = [];
  const uploadedObjects: VersionedS3Object[] = [];

  await new Promise<void>((resolve, reject) => {
    /*
        We're setting caching headers in S3 here because CloudFront doesn't allow you to add
        any additional headers to the response sent to the browser.
      */
    satay(
      lsdBucketName,
      [
        // upload files with no caching, by default this is all files suffixed with `.html`
        // (these generally have no fingerprint and the latest content must be served directly from the bucket by nginx)
        {
          include: doNotCacheRegex,
          prefix: lsdFolderKey,
          source: buildPath,
          params: {
            ACL: 'private',
            CacheControl: `no-store`,
          },
        },
        // new cache with revalidation feature after #im-540
        {
          include: cacheWithRevalidationRegex,
          prefix: lsdFolderKey,
          source: buildPath,
          params: {
            ACL: 'private',
            CacheControl: `s-maxage=0, proxy-revalidate, max-age=0, stale-while-revalidate=86400`,
          },
        },
        // upload every other file with caching
        {
          exclude: filesNotMatchingCachePatternsRegex,
          prefix: lsdFolderKey,
          source: buildPath,
          params: {
            ACL: 'private',
            CacheControl: `max-age=${60 * 60 * 24 * 365.25}, public`,
          },
        },
      ],
      {
        shouldCreateBucket: false,
        shouldConfigureBucket: false,
        shouldUploadUnmodifiedObjects: true,
        tags: setRequiredTags(
          environment,
          'aussie-property-sitemap',
          false,
          process.env.LSD_TAG,
          undefined,
          'lsd'
        ),
      }
    )
      .on('diff', ({ diff }) => {
        output();
        Object.keys(diff)
          .sort()
          .forEach((key) => {
            const filename = path.relative(lsdFolderKey, key);
            if (doNotCacheRegex.test(filename)) {
              noCacheFiles.push(`/${key}`);
            }
            output(
              chalk.bold(
                `  ⬆  ${filename}${
                  doNotCacheRegex.test(filename)
                    ? '  [not cached]'
                    : cacheWithRevalidationRegex.test(filename)
                    ? '  [cache with revalidation]'
                    : ''
                }`
              )
            );
          });
        output();
        output(`⬆  Deploying files...`);
      })
      .on('object:upload', (uploadKey, uploadData) => {
        if (uploadData.progress !== 100) return;

        if (!uploadData.version) {
          debug();
          debug(`Key:${uploadKey}: Object missing VersionId`);
        } else {
          uploadedObjects.push({ key: uploadKey, version: uploadData.version });
        }
      })
      .on('object:delete', (deleteKey, deleteData) => {
        if (deleteData.progress !== 100) return;
        uploadedObjects.push({ key: deleteKey, version: 'DELETE_MARKER' });
      })
      .on('done', async () => {
        output();
        output(`✅ Deploying complete.`);
        output();
        output(`🔗 ${chalk.underline(chalk.blue(lsdUrl))}`);
        output();
        resolve();
      })
      .on('error', (error) => reject(error));
  });

  const cloudfront: CloudFrontClient = new CloudFrontClient({ region: getRegion() });
  const distributionId = process.env.LENDI_DISTRIBUTION_ID;
  const params = {
    DistributionId: distributionId,
    InvalidationBatch: {
      CallerReference: Date.now().toString(),
      Paths: {
        Quantity: noCacheFiles.length,
        Items: noCacheFiles,
      },
    },
  };
  if (noCacheFiles.length > 0) {
    debug('found files we should not cache - creating invalidation as a precaution');
    const command = new CreateInvalidationCommand(params);
    await cloudfront.send(command).catch((err) => output(err, err.stack));
  }
};

generateAllSitemaps().then(() => logger.info('running'));
