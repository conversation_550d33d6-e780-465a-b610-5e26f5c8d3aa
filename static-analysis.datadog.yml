schema-version: v1
rulesets:
  - tsx-react
  - typescript-best-practices
  - typescript-browser-security
  - typescript-code-style
  - typescript-common-security
  - typescript-express
  - typescript-inclusive
  - typescript-node-security
  - github-actions
  - github-actions-best-practices
  - github-actions-security
  - github-actions-performance
  - github-actions-reliability
  - github-actions-cost-optimization
  - github-actions-monitoring
  - github-actions-logging
  - github-actions-security
  - github-actions-performance
