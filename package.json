{"name": "@gp/source", "version": "0.0.0", "license": "MIT", "scripts": {"prepare": "husky", "commit": "czg", "lendi:dev": "nx run lendi:dev | pino-pretty", "lendi:build": "nx run lendi:build", "lendi:start": "nx run lendi:start", "lendi:generate-local-env": "bash scripts/apps/lendi/generate-local-env.sh", "aussie:dev": "nx run aussie:dev | pino-pretty", "aussie:build": "nx run aussie:build", "aussie:start": "nx run aussie:start", "aussie:generate-local-env": "bash scripts/apps/aussie/generate-local-env.sh", "stores-and-brokers-api:dev": "nx run stores-and-brokers-api:serve | pino-pretty", "stores-and-brokers-api:build": "nx run stores-and-brokers-api:build", "stores-and-brokers-api:start": "nx run stores-and-brokers-api:start", "property-hub-api:dev": "nx run property-hub-api:serve | pino-pretty", "property-hub-api:build": "nx run property-hub-api:build", "property-hub-api:start": "nx run property-hub-api:start", "property-hub-db:generate": "nx run database/property-hub:drizzle-generate", "property-hub-db:migration": "nx run database/property-hub:drizzle-migrate", "property-hub-db:drizzle-studio": "nx run database/property-hub:drizzle-studio", "property-hub-db:tunnel-connect": "bash scripts/database/property-hub/setup-tunnel-connection.sh", "property-hub-kafka:build": "nx run property-hub-kafka:build", "property-hub-kafka:serve": "nx run property-hub-kafka:serve | pino-pretty --colorize", "property-hub-kafka:start": "nx run property-hub-kafka:start", "refinance:dev": "nx run refinance-funnel:serve", "refinance:build": "nx run refinance-funnel:build", "new-purchase:dev": "nx run new-purchase-funnel:serve", "new-purchase:build": "nx run new-purchase-funnel:build", "home-loan-health-check:dev": "nx run home-loan-health-check:serve", "home-loan-health-check:build": "nx run home-loan-health-check:build", "theme:build": "nx run theme:build", "theme:dev": "nx run theme:serve", "start:docker": "bash scripts/docker/start-docker-compose.sh", "build:docker:dev": "bash scripts/docker/start-docker-compose.sh -b", "start:docker:dev": "bash scripts/docker/start-docker-compose.sh", "stop:docker:dev": "docker-compose -f services/property-hub-kafka/docker-compose.yml down", "docker:login": "lcd docker:login", "icons:build": "svgr libs/ui/icons/assets && eslint libs/ui/icons/src --fix", "sitemap:build": "nx run sitemap-generator:build", "sitemap:run": "nx run sitemap-generator:run"}, "private": true, "dependencies": {"@asteasolutions/zod-to-openapi": "^7.2.0", "@aws-sdk/client-cloudfront": "^3.767.0", "@aws-sdk/client-dynamodb": "^3.767.0", "@aws-sdk/client-s3": "^3.767.0", "@aws-sdk/lib-dynamodb": "^3.767.0", "@aws-sdk/lib-storage": "^3.767.0", "@aws-sdk/rds-signer": "^3.637.0", "@aws-sdk/util-dynamodb": "^3.767.0", "@contentful/rich-text-react-renderer": "^15.19.6", "@contentful/rich-text-types": "^16.3.5", "@copilotkit/react-core": "^1.3.15", "@copilotkit/react-ui": "^1.3.15", "@copilotkit/runtime": "^1.3.15", "@datadog/browser-rum": "^6.10.1", "@datadog/browser-rum-react": "^6.10.1", "@googlemaps/js-api-loader": "^1.16.8", "@launchdarkly/node-server-sdk": "^9.9.2", "@lendi/analytics-web": "^3.4.1", "@lendi/analytics-web-v4": "npm:@lendi/analytics-web@latest", "@lendi/auth": "^0.1.11", "@lendi/aws-environments": "^5.3.0", "@lendi/config": "2.0.0", "@lendi/core-constants": "^10.10.2", "@lendi/core-kafka-client": "^9.3.0", "@lendi/kafka-topics-property-hub": "^1.1.3", "@lendi/lala-react": "^13.1.2", "@lendi/lala-utils": "^12.0.2", "@lendi/lendigroup-leads-library": "^3.6.0", "@lendi/required-tags": "^1.17.0", "@lendi/satay": "^2.6.0", "@mantine/carousel": "^7.16.1", "@mantine/charts": "^7.16.1", "@mantine/code-highlight": "^7.16.1", "@mantine/core": "^7.16.1", "@mantine/dates": "^7.16.1", "@mantine/form": "^7.16.1", "@mantine/hooks": "^7.16.1", "@mantine/modals": "^7.16.1", "@mantine/notifications": "^7.16.1", "@mantine/nprogress": "^7.16.1", "@mantine/spotlight": "^7.16.1", "@nestjs/common": "^10.4.5", "@nestjs/core": "^10.4.5", "@nestjs/platform-express": "^10.4.5", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.4.2", "@nx/cypress": "19.2.3", "@nx/devkit": "19.2.3", "@nx/eslint": "19.2.3", "@nx/eslint-plugin": "19.2.3", "@nx/jest": "19.2.3", "@nx/js": "19.2.3", "@nx/nest": "19.2.3", "@nx/next": "19.2.3", "@nx/node": "19.2.3", "@nx/react": "19.2.3", "@nx/web": "19.2.3", "@nx/webpack": "19.2.3", "@nx/workspace": "19.2.3", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "buffer": "^6.0.3", "chalk": "^5.4.1", "clsx": "^2.1.0", "contentful": "^9.1.34", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "dd-trace": "^5.25.0", "drizzle-orm": "^0.36.3", "embla-carousel-react": "^8.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-simple-import-sort": "^12.0.0", "express": "^4.21.1", "framer-motion": "^11.3.21", "helmet": "^8.0.0", "html-webpack-plugin": "^5.6.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "launchdarkly-js-client-sdk": "^3.7.0", "launchdarkly-js-sdk-common": "^5.6.0", "launchdarkly-react-client-sdk": "^3.7.0", "mantine-form-zod-resolver": "^1.1.0", "nestjs-ddtrace": "^5.0.0", "nestjs-pino": "^4.1.0", "next": "14.2.25", "node-fetch": "^3.3.2", "nx": "19.2.3", "openai": "^4.68.4", "pg": "^8.12.0", "pino": "^9.5.0", "pino-http": "^10.3.0", "pino-pretty": "^12.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-intl": "^6.6.6", "react-router": "6", "react-router-dom": "6", "recharts": "2.13.0-alpha.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "swr": "^2.2.5", "ts-jest": "^29.1.0", "tslib": "^2.3.0", "typescript": "~5.4.2", "uuid": "^9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@commitlint/config-nx-scopes": "^19.2.1", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.5", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/cli": "^8.1.0", "@svgr/webpack": "^8.0.1", "@swc-node/register": "1.9.2", "@swc/cli": "0.3.12", "@swc/core": "1.5.7", "@swc/helpers": "0.5.11", "@swc/jest": "~0.2.36", "@testing-library/react": "15.0.6", "@types/express": "^5.0.0", "@types/geojson": "^7946.0.14", "@types/jest": "^29.4.0", "@types/js-cookie": "^3.0.6", "@types/node": "18.16.9", "@types/node-fetch": "^2.6.11", "@types/pg": "^8.11.8", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/uuid": "^9.0.8", "babel-jest": "^29.4.1", "core-js": "*", "cypress": "13.9.0", "cz-git": "^1.9.1", "czg": "^1.9.1", "drizzle-kit": "^0.28.1", "eslint": "~8.57.0", "eslint-config-next": "14.2.25", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "husky": "^9.0.11", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-environment-node": "^29.4.1", "jsdom": "~22.1.0", "lint-staged": "^15.2.2", "postcss": "8.4.38", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "ts-node": "10.9.1", "webpack-cli": "^5.1.4"}, "resolutions": {"multer": "^2.0.1", "jwt-decode": "3.1.2"}, "config": {"commitizen": {"path": "node_modules/cz-git", "czConfig": "cz.config.js"}}, "engines": {"node": ">=22.0.0"}, "packageManager": "pnpm@9.5.0"}