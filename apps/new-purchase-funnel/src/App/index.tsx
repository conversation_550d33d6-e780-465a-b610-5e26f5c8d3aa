import { lazy, Suspense, useState } from 'react';
import { Group, Stack, Text, Title } from '@mantine/core';
import { useDocumentTitle, useLocalStorage, useSessionStorage } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import {
  generateNewPurchaseLead,
  NewPurchase,
  setNewPurchaseFunnelOneFormValues,
} from '@gp/data-access/customer-funnel';
import { FunnelLayout } from '@gp/feature/funnels';
import { AlertWithTracking, ButtonWithTracking, PageTracking } from '@gp/ui/components';
import { ArrowBack, Error } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

const NewProperty = lazy(() =>
  import('@gp/feature/new-purchase').then((module) => ({ default: module.NewProperty }))
);
const BuyingSituation = lazy(() =>
  import('@gp/feature/new-purchase').then((module) => ({ default: module.BuyingSituation }))
);
const Preferences = lazy(() =>
  import('@gp/feature/new-purchase').then((module) => ({ default: module.Preferences }))
);
const PersonalDetails = lazy(() =>
  import('@gp/feature/new-purchase').then((module) => ({ default: module.PersonalDetails }))
);

const STEPS = [
  {
    title: 'Your new property',
    description: `Let's start with the basics of your new home. This information helps us find the perfect loan options for your new property.`,
    Component: NewProperty,
    alert: {
      header: 'Beginning your new home loan journey starts with 4 easy steps...',
      content:
        'Tell us about your new property, your buying situation, your preferences and details and we’ll provide you with some preliminary rate matches.',
      variant: 'default',
    },
  },
  {
    title: 'Your buying situation',
    description:
      'Understanding where you stand in your home buying journey allows us to tailor solutions specifically for you. ',
    Component: BuyingSituation,
    alert: undefined,
  },
  {
    title: 'Your preferences',
    description:
      "Your financial needs are unique, and your new home loan should be too. Share what's important to you in a lender, and we'll focus on finding options that align with your preferences.",
    Component: Preferences,
    alert: undefined,
  },
  {
    title: 'Your details',
    description:
      "Almost done! Just a few quick personal details to ensure our advice is tailored and relevant to you. We're excited to help you find the perfect home loan!",
    Component: PersonalDetails,
    alert: undefined,
  },
] as const;

const NewPurchaseFunnel = () => {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const [isPropertyOrigin] = useSessionStorage<boolean>({
    key: 'l3-PROPERTY_REPORT',
  });
  const [step, setStep] = useState(0);
  const [showAlert, setShowAlert] = useSessionStorage<boolean>({
    key: 'new-purchase-funnel-alert',
    defaultValue: true,
  });
  const [data, setData] = useLocalStorage<NewPurchase>({
    key: 'new-purchase-funnel',
    getInitialValueInEffect: false,
    defaultValue: {},
  });

  const { title, description, Component, alert } = STEPS[step];

  const handleNextStep = (data: NewPurchase) => {
    if (typeof window === 'undefined') return;

    setData(data);
    if (step < STEPS.length - 1) {
      setStep(step + 1);
    } else {
      setNewPurchaseFunnelOneFormValues(data);
      !isPropertyOrigin && generateNewPurchaseLead(data);
      const destination = embedMarketingTrackingParams(
        `${
          isAuthenticated ? '/home-loans/basics/landing/' : '/sign-in/sign-up/'
        }${generateReturnURL([
          ...(isAuthenticated ? [] : ['/home-loans/basics/landing/']),
          '/home-loans/basics/application/applicationidplaceholder/ctp/',
          '/home-loans/basics/application/applicationidplaceholder/applicant/applicantidplaceholder/',
        ])}`
      );
      if (process.env.NODE_ENV === 'development') {
        console.log('Redirecting to:', destination);
      } else {
        window.localStorage.removeItem('new-purchase-funnel');
        window.location.assign(destination);
      }
    }
  };

  useDocumentTitle(title);

  return (
    <PageTracking name="New Purchase Funnel 1" category="FUNNELS">
      <FunnelLayout.Progress progress={step / STEPS.length} />
      <FunnelLayout.FormContainer>
        {step > 0 && (
          <Group>
            <ButtonWithTracking
              leftSection={<ArrowBack />}
              label="Back"
              position={`Refinance - ${title}`}
              variant="transparent"
              size="compact-sm"
              onClick={() => setStep(step - 1)}
            />
          </Group>
        )}
        {alert && showAlert && (
          <AlertWithTracking
            title={alert.header}
            name={alert.header}
            variant={alert.variant}
            icon={<Error size={14} />}
            withCloseButton
            closeButtonLabel="Close"
            onClose={() => setShowAlert(false)}
          >
            {alert.content}
          </AlertWithTracking>
        )}
        <Stack gap={10}>
          <Title order={2} c="primary">
            {title}
          </Title>
          <Text size="lg">{description}</Text>
        </Stack>
        <Suspense fallback={null}>
          <Component
            formName={`New purchase - ${title}`}
            data={data}
            handleNextStep={handleNextStep}
          />
        </Suspense>
      </FunnelLayout.FormContainer>
    </PageTracking>
  );
};

export default function App() {
  return (
    <FunnelLayout>
      <NewPurchaseFunnel />
    </FunnelLayout>
  );
}
