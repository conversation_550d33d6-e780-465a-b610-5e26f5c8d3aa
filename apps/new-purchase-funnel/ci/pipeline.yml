plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: "NPM_TOKEN"
  - lendi-au/ssm#0.5.5:
      ssmkey: "platform/NPM_TOKEN"
      exportname: LENDI_NPM_TOKEN
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/lsd"
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/liam"
  - docker#v3.4.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest
      workdir: /app
      environment:
        - "NPM_TOKEN"
        - "LENDI_NPM_TOKEN"
        - "AWS_REGION"
        - "AWS_DEFAULT_REGION"
      propagate-environment: true

artifacts: &artifacts
  - dist/new-purchase-funnel/**/*
steps:
  - group: "🎯 New Purchase Funnel"
    key: "new-purchase-funnel"
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ":webpack: New Purchase Funnel - Build (DEV)"
        agents:
          queue: build
        key: "new-purchase:build-development"
        commands:
          - echo "⏳ Building New Purchase Funnel (DEV)"
          - ./apps/new-purchase-funnel/ci/build.sh development
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: New Purchase Funnel - Build (STG)"
        branches: 'main'
        agents:
          queue: build
        key: "new-purchase:build-staging"
        commands:
          - echo "⏳ Building New Purchase Funnel (STG)"
          - ./apps/new-purchase-funnel/ci/build.sh staging
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: New Purchase Funnel - Build (PRE-PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "new-purchase:build-preproduction"
        commands:
          - echo "⏳ Building New Purchase Funnel (PRE-PROD)"
          - ./apps/new-purchase-funnel/ci/build.sh preproduction
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: New Purchase Funnel - Build (PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "new-purchase:build-production"
        commands:
          - echo "⏳ Building New Purchase Funnel (PROD)"
          - ./apps/new-purchase-funnel/ci/build.sh production
        plugins: *plugins
        artifact_paths: *artifacts

      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ":codedeploy: New Purchase Funnel - Deploy (DEV)"
        agents:
          queue: development
        key: "new-purchase:deploy-development"
        depends_on:
          - step: "new-purchase:build-development"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying New Purchase Funnel (DEV)"
          - ./apps/new-purchase-funnel/ci/deploy.sh development
        plugins: *plugins

      - label: ":codedeploy: New Purchase Funnel - Deploy (STG)"
        if: build.branch == 'main'
        agents:
          queue: staging
        key: "new-purchase:deploy-staging"
        depends_on:
          - step: "new-purchase:build-staging"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying New Purchase Funnel (STG)"
          - ./apps/new-purchase-funnel/ci/deploy.sh staging
        plugins: *plugins

      - label: ":codedeploy: New Purchase Funnel - Deploy (PRE-PROD)"
        if: build.branch == 'main'
        agents:
          queue: preproduction
        key: "new-purchase:deploy-preproduction"
        depends_on:
          - step: "new-purchase:build-preproduction"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying New Purchase Funnel (PRE-PROD)"
          - ./apps/new-purchase-funnel/ci/deploy.sh preproduction
        plugins: *plugins

      - block: ':question: Release to Production'
        if: build.branch == 'main'
        key: "new-purchase:deploy-production-check"
        prompt: 'Are you sure you want to release to production?'

      - label: ":codedeploy: New Purchase Funnel - Deploy (PROD)"
        if: build.branch == 'main'
        agents:
          queue: production
        key: "new-purchase:deploy-production"
        depends_on:
          - step: "new-purchase:build-production"
          - step: "new-purchase:deploy-production-check"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying New Purchase Funnel (PROD)"
          - ./apps/new-purchase-funnel/ci/deploy.sh production
        plugins: *plugins
