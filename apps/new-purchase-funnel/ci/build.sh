#!/usr/bin/env bash
environment=$1;

source ./scripts/ci/setup-shell.sh
source ./scripts/ci/setup-pnpm.sh

if [[ "$BUILDKITE_BRANCH" = "main" ]]; then
    tag="latest"
else
    tag=$BUILDKITE_BRANCH
fi

if [ "$environment" = "production" ]; then
  echo "Production env"
  export NX_PUBLIC_ENVIRONMENT="production"
  export NX_PUBLIC_API_BASE_URL="https://api.lendi.com.au"
  export NX_PUBLIC_LAUNCH_DARKLY_API_KEY="****************************************"
  export NX_PUBLIC_LAUNCH_DARKLY_ENV_KEY="production"
  export NX_PUBLIC_LAUNCH_DARKLY_CLIENT_ID="5e5486f5f434d50824ef46b0"
elif [ "$environment" = "preproduction" ]; then
  echo "Preproduction env"
  export NX_PUBLIC_ENVIRONMENT="preproduction"
  export NX_PUBLIC_API_BASE_URL="https://api.lendi-preprod.net"
  export NX_PUBLIC_LAUNCH_DARKLY_API_KEY="****************************************"
  export NX_PUBLIC_LAUNCH_DARKLY_ENV_KEY="pre-prod"
  export NX_PUBLIC_LAUNCH_DARKLY_CLIENT_ID="61d4cd637fe8d014e78814fa"
elif [ "$environment" = "staging" ]; then
  echo "Staging env"
  export NX_PUBLIC_ENVIRONMENT="staging"
  export NX_PUBLIC_API_BASE_URL="https://api.lendi-stg.net"
  export NX_PUBLIC_LAUNCH_DARKLY_API_KEY="****************************************"
  export NX_PUBLIC_LAUNCH_DARKLY_ENV_KEY="test"
  export NX_PUBLIC_LAUNCH_DARKLY_CLIENT_ID="5e5486f5f434d50824ef46af"
else
  echo "Development env"
  export NX_PUBLIC_ENVIRONMENT="development"
  export NX_PUBLIC_API_BASE_URL="https://api.lendi-dev.net"
  export NX_PUBLIC_LAUNCH_DARKLY_API_KEY="****************************************"
  export NX_PUBLIC_LAUNCH_DARKLY_ENV_KEY="development"
  export NX_PUBLIC_LAUNCH_DARKLY_CLIENT_ID="5e54875ff434d50824ef46b6"
fi

# set env vars used by reactjs to build the app
export NODE_ENV=production
export EXPORT_ENV=$environment
export NX_PUBLIC_BASE_URL=`lsd url --environment $environment --project gp-new-purchase-funnel --tag $tag`
export RELEASE_VERSION=$BUILDKITE_COMMIT

# TODO: Generate sitemap
pnpm run new-purchase:build
mkdir -p dist/new-purchase-funnel/$environment
mv ./apps/new-purchase-funnel/dist/* dist/new-purchase-funnel/$environment/
