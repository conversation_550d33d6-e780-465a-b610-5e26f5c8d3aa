{"name": "new-purchase-funnel", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/new-purchase-funnel/src", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project new-purchase-funnel --web", "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/new-purchase-funnel/webpack.config.js", "outputPath": "apps/new-purchase-funnel/dist", "index": "apps/new-purchase-funnel/src/index.html", "main": "apps/new-purchase-funnel/src/main.tsx", "tsConfig": "apps/new-purchase-funnel/tsconfig.app.json", "assets": ["apps/new-purchase-funnel/src/growth-product-assets"], "postcssConfig": "apps/new-purchase-funnel/postcss.config.js"}}, "serve": {"executor": "@nx/webpack:dev-server", "options": {"buildTarget": "build", "hmr": true, "port": 5000}}, "preview": {"executor": "nx:run-commands", "options": {"command": "echo", "args": ["use \"pnpm nx run new-purchase-funnel:serve-static\" to preview the app"]}}, "serve-static": {"options": {"buildTarget": "build", "staticFilePath": "apps/new-purchase-funnel/dist", "port": 5555, "spa": true}}}}