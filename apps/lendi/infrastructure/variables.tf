locals {
  subdomain = "gp-lendi-latest" // actual lcd subdomain that is setup
  cloudfront = "growth-product-cloudfront" // cloudfront alias
  env_backend = {
    "development" = "${local.subdomain}.lendi-paas-dev.net"
    "staging" = "${local.subdomain}.lendi-paas-stg.net"
    "preproduction" = "${local.subdomain}.lendi-paas-preprod.net"
    "production" = "${local.subdomain}.lendi-paas.net"
  }
  cloudfront_alias = {
    "development" = "${local.cloudfront}.lendi-dev.net"
    "staging" = "${local.cloudfront}.lendi-stg.net"
    "preproduction" = "${local.cloudfront}.lendi-preprod.net"
    "production" = "${local.cloudfront}.lendi.com.au"
  }
  cloudfront_certs = {
    "development" = "arn:aws:acm:us-east-1:702880128631:certificate/5833126f-3001-4f69-bd03-c4c1ffc66415" // *.lendi-dev.net
    "staging" = "arn:aws:acm:us-east-1:106772905355:certificate/6078c1db-3cf1-4292-86d2-24a7ebc58926" // *.lendi-stg.net
    "preproduction" = "arn:aws:acm:us-east-1:226481772206:certificate/5b909431-14b4-46ea-9099-ee5abd4cf77d" // not legit
    "production" = "arn:aws:acm:us-east-1:362442693667:certificate/c3cf3370-1840-4437-8597-f67462fc4863" // lendi.com.au
  }
}

data "aws_ssm_parameter" "waf_allow_key" {
  name            = "/platform/waf/growth-product-allow-key"
  with_decryption = true
}

variable environment {
  type = string
  default = "development"
  validation {
    condition = can(regex("^(development|staging|preproduction|production)$", var.environment))
    error_message = "Invalid environment provided ${var.environment}"
  }
}