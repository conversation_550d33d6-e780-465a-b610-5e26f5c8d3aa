resource "aws_cloudfront_cache_policy" "cache" {
  name        = "growth-product-cache-policy"
  comment     = "url based cache with query string support"
  min_ttl     = 60 // 1 min
  max_ttl     = 31536000 // 1 year
  default_ttl = 86400 // 1 day
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "none"
    }
    query_strings_config {
      query_string_behavior = "whitelist"
      query_strings {
        items = ["_rsc"]
      }
    }
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
  }
}