# This gets attached to the task role of the containers running.
# Use this if you need to access any resources you create like S3, <PERSON><PERSON>, <PERSON>, etc.
# See AWS Documentation here: https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_examples_s3_rw-bucket.html

team: growth
containers:
  - image: ${LENDI_ECR_REGISTRY}/${PROJECT}:${BUILDKITE_BUILD_NUMBER}
    name: app
    port: 3000
    healthCheck: /
    access: public
    memory:
      default: 512
    cpu:
      default: 300
    environment:
      development:
        APP_ENVIRONMENT: development
        CONTENTFUL_ENVIRONMENT: development
        API_BASE_URL: 'https://api.lendi-dev.net'
        LAUNCHDARKLY_ENV_KEY: development
        AMPLITUDE_EXPERIMENT_API_KEY: ********************************
      staging:
        APP_ENVIRONMENT: staging
        CONTENTFUL_ENVIRONMENT: staging
        API_BASE_URL: https://api.lendi-stg.net
        LAUNCHDARKLY_ENV_KEY: 'test'
        AMPLITUDE_EXPERIMENT_API_KEY: ********************************
      production:
        APP_ENVIRONMENT: production
        CONTENTFUL_ENVIRONMENT: master
        API_BASE_URL: https://api.lendi.com.au
        LAUNCHDARKLY_ENV_KEY: production
        AMPLITUDE_EXPERIMENT_API_KEY: ********************************
    secrets:
      default:
        CONTENTFUL_SPACE_ID: growth-product/contentful/lendi/space-id
        CONTENTFUL_ACCESS_TOKEN: growth-product/contentful/lendi/access-token
        LAUNCHDARKLY_API_KEY: growth-product/launchdarkly/api-key
        LAUNCHDARKLY_CLIENT_ID: growth-product/launchdarkly/client-id
        LAUNCHDARKLY_SDK_KEY: growth-product/launchdarkly/sdk-key
    alternateHostnames:
      development:
        latest:
          - growth-product-cloudfront.lendi-dev.net
      staging:
        latest:
          - growth-product-cloudfront.lendi-stg.net
      production:
        latest:
          - growth-product-cloudfront.lendi.com.au
