
# ==================================================================================
# Build image
# =================================================================================
FROM harbor-core.lendi-paas-mgmt.net/public/library/node:lts-alpine AS build-image

ARG NPM_TOKEN
ARG LENDI_NPM_TOKEN
ARG LENDI_NPM_PROXY

WORKDIR /usr/growth-product

COPY ./ ./

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NX_DAEMON=false
ENV NX_REJECT_UNKNOWN_LOCAL_CACHE=0
ENV BUILD_KITE_BRANCH ${BUILD_KITE_BRANCH}
ENV BUILDKITE_COMMIT ${BUILDKITE_COMMIT}
ENV NPM_TOKEN ${NPM_TOKEN}
ENV LENDI_NPM_TOKEN ${LENDI_NPM_TOKEN}
ENV LENDI_NPM_PROXY ${LENDI_NPM_PROXY}
ENV RELEASE_VERSION ${BUILDKITE_COMMIT}

RUN set -euo pipefail
RUN apk add --no-cache bash git openssh curl g++ make python3 jq
RUN corepack enable
RUN corepack prepare pnpm@9.5.0 --activate
RUN echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > .npmrc
RUN pnpm install
RUN pnpm run lendi:build

# ==================================================================================
# Deployed image
# =================================================================================
FROM harbor-core.lendi-paas-mgmt.net/public/library/node:lts-alpine AS deploy-img

WORKDIR /usr/growth-product

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NX_DAEMON=false
ENV NX_REJECT_UNKNOWN_LOCAL_CACHE=0
ENV BUILD_KITE_BRANCH ${BUILD_KITE_BRANCH}
ENV BUILDKITE_COMMIT ${BUILDKITE_COMMIT}
ENV NODE_ENV production

ENV COREPACK_DEFAULT_TO_LATEST=0

ENV PORT 3000

RUN corepack enable
RUN corepack prepare pnpm@9.5.0 --activate
RUN corepack pack pnpm@9.5.0

RUN addgroup -g 1001 -S appgroup && \
  adduser -u 1001 -S appuser -G appgroup

COPY --chown=appuser:appgroup --from=build-image /usr/growth-product/ /usr/growth-product/

EXPOSE 3000

USER appuser

# Need to remove during upgrage to nextjs 15
RUN rm -rf ./usr/growth-product/apps/lendi/.next
RUN pnpm run lendi:build
# Need to remove during upgrage to nextjs 15

ENTRYPOINT pnpm run lendi:start
