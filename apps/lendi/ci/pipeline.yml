plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: "NPM_TOKEN"
  - lendi-au/ssm#0.5.5:
      ssmkey: "platform/NPM_TOKEN"
      exportname: LENDI_NPM_TOKEN
  - docker#v3.7.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest # this image has the tools baked in already
      workdir: /app
      environment:
        - 'NPM_TOKEN'
        - 'LENDI_NPM_TOKEN'
        - 'BUILDKITE_BRANCH'
        - 'BUILDKITE_BUILD_NUMBER'
        - 'LENDI_ECR_REGISTRY'
        - 'DATADOG_API_KEY'
        - 'DATADOG_APP_KEY'
      propagate-environment: true

steps:
  - group: "🎯 Growth Product - Lendi"
    key: "gp-lendi"
    steps:
      #   ____  _    _ _____ _      _____  
      #  |  _ \| |  | |_   _| |    |  __ \ 
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ":docker: Lendi - Build"
        agents:
          queue: build
        key: "lendi:build"
        commands:
          - echo "⏳ Building Lendi"
          - ./scripts/ci/build-image.sh gp-lendi ./apps/lendi/ci/Dockerfile
          - echo "🚀 Pushing Image for Lendi"
          - ./scripts/ci/push-image.sh gp-lendi
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins:
          - lendi-au/ssm#0.5.5:
              ssmkey: 'NPM_TOKEN'
          - lendi-au/ssm#0.5.5:
              ssmkey: 'platform/NPM_TOKEN'
              exportname: LENDI_NPM_TOKEN
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lcd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/liam'

      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ / 
      #  | | | |  __| | |  | | |    | | | |  \   /  
      #  | |_| | |____| |__| | |____| |_| |   | |   
      #  |____/|______|_____/|______|____/    |_|   
      #  ==========================================
      - label: ":codedeploy: Lendi - Deploy (DEV)"
        agents:
          queue: development
        key: "lendi:deploy-development"
        depends_on: 
          - step: "lendi:build"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Lendi (DEV)"
          - ./scripts/ci/deploy-infrastructure.sh development gp-lendi ./apps/lendi/infrastructure
          - ./scripts/ci/deploy.sh development gp-lendi ./apps/lendi/ci/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - label: ":codedeploy: Lendi - Deploy (STG)"
        if: build.branch == 'main'
        agents:
          queue: staging
        key: "lendi:deploy-staging"
        depends_on: 
          - step: "lendi:build"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Lendi (STG)"
          - ./scripts/ci/deploy-infrastructure.sh staging gp-lendi ./apps/lendi/infrastructure
          - ./scripts/ci/deploy.sh staging gp-lendi ./apps/lendi/ci/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins

      - block: ':question: Release Lendi app to Production'
        if: build.branch == 'main'
        key: "lendi:deploy-production-check"
        prompt: 'Are you sure you want to release the lendi app to production?'

      - label: ":codedeploy: Lendi - Deploy (PROD)"
        if: build.branch == 'main'
        agents:
          queue: production
        key: "lendi:deploy-production"
        depends_on: 
          - step: "lendi:build"
          - step: "lendi:deploy-production-check"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Lendi (PROD)"
          - ./scripts/ci/deploy-infrastructure.sh production gp-lendi ./apps/lendi/infrastructure
          - ./scripts/ci/deploy.sh production gp-lendi ./apps/lendi/ci/lcd.yml
        plugins: *plugins