export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const { replaceConsoleWithPinoLogger } = await import('@gp/util/logging');
    replaceConsoleWithPinoLogger();

    if (process.env.DD_SERVICE_NAME) {
      const { tracer } = await import('dd-trace');
      console.info(`Initialising dd-trace for service ${process.env.DD_SERVICE_NAME}`);
      tracer.init({
        logInjection: true,
        runtimeMetrics: true,
        profiling: true,
      });
      tracer.use('next', { measured: true });
      tracer.use('pino');
    }

    const { initializeLDClient } = await import('@gp/shared/server-launchdarkly');
    initializeLDClient();

    console.info('Starting gp-lendi NextJS service...');
  }
}
