import { Brand, InteractionType } from '@lendi/core-constants';

import { get, getEnvVar } from '@gp/util/data-service';

type AvailabilityData = {
  [key in InteractionType]: {
    availability: {
      datetime: string;
    }[];
    nextAvailableTimeslot: string;
  };
};

export const getBrokerNextAvailability = async (
  brokerId: string,
  brand: Brand,
  interactionType = InteractionType.Video
) => {
  try {
    const {
      parsedBody: { data },
    } = await get<{ data: AvailabilityData }>(
      `${getEnvVar('GP_API_BASE_URL')}/v3/availability/next/${brokerId}?brand=${brand}`
    );
    return (
      data[interactionType].nextAvailableTimeslot || data[interactionType].availability[0].datetime
    );
  } catch (e) {
    return null;
  }
};
