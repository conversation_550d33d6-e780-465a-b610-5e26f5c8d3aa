'use client';

import { useEffect, useState } from 'react';
import { documentToReactComponents } from '@contentful/rich-text-react-renderer';
import { Document } from '@contentful/rich-text-types';
import {
  Breadcrumbs,
  Container,
  Divider,
  Grid,
  GridCol,
  Group,
  Image,
  List,
  ListItem,
  SimpleGrid,
  Stack,
  Text,
  Title,
  TypographyStylesProvider,
} from '@mantine/core';
import dayjs from 'dayjs';
import { Brand } from '@lendi/core-constants';
import { SESSION_STORAGE_KEYS } from '@lendi/lala-utils';

import { Broker } from '@gp/data-access/broker';
import { AnchorWithTracking, LinkButtonWithTracking, PageTracking } from '@gp/ui/components';
import { Clock, Facebook, Google, LinkedIn, Telephone, Twitter } from '@gp/ui/icons';

import { getBrokerNextAvailability } from './getBrokerNextAvailability';

import classes from './style.module.css';

export default function BrokerDetails({ broker }: { broker: Broker }) {
  const {
    id,
    name,
    email,
    phone,
    imageUrl,
    workingHours,
    specialise = [],
    introduction,
    mediaLink,
  } = broker;
  const { facebook, linkedIn, twitter, google } = mediaLink || {};
  const [nextAvailability, setNextAvailability] = useState<string | null>(null);
  const handleClickBookAnAppointment = () => {
    if (typeof sessionStorage === 'undefined') return;

    sessionStorage.setItem(SESSION_STORAGE_KEYS.CTOR_TARGET_BROKER_ID, id);
  };

  useEffect(() => {
    if (typeof sessionStorage === 'undefined') return;
    if (document?.referrer === '') {
      sessionStorage.setItem(SESSION_STORAGE_KEYS.CTOR_TARGET_BROKER_ID, id);
    }
    getBrokerNextAvailability(id, Brand.Lendi).then(setNextAvailability);
  }, [id]);

  return (
    <PageTracking name="Broker Page" category="CMS">
      <Stack gap={0}>
        <Container fluid bg="#003046" c="white">
          <Container pb={{ base: 'md', md: '0' }} size="responsive">
            <Grid pt="lg">
              <Grid.Col span={{ base: 12, sm: 6 }} pr="md">
                <Stack>
                  <Breadcrumbs my="xl">
                    <AnchorWithTracking
                      href="/mortgage-broker"
                      label="Mortgage broker"
                      position="breadcrumb"
                    >
                      <Title order={6}>Mortgage broker</Title>
                    </AnchorWithTracking>
                    <Title order={6}>{name}</Title>
                  </Breadcrumbs>
                  <Title order={1} ta={{ base: 'center', sm: 'left' }}>
                    {name}
                  </Title>
                  <Group>
                    <LinkButtonWithTracking
                      w={{ base: '100%', lg: 'min-content' }}
                      variant="primary"
                      label="Book an appointment"
                      href={`/book-appointment/time-selection/?brokerId=${id}`}
                      onClick={handleClickBookAnAppointment}
                      isExternalLink
                      purpose="Banner"
                    />
                  </Group>
                </Stack>
              </Grid.Col>
              <Grid.Col
                span={{ base: 12, sm: 6 }}
                mih={{ base: 'unset', sm: 460 }}
                pl={{ base: 'xs', sm: 'md' }}
              >
                {!!imageUrl && (
                  <Image className={classes.image} src={imageUrl} alt={name} fit="contain" />
                )}
              </Grid.Col>
            </Grid>
          </Container>
        </Container>
        <Container size="responsive" my="xl">
          <Grid>
            <GridCol span={{ base: 12, sm: 7 }} pr="md">
              <Stack>
                <Title order={2}>About me</Title>
                {!!introduction && (
                  <TypographyStylesProvider>
                    {documentToReactComponents(introduction as Document)}
                  </TypographyStylesProvider>
                )}
                <SimpleGrid cols={{ base: 1, md: 3 }} verticalSpacing={0}>
                  <Title order={3} mb={{ base: 'md', md: 0 }}>
                    I specialise in
                  </Title>
                  <List>
                    {specialise.slice(0, Math.ceil(specialise.length / 2)).map((v) => (
                      <ListItem key={v}>{v}</ListItem>
                    ))}
                  </List>
                  <List>
                    {specialise
                      .slice(Math.ceil(specialise.length / 2), specialise.length)
                      .map((v) => (
                        <ListItem key={v}>{v}</ListItem>
                      ))}
                  </List>
                </SimpleGrid>
              </Stack>
            </GridCol>
            <GridCol
              span={{ base: 12, sm: 5 }}
              pl={{ base: 'xs', sm: 'md' }}
              className={classes.contact}
            >
              <Stack>
                <Title order={3}>Let&apos;s talk</Title>
                <Group>
                  <Stack gap="xxs" align="center">
                    <LinkButtonWithTracking
                      variant="primary"
                      label="Book an appointment"
                      href={`/book-appointment/time-selection/?brokerId=${id}`}
                      onClick={handleClickBookAnAppointment}
                      isExternalLink
                      purpose="Let's talk"
                      size="sm"
                    />
                    <Text size="sm">Choose a time</Text>
                  </Stack>
                  {nextAvailability && (
                    <Stack gap="xxs" align="center">
                      <LinkButtonWithTracking
                        variant="secondary"
                        label={dayjs(nextAvailability).format('ddd, D MMMM h:mma')}
                        href={`/book-appointment/time-selection/?brokerId=${id}&selectedVideoApptDateTime=${encodeURIComponent(
                          nextAvailability
                        )}`}
                        onClick={handleClickBookAnAppointment}
                        isExternalLink
                        purpose="Let's talk"
                        size="sm"
                      />
                      <Text size="sm">Next appointment</Text>
                    </Stack>
                  )}
                </Group>
                <Divider variant="dashed" color="#5F5F5F" />
                <Group align="flex-start">
                  <Clock size={48} />
                  <Stack gap={0}>
                    {[
                      'Monday',
                      'Tuesday',
                      'Wednesday',
                      'Thursday',
                      'Friday',
                      'Saturday',
                      'Sunday',
                    ].map((day, index) => (
                      <Text key={day}>
                        {day}: {workingHours[index]}
                      </Text>
                    ))}
                  </Stack>
                </Group>
                <Group align="flex-start">
                  <Telephone size={48} />
                  <Stack gap={0}>
                    <Text>
                      Phone:{' '}
                      <AnchorWithTracking label="Phone" href={`tel:${phone}`}>
                        {phone}
                      </AnchorWithTracking>
                    </Text>
                    <Text>
                      Email:{' '}
                      <AnchorWithTracking
                        className={classes.email}
                        label="Email"
                        href={`mailto:${email}`}
                      >
                        {email}
                      </AnchorWithTracking>
                    </Text>
                    {(facebook || linkedIn || twitter || google) && (
                      <Group gap="xxxs" align="center">
                        <Text>Follow: </Text>
                        {facebook && (
                          <AnchorWithTracking
                            className={classes.social}
                            label="Facebook"
                            href={facebook}
                            target="_blank"
                          >
                            <Facebook size={24} />
                          </AnchorWithTracking>
                        )}
                        {linkedIn && (
                          <AnchorWithTracking
                            className={classes.social}
                            label="LinkedIn"
                            href={linkedIn}
                            target="_blank"
                          >
                            <LinkedIn size={24} />
                          </AnchorWithTracking>
                        )}
                        {twitter && (
                          <AnchorWithTracking
                            className={classes.social}
                            label="Twitter"
                            href={twitter}
                            target="_blank"
                          >
                            <Twitter size={24} />
                          </AnchorWithTracking>
                        )}
                        {google && (
                          <AnchorWithTracking
                            className={classes.social}
                            label="Google"
                            href={google}
                            target="_blank"
                          >
                            <Google size={24} />
                          </AnchorWithTracking>
                        )}
                      </Group>
                    )}
                  </Stack>
                </Group>
              </Stack>
            </GridCol>
          </Grid>
        </Container>
      </Stack>
    </PageTracking>
  );
}
