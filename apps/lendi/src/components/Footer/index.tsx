import { Container, Stack, Title, useMantineTheme } from '@mantine/core';

import GeneralPageLinks from './GeneralPageLinks';
import MainPageLinks from './MainPageLinks';
import SocialLinks from './SocialLinks';

export default function Footer() {
  const { colors } = useMantineTheme();

  return (
    <footer style={{ backgroundColor: colors.cyan[5] }}>
      <Container c="white" size="responsive" py="lg">
        <Stack>
          <Title order={3}>Still looking?</Title>
          <MainPageLinks />
          <GeneralPageLinks />
          <SocialLinks />
        </Stack>
      </Container>
    </footer>
  );
}
