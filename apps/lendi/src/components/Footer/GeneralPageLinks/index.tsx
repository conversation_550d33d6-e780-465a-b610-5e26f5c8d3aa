import { Fragment } from 'react';
import { An<PERSON>, Divider, Group, Stack, Text } from '@mantine/core';

import { GENERAL_PAGES } from './generalPages';

export default function GeneralPageLinks() {
  return (
    <Stack>
      <Group align="center" justify="center" py="xxxs">
        {GENERAL_PAGES.map(({ label, url }, index) => (
          <Fragment key={label}>
            <Anchor href={url} underline="never" c="white" fw="bold">
              {label}
            </Anchor>
            {index < GENERAL_PAGES.length - 1 && (
              <Divider orientation="vertical" color="white" my={2} />
            )}
          </Fragment>
        ))}
      </Group>
      <Group justify="center" gap="xs">
        <Text size="sm" fw="bold">
          Lendi Pty Ltd
        </Text>
        <Text size="sm"> · </Text>
        <Text size="sm">Grosvenor Place, L28, 225 George Street Sydney NSW 2000</Text>
        <Text size="sm"> · </Text>
        <Anchor href="tel:1300 323 181" size="sm" underline="never" c="white">
          Ph: 1300 323 181
        </Anchor>
      </Group>
    </Stack>
  );
}
