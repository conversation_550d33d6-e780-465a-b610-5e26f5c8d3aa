import { Anchor, List, SimpleGrid, Stack } from '@mantine/core';

import { MAIN_PAGES } from './mainPages';

import classes from './style.module.css';

export default function MainPageLinks() {
  return (
    <SimpleGrid cols={{ base: 1, sm: MAIN_PAGES.length }} spacing={0}>
      {MAIN_PAGES.map(({ category, url, pages }) => (
        <Stack key={category} gap="sm">
          <Anchor href={url} fz="lg" c="white" fw="bold">
            {category}
          </Anchor>
          <List listStyleType="none" spacing="sm" classNames={{ root: classes.root }}>
            {pages.map(({ label, url }) => (
              <List.Item key={label}>
                <Anchor href={url} c="white" fz={{ base: 'sm', sm: 'md' }} fw="bold">
                  {label}
                </Anchor>
              </List.Item>
            ))}
          </List>
        </Stack>
      ))}
    </SimpleGrid>
  );
}
