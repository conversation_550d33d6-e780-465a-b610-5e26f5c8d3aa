import React from 'react';
import { <PERSON><PERSON>, Group, Stack } from '@mantine/core';

import { Facebook, Lendi, LinkedIn, Twitter, Youtube } from '@gp/ui/icons';

const FACEBOOK_URL = 'https://www.facebook.com/lendiau/';
const TWITTER_URL = 'https://twitter.com/lendiau';
const LINKEDIN_URL = 'https://www.linkedin.com/company/lendi/';
const YOUTUBE_URL = 'https://www.youtube.com/channel/UClqvf6ktIXZtlD556hyFq1g/featured';

export default function SocialLinks() {
  return (
    <Stack pt="sm" pb={22} gap={0}>
      <Group justify="center">
        <Lendi fill="#fff" width={160} height={100} />
      </Group>
      <Group justify="center" mt={36} gap={20} ml={18}>
        <Anchor href={FACEBOOK_URL} c="white" h={35}>
          <Facebook size={35} />
        </Anchor>
        <Anchor href={TWITTER_URL} c="white" h={35}>
          <Twitter size={35} />
        </Anchor>
        <Anchor href={LINKEDIN_URL} c="white" h={35}>
          <LinkedIn size={35} />
        </Anchor>
        <Anchor href={YOUTUBE_URL} c="white" h={35}>
          <Youtube size={35} />
        </Anchor>
      </Group>
    </Stack>
  );
}
