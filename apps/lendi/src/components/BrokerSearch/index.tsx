'use client';

import { useState } from 'react';
import { Container, Grid, Select, Title } from '@mantine/core';

import type { Broker } from '@gp/data-access/broker';
import { LinkButtonWithTracking } from '@gp/ui/components';

import classes from './style.module.css';

interface BrokerSearchProps {
  brokers: Pick<Broker, 'slug' | 'name'>[];
}

export default function BrokerSearch({ brokers }: BrokerSearchProps) {
  const [selectedBroker, setSelectedBroker] = useState<string | null>(null);

  return (
    <Container size="responsive" my="xl" p="lg" bg="#f8f8f8">
      <Grid>
        <Grid.Col span={{ base: 12, md: 12, lg: 9 }}>
          <Select
            flex="1"
            label={<Title order={6}>Enter a broker name and select an option</Title>}
            placeholder="Just start typing..."
            searchable
            data={brokers.map(({ name, slug }) => ({ value: slug, label: name }))}
            onChange={setSelectedBroker}
          />
        </Grid.Col>
        <Grid.Col className={classes.button} span={{ base: 12, md: 12, lg: 3 }}>
          <LinkButtonWithTracking
            fullWidth
            label="View Broker"
            miw="120px"
            disabled={!selectedBroker}
            useNextLink
            prefetch={false}
            href={`/mortgage-broker/${selectedBroker || ''}`}
          />
        </Grid.Col>
      </Grid>
    </Container>
  );
}
