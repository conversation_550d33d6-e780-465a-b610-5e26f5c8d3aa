import { Container, Stack, Text, Title } from '@mantine/core';
import { Brand } from '@lendi/lala-utils';

import { FunnelCTAs, FunnelFAQs } from '@gp/feature/funnels';
import { PageTracking, ProductReviewWidget } from '@gp/ui/components';

export default async function ApplyNow() {
  return (
    <PageTracking name="Basics page" category="FUNNELS">
      <Stack gap={48} align="center" flex="1">
        <Container maw={800} pt={48} px={16}>
          <Stack align="center">
            <Title order={2} fw={700} c="primary" ta="center">
              Discover your perfect home loan with Lendi
            </Title>
            <Text size="lg">
              Leveraging our market leading technology, we&apos;ve empowered millions of Australians
              to navigate the home loan process with confidence and ease.
            </Text>
            <Text size="lg">
              At Lendi, we&apos;re flipping the script on home loans – all online, with expert
              support. Sign up now, and <strong>in just two minutes</strong>, unlock tailored
              options from 25+ lenders to secure your loan.
            </Text>
          </Stack>
        </Container>
        <Container maw={570} px={16}>
          <FunnelCTAs />
        </Container>
        <Container px={16}>
          <Stack gap="xs" align="center">
            <ProductReviewWidget brand={Brand.Lendi} />
          </Stack>
        </Container>
        <Container fluid bg="gray.0" py={48} px={16} flex="1">
          <FunnelFAQs type={Brand.Lendi} />
        </Container>
      </Stack>
    </PageTracking>
  );
}
