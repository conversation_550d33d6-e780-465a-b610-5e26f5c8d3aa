'use client';

import { useEffect } from 'react';
import { Stack } from '@mantine/core';
import dynamic from 'next/dynamic';

import { ErrorProvider } from '@gp/shared/app-providers';

const Header = dynamic(() => import('@gp/feature/lendi-navbar'));

export default function Error({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    // update to Datadog error logging
    console.error(error);
  }, [error]);

  return (
    <html>
      <body>
        <Stack gap={0} mih="100vh">
          <Header />
          <ErrorProvider errorCode={500} />
        </Stack>
      </body>
    </html>
  );
}
