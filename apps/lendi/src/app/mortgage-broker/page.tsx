import { Breadcrumbs, Container, Stack, Text, Title } from '@mantine/core';
import type { Metadata } from 'next';

import { getAllBrokers } from '@gp/data-access/broker';
import { PageTracking } from '@gp/ui/components';

import { BrokerSearch } from '../../components';

export const dynamic = 'auto';
export const revalidate = 0;

export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default async function MortgageBroker() {
  const brokers = await getAllBrokers();

  return (
    <PageTracking name="Broker search" category="CMS">
      <Stack gap={0}>
        <Container fluid bg="#003046" c="white" style={{ borderBottomRightRadius: '90px' }}>
          <Container pb="xxxl" size="responsive">
            <Breadcrumbs my="xl">
              <Title order={6}>Mortgage broker</Title>
            </Breadcrumbs>
            <Title order={1}>Find your Lendi expert</Title>
            <Text size="lg" mt="md">
              Get expert guidance from a home loan specialist
            </Text>
          </Container>
        </Container>
        <BrokerSearch brokers={brokers} />
      </Stack>
    </PageTracking>
  );
}
