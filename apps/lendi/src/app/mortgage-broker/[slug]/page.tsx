import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { getBrokerBySlug } from '@gp/data-access/broker';

import { BrokerDetails } from '../../../components';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default async function Broker({ params }: { params: { slug: string } }) {
  const broker = await getBrokerBySlug(params.slug);

  if (!broker) {
    notFound();
  }

  return <BrokerDetails broker={broker} />;
}
