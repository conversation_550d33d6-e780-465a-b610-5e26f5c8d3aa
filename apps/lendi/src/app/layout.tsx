import { ColorSchemeScript } from '@mantine/core';
import type { Metadata } from 'next';
import { cookies } from 'next/headers';
import Script from 'next/script';
import { Brand } from '@lendi/lala-utils';

import { AppProviders } from '@gp/shared/app-providers';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { getLDInitFlags, getLDSingleKindContext } from '@gp/shared/server-launchdarkly';
import { lendiTheme } from '@gp/theme/lendi';

export const metadata: Metadata = {
  title: 'Lendi - Your home for home loans',
  description:
    'We make home loans simple by combining smart technology and expertise to find you a better solution.',
};

const ENV_VARS = {
  GP_AMPLITUDE_EXPERIMENT_API_KEY: process.env['AMPLITUDE_EXPERIMENT_API_KEY'] || '',
  GP_API_BASE_URL: process.env['API_BASE_URL'] || '',
  GP_APP_ENVIRONMENT: process.env['APP_ENVIRONMENT'] || '',
  GP_LAUNCHDARKLY_API_KEY: process.env['LAUNCHDARKLY_API_KEY'] || '',
  GP_LAUNCHDARKLY_CLIENT_ID: process.env['LAUNCHDARKLY_CLIENT_ID'] || '',
  GP_LAUNCHDARKLY_ENV_KEY: process.env['LAUNCHDARKLY_ENV_KEY'] || '',
  GP_APP_BRAND: Brand.Lendi,
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const _cookies = await cookies();
  const targetId = _cookies.get('targetId')?.value; // Generate a new targetId if not present
  const authsession = _cookies.get('authsession')?.value; // sessionId,userId
  const [_, userId] = (authsession || '').split(',');
  const ldContext = getLDSingleKindContext(Brand.Aussie, targetId, userId);
  const initAllFlags = await getLDInitFlags(ldContext);

  return (
    <html lang="en">
      <head>
        <ColorSchemeScript />
        <Script
          type="text/javascript"
          strategy="afterInteractive"
          src={`https://cdn.amplitude.com/script/${ENV_VARS.GP_AMPLITUDE_EXPERIMENT_API_KEY}.experiment.js`}
        ></Script>
      </head>
      <body>
        <AppProviders
          envVars={ENV_VARS}
          brand={Brand.Lendi}
          theme={lendiTheme}
          launchdarklyInit={{ bootstrap: initAllFlags.toJSON(), context: ldContext }}
        >
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
