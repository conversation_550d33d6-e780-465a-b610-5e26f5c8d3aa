// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  experimental: {
    optimizePackageImports: ['@mantine/core', '@mantine/hooks'],
    instrumentationHook: true,
    serverComponentsExternalPackages: ['dd-trace'],
  },
  assetPrefix: '/growth-product',
  trailingSlash: true,
  webpack: ( config ) => {
    if(config.output.chunkFilename === 'static/chunks/[name].[contenthash].js') {
      config.output.chunkFilename = (pathData) => {
        if(pathData.chunk.name){
          return `static/chunks/${`${pathData.chunk.name}`.toLowerCase()}.[contenthash].js`;
        }
        return 'static/chunks/[name].[contenthash].js';
      }
    }
    return config
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
