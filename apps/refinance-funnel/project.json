{"name": "refinance-funnel", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/refinance-funnel/src", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project refinance-funnel --web", "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/refinance-funnel/webpack.config.js", "outputPath": "apps/refinance-funnel/dist", "index": "apps/refinance-funnel/src/index.html", "main": "apps/refinance-funnel/src/main.tsx", "tsConfig": "apps/refinance-funnel/tsconfig.app.json", "assets": ["apps/refinance-funnel/src/growth-product-assets"], "postcssConfig": "apps/refinance-funnel/postcss.config.js"}}, "serve": {"executor": "@nx/webpack:dev-server", "options": {"buildTarget": "build", "hmr": true, "port": 4000}}, "preview": {"executor": "nx:run-commands", "options": {"command": "echo", "args": ["use \"pnpm nx run refinance-funnel:serve-static\" to preview the app"]}}, "serve-static": {"options": {"buildTarget": "build", "staticFilePath": "apps/refinance-funnel/dist", "port": 4444, "spa": true}}}}