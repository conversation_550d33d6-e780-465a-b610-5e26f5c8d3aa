import { lazy, Suspense, useState } from 'react';
import { Group, Stack, Text, Title } from '@mantine/core';
import { useDocumentTitle, useLocalStorage, useSessionStorage } from '@mantine/hooks';
import { Status, useSession } from '@lendi/lala-react';

import {
  generateRefinanceLead,
  Refinance,
  setRefinanceFunnelOneFormValues,
} from '@gp/data-access/customer-funnel';
import { FunnelLayout } from '@gp/feature/funnels';
import { AlertWithTracking, ButtonWithTracking, PageTracking } from '@gp/ui/components';
import { ArrowBack, Error } from '@gp/ui/icons';
import { embedMarketingTrackingParams, generateReturnURL } from '@gp/util/url';

const CurrentLoan = lazy(() =>
  import('@gp/feature/refinance').then((module) => ({ default: module.CurrentLoan }))
);
const Property = lazy(() =>
  import('@gp/feature/refinance').then((module) => ({ default: module.Property }))
);
const Preferences = lazy(() =>
  import('@gp/feature/refinance').then((module) => ({ default: module.Preferences }))
);
const PersonalDetails = lazy(() =>
  import('@gp/feature/refinance').then((module) => ({ default: module.PersonalDetails }))
);

const STEPS = [
  {
    title: 'Your current loan',
    description: `This will help us find some great options, suited to you and your scenario.`,
    Component: CurrentLoan,
    alert: {
      header: 'Refinancing starts with 4 easy steps...',
      content:
        "Let us know about your current loan, the property you're refinancing, your preferences and details and we'll provide you with some preliminary rate matches.",
      variant: 'default',
    },
  },
  {
    title: 'Your property',
    description:
      'Let us work out how much you own of your property by telling us a few details below. ',
    Component: Property,
    alert: undefined,
  },
  {
    title: 'Your preferences',
    description:
      'Your financial needs are unique, and your refinancing should be too. Tell us what matters most to you.',
    Component: Preferences,
    alert: undefined,
  },
  {
    title: 'Your details',
    description:
      "Almost done! Just a few quick personal details to ensure our advice is tailored and relevant to you. We're excited to help you find the perfect home loan!",
    Component: PersonalDetails,
    alert: undefined,
  },
] as const;

const RefinanceFunnel = () => {
  const { status } = useSession();
  const isAuthenticated = status === Status.Authenticated;
  const [step, setStep] = useState(0);
  const [showAlert, setShowAlert] = useSessionStorage<boolean>({
    key: 'refinance-funnel-alert',
    defaultValue: true,
  });
  const [data, setData] = useLocalStorage<Refinance>({
    key: 'refinance-funnel',
    getInitialValueInEffect: false,
    defaultValue: {},
  });
  const { title, description, Component, alert } = STEPS[step];

  const handleNextStep = (data: Refinance) => {
    if (typeof window === 'undefined') return;

    setData(data);
    if (step < STEPS.length - 1) {
      setStep(step + 1);
    } else {
      setRefinanceFunnelOneFormValues(data);
      generateRefinanceLead(data);
      const destination = embedMarketingTrackingParams(
        `${
          isAuthenticated ? '/home-loans/basics/landing/' : '/sign-in/sign-up/'
        }${generateReturnURL([
          ...(isAuthenticated ? [] : ['/home-loans/basics/landing/']),
          '/home-loans/basics/application/applicationidplaceholder/ctp/',
          '/home-loans/basics/application/applicationidplaceholder/applicant/applicantidplaceholder/',
        ])}`
      );
      if (process.env.NODE_ENV === 'development') {
        console.log('Redirecting to:', destination);
      } else {
        window.localStorage.removeItem('refinance-funnel');
        window.location.assign(destination);
      }
    }
  };

  useDocumentTitle(title);

  return (
    <PageTracking name="Refinance Funnel 1" category="FUNNELS">
      <FunnelLayout.Progress progress={step / STEPS.length} />
      <FunnelLayout.FormContainer>
        {step > 0 && (
          <Group>
            <ButtonWithTracking
              leftSection={<ArrowBack />}
              label="Back"
              position={`Refinance - ${title}`}
              variant="transparent"
              size="compact-sm"
              onClick={() => setStep(step - 1)}
            />
          </Group>
        )}
        {alert && showAlert && (
          <AlertWithTracking
            title={alert.header}
            name={alert.header}
            position="Refinance"
            variant={alert.variant}
            icon={<Error size={14} />}
            withCloseButton
            closeButtonLabel="Close"
            onClose={() => setShowAlert(false)}
          >
            {alert.content}
          </AlertWithTracking>
        )}
        <Stack gap={10}>
          <Title order={2} c="primary">
            {title}
          </Title>
          <Text size="lg">{description}</Text>
        </Stack>
        <Suspense fallback={null}>
          <Component
            formName={`Refinance - ${title}`}
            data={data}
            handleNextStep={handleNextStep}
          />
        </Suspense>
      </FunnelLayout.FormContainer>
    </PageTracking>
  );
};

export default function App() {
  return (
    <FunnelLayout>
      <RefinanceFunnel />
    </FunnelLayout>
  );
}
