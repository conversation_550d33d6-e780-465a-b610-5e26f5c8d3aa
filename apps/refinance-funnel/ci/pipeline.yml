plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: "NPM_TOKEN"
  - lendi-au/ssm#0.5.5:
      ssmkey: "platform/NPM_TOKEN"
      exportname: LENDI_NPM_TOKEN
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/lsd"
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/liam"
  - docker#v3.4.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest
      workdir: /app
      environment:
        - "NPM_TOKEN"
        - "LENDI_NPM_TOKEN"
        - "AWS_REGION"
        - "AWS_DEFAULT_REGION"
      propagate-environment: true

artifacts: &artifacts
  - dist/refinance-funnel/**/*
steps:
  - group: "🎯 Refinance Funnel"
    key: "refinance-funnel"
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ":webpack: Refinance Funnel - Build (DEV)"
        agents:
          queue: build
        key: "refinance:build-development"
        commands:
          - echo "⏳ Building Refinance Funnel (DEV)"
          - ./apps/refinance-funnel/ci/build.sh development
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Refinance Funnel - Build (STG)"
        branches: 'main'
        agents:
          queue: build
        key: "refinance:build-staging"
        commands:
          - echo "⏳ Building Refinance Funnel (STG)"
          - ./apps/refinance-funnel/ci/build.sh staging
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Refinance Funnel - Build (PRE-PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "refinance:build-preproduction"
        commands:
          - echo "⏳ Building Refinance Funnel (PRE-PROD)"
          - ./apps/refinance-funnel/ci/build.sh preproduction
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Refinance Funnel - Build (PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "refinance:build-production"
        commands:
          - echo "⏳ Building Refinance Funnel (PROD)"
          - ./apps/refinance-funnel/ci/build.sh production
        plugins: *plugins
        artifact_paths: *artifacts

      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ":codedeploy: Refinance Funnel - Deploy (DEV)"
        agents:
          queue: development
        key: "refinance:deploy-development"
        depends_on:
          - step: "refinance:build-development"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Refinance Funnel (DEV)"
          - ./apps/refinance-funnel/ci/deploy.sh development
        plugins: *plugins

      - label: ":codedeploy: Refinance Funnel - Deploy (STG)"
        if: build.branch == 'main'
        agents:
          queue: staging
        key: "refinance:deploy-staging"
        depends_on:
          - step: "refinance:build-staging"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Refinance Funnel (STG)"
          - ./apps/refinance-funnel/ci/deploy.sh staging
        plugins: *plugins

      - label: ":codedeploy: Refinance Funnel - Deploy (PRE-PROD)"
        if: build.branch == 'main'
        agents:
          queue: preproduction
        key: "refinance:deploy-preproduction"
        depends_on:
          - step: "refinance:build-preproduction"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Refinance Funnel (PRE-PROD)"
          - ./apps/refinance-funnel/ci/deploy.sh preproduction
        plugins: *plugins

      - block: ':question: Release to Production'
        if: build.branch == 'main'
        key: "refinance:deploy-production-check"
        prompt: 'Are you sure you want to release to production?'

      - label: ":codedeploy: Refinance Funnel - Deploy (PROD)"
        if: build.branch == 'main'
        agents:
          queue: production
        key: "refinance:deploy-production"
        depends_on:
          - step: "refinance:build-production"
          - step: "refinance:deploy-production-check"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Refinance Funnel (PROD)"
          - ./apps/refinance-funnel/ci/deploy.sh production
        plugins: *plugins
