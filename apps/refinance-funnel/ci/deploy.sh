#!/usr/bin/env bash
environment=$1;

source ./scripts/ci/setup-shell.sh

if [[ "$BUILDKITE_BRANCH" = "main" ]]; then
    tag="latest"
else
    tag=$BUILDKITE_BRANCH
fi
buildkite-agent artifact download dist/refinance-funnel/$environment/* .
lsd deploy --environment $environment --project gp-refinance-funnel --tag $tag --directory dist/refinance-funnel/$environment -c '(\.html$|page-data\/|\.xml$|\.txt$)'
