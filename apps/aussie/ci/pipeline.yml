plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: 'NPM_TOKEN'
  - lendi-au/ssm#0.5.5:
      ssmkey: 'platform/NPM_TOKEN'
      exportname: LENDI_NPM_TOKEN
  - docker#v3.7.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest # this image has the tools baked in already
      workdir: /app
      environment:
        - 'NPM_TOKEN'
        - 'LENDI_NPM_TOKEN'
        - 'BUILDKITE_BRANCH'
        - 'BUILDKITE_BUILD_NUMBER'
        - 'LENDI_ECR_REGISTRY'
        - 'DATADOG_API_KEY'
        - 'DATADOG_APP_KEY'
      propagate-environment: true

steps:
  - group: '🎯 Growth Product - Aussie'
    key: 'gp-aussie'
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ':docker: Aussie - Build'
        agents:
          queue: build
        key: 'aussie:build'
        commands:
          - echo "⏳ Building Aussie"
          - ./scripts/ci/build-image.sh gp-aussie ./apps/aussie/ci/Dockerfile
          - echo "🚀 Pushing Image for Aussie"
          - ./scripts/ci/push-image.sh gp-aussie
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins:
          - lendi-au/ssm#0.5.5:
              ssmkey: 'NPM_TOKEN'
          - lendi-au/ssm#0.5.5:
              ssmkey: 'platform/NPM_TOKEN'
              exportname: LENDI_NPM_TOKEN
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/lcd'
          - lendi-au/npm-global#1.1.0:
              env: 'NPM_TOKEN'
              package: '@lendi/liam'
      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ':codedeploy: Aussie - Deploy (DEV)'
        agents:
          queue: development
        key: 'aussie:deploy-development'
        depends_on:
          - step: 'aussie:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Aussie (DEV)"
          - ./scripts/ci/deploy-infrastructure.sh development gp-aussie ./apps/aussie/infrastructure
          - ./scripts/ci/deploy.sh development gp-aussie ./apps/aussie/ci/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        # skip: true
        plugins: *plugins
      - label: ':codedeploy: Aussie - Deploy (STG)'
        if: build.branch == 'main'
        agents:
          queue: staging
        key: 'aussie:deploy-staging'
        depends_on:
          - step: 'aussie:build'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Aussie (STG)"
          - ./scripts/ci/deploy-infrastructure.sh staging gp-aussie ./apps/aussie/infrastructure
          - ./scripts/ci/deploy.sh staging gp-aussie ./apps/aussie/ci/lcd.yml
        retry:
          automatic:
            - exit_status: '*'
              limit: 2
        plugins: *plugins
      - block: ':question: Release Aussie app to Production'
        if: build.branch == 'main'
        key: 'aussie:deploy-production-check'
        prompt: 'Are you sure you want to release the aussie app to production?'
      - label: ':codedeploy: Aussie - Deploy (PROD)'
        if: build.branch == 'main'
        agents:
          queue: production
        key: 'aussie:deploy-production'
        depends_on:
          - step: 'aussie:build'
          - step: 'aussie:deploy-production-check'
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Aussie (PROD)"
          - ./scripts/ci/deploy-infrastructure.sh production gp-aussie ./apps/aussie/infrastructure
          - ./scripts/ci/deploy.sh production gp-aussie ./apps/aussie/ci/lcd.yml
        plugins: *plugins
