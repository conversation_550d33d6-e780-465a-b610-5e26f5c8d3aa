# This gets attached to the task role of the containers running.
# Use this if you need to access any resources you create like <PERSON>3, <PERSON><PERSON>, <PERSON>, etc.
# See AWS Documentation here: https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_examples_s3_rw-bucket.html

team: growth
containers:
  - image: ${LENDI_ECR_REGISTRY}/${PROJECT}:${BUILDKITE_BUILD_NUMBER}
    name: app
    port: 3000
    healthCheck:
      httpGet: /api/healthcheck
      timeout: 15
      startPeriod: 30
    access: public
    memory:
      default: 1024
    cpu:
      default: 500
    environment:
      development:
        APP_ENVIRONMENT: development
        CONTENTFUL_ENVIRONMENT: dev-cms
        BASE_URL: 'https://www.aussie-dev.com.au'
        API_BASE_URL: 'https://api.lendi-dev.net'
        AGENTS_SERVICE_API_BASE_URL: 'https://agents-api.aussie-dev.com.au'
        AUSSIE_FOR_AGENTS_ENQUIRY_WEBHOOK: 'https://hooks.zapier.com/hooks/catch/9825322/bq90a8u/'
        LAUNCHDARKLY_ENV_KEY: 'development'
        CORELOGIC_API_BASE_URL: 'https://api.corelogic.asia'
        AREA_BOUNDARY_MAPS_URL: 'https://lendicdn-dev.net/e5fa3da1afb35e409d31298e44569d10/71ccb7a35a452ea8153b6d920f9f190e'
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '3'
        # LENDI_AMPLITUDE_KEY: '********************************'
        AUSSIE_AMPLITUDE_KEY: '********************************'
      staging:
        APP_ENVIRONMENT: staging
        CONTENTFUL_ENVIRONMENT: staging
        BASE_URL: 'https://www.aussie-stg.com.au'
        API_BASE_URL: 'https://api.lendi-stg.net'
        AGENTS_SERVICE_API_BASE_URL: 'https://agents-api.aussie-stg.com.au'
        AUSSIE_FOR_AGENTS_ENQUIRY_WEBHOOK: 'https://hooks.zapier.com/hooks/catch/9825322/bq90a8u/'
        LAUNCHDARKLY_ENV_KEY: 'test'
        CORELOGIC_API_BASE_URL: 'https://api.corelogic.asia'
        AREA_BOUNDARY_MAPS_URL: 'https://lendicdn-stg.net/e5fa3da1afb35e409d31298e44569d10/71ccb7a35a452ea8153b6d920f9f190e'
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '10'
        AUSSIE_AMPLITUDE_KEY: '********************************'
      production:
        APP_ENVIRONMENT: production
        CONTENTFUL_ENVIRONMENT: master
        BASE_URL: 'https://www.aussie.com.au'
        API_BASE_URL: 'https://api.lendi.com.au'
        AGENTS_SERVICE_API_BASE_URL: 'https://agents-api.aussie.com.au'
        AUSSIE_FOR_AGENTS_ENQUIRY_WEBHOOK: 'https://hooks.zapier.com/hooks/catch/9825322/bq90a8u/'
        LAUNCHDARKLY_ENV_KEY: 'production'
        CORELOGIC_API_BASE_URL: 'https://api.corelogic.asia'
        AREA_BOUNDARY_MAPS_URL: 'https://lendicdn.net/e5fa3da1afb35e409d31298e44569d10/71ccb7a35a452ea8153b6d920f9f190e'
        PROPERTY_HUB_RDS_MAX_CONNECTIONS: '10'
        AUSSIE_AMPLITUDE_KEY: '********************************'
    secrets:
      default:
        CONTENTFUL_SPACE_ID: growth-product/contentful/aussie/space-id
        CONTENTFUL_ACCESS_TOKEN: growth-product/contentful/aussie/access-token
        LAUNCHDARKLY_API_KEY: growth-product/launchdarkly/api-key
        LAUNCHDARKLY_CLIENT_ID: growth-product/launchdarkly/client-id
        LAUNCHDARKLY_SDK_KEY: growth-product/launchdarkly/sdk-key
        MAPBOX_ACCESS_TOKEN: growth-product/mapbox/access-token
        PROPERTY_HUB_DATABASE_HOST: growth-product/property-hub/rds/root/host
        PROPERTY_HUB_DATABASE_APP_PASSWORD: growth-product/property-hub/rds/root/password
        CORELOGIC_CLIENT_ID: growth-product/corelogic/client-id
        CORELOGIC_CLIENT_SECRET: growth-product/corelogic/client-secret
        GOOGLE_MAPS_API_KEY: growth-product/google-maps/api-key
        GOOGLE_MAPS_ID: growth-product/google-maps/map-id
        RECAPTCHA_SECRET_KEY: growth-product/recaptcha/secret-key
        RECAPTCHA_CLIENT_KEY: growth-product/recaptcha/client-key

    alternateHostnames:
      development:
        latest:
          - growth-product-cloudfront.aussie-dev.com.au
      staging:
        latest:
          - growth-product-cloudfront.aussie-stg.com.au
      production:
        latest:
          - growth-product-cloudfront.aussie.com.au
ecsTaskRoleStatements:
  - Effect: Allow
    Action:
      - 'rds-db:connect'
    Resource:
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-RGYOMU7FCANHWGTRIURPPIATBI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-AAJVPYQ5QHRJGXN5ENZ6LVWTZI/property_hub
      - arn:aws:rds-db:ap-southeast-2:*:dbuser:db-T7D5GZGZFETTTY6DENT7HW3KWY/property_hub
