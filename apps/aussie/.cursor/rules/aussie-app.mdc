# Aussie App Guidelines

## Overview
The Aussie app is a Next.js 14 application using the App Router, built for the Aussie brand. It serves as the main customer-facing application for property search, home loans, and related services.

## Project Structure

### Core Architecture
```
apps/aussie/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── layout.tsx         # Root layout with providers
│   │   ├── page.tsx           # Home page
│   │   ├── api/               # API routes
│   │   ├── property/          # Property Hub pages
│   │   ├── insights/          # Articles & insights
│   │   ├── buyers-agent/      # Buyers agent flow
│   │   ├── conveyancing/      # Conveyancing services
│   │   ├── home-loans/        # Home loan funnels
│   │   ├── mobile-app/        # Mobile app pages
│   │   └── for-agents/        # Agent-specific pages
│   ├── middleware.ts          # Next.js middleware
│   ├── datadog-init.tsx       # Datadog RUM initialization
│   └── instrumentation.ts     # Performance monitoring
├── public/                    # Static assets
├── infrastructure/            # Terraform infrastructure
└── ci/                       # CI/CD configuration
```

## Key Technologies & Dependencies

### Core Framework
- **Next.js 14** with App Router
- **React 18** with Server Components
- **TypeScript** for type safety
- **Mantine UI** for component library
- **Aussie Theme** for branding

### Key Libraries
- **@gp/shared/app-providers** - Application providers
- **@gp/feature/aussie-navbar** - Navigation components
- **@gp/feature/aussie-footer** - Footer components
- **@gp/theme/aussie** - Brand theme
- **@gp/shared/launchdarkly** - Feature flags
- **@gp/shared/session-redirect** - Session management

## Page Structure Patterns

### 1. Root Layout Pattern
```typescript
// src/app/layout.tsx - Root layout with all providers
export default async function RootLayout({ children }: { children: React.ReactNode }) {
  // Get LaunchDarkly context and flags
  const ldContext = getLDSingleKindContext(Brand.Aussie, targetId, userId);
  const initAllFlags = await getLDInitFlags(ldContext);

  return (
    <html lang="en">
      <head>
        <ColorSchemeScript />
        {/* SEO and metadata */}
      </head>
      <body>
        <DatadogInit />
        <AppProviders
          envVars={ENV_VARS}
          brand={Brand.Aussie}
          theme={aussieTheme}
          launchdarklyInit={{ bootstrap: initAllFlags.toJSON(), context: ldContext }}
        >
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
```

### 2. Section Layout Pattern
```typescript
// Section-specific layouts (e.g., property/layout.tsx)
export default function SectionLayout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh">
      <MultiNavbar />
      <SessionRedirect allowUnauthenticated>
        <div role="main">{children}</div>
      </SessionRedirect>
      <FinePrint />
      <Footer />
    </Stack>
  );
}
```

### 3. Page Component Pattern
```typescript
// Simple page components using feature libraries
export default function PageName() {
  return <FeatureComponent />;
}

// With dynamic rendering
export const dynamic = 'force-dynamic';

// With metadata
export const metadata: Metadata = {
  title: 'Page Title | Aussie Homes',
  description: 'Page description...',
};
```

## Route Organization

### 1. Property Hub Routes
```
/property/
├── page.tsx                    # Property search landing
├── layout.tsx                  # Property section layout
├── results/                    # Search results
├── [state]/                    # State-specific pages
├── suburbs/                    # Suburb pages
├── suburb-search/              # Suburb search
└── sitemap.xml/               # Dynamic sitemap
```

### 2. Insights Routes
```
/insights/
├── page.tsx                    # Articles hub
├── layout.tsx                  # Insights layout
├── loading.tsx                 # Loading state
├── utils/                      # Utility functions
└── (pillar)/                   # Pillar pages (grouped)
    ├── home-loans/
    ├── investment/
    └── property/
```

### 3. Service Routes
```
/buyers-agent/
├── engagement/                 # Main engagement flow
├── start/                      # Initial landing
├── confirmation/               # Success confirmation
└── transaction-complete/       # Transaction completion

/conveyancing/
└── engagement/                 # Conveyancing flow

/home-loans/
└── basics/                     # Home loan basics
```

## Component Usage Patterns

### 1. Feature Library Integration
```typescript
// Use feature libraries for complete features
import { Confirmation } from '@gp/feature/buyers-agent';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { Footer } from '@gp/feature/aussie-footer';

export default function BuyersAgentConfirmation() {
  return <Confirmation />;
}
```

### 2. UI Component Integration
```typescript
// Use UI components for common elements
import { ButtonWithTracking } from '@gp/ui/components';
import { Hero, FeatureBlock } from '@gp/ui/blocks';

export default function LandingPage() {
  return (
    <>
      <Hero
        heading="Find Your Perfect Home"
        subtitle="Expert guidance every step of the way"
        heroImage={{ url: "/hero.jpg", description: "Happy family" }}
        buttons={[{ label: "Get Started", href: "/apply" }]}
      />
      <FeatureBlock
        title="Borrowing Power Calculator"
        description="See how much you could borrow"
        imageUrl="/calculator.jpg"
        cta="Try Calculator"
        ctaLink="/calculators/borrowing-power"
      />
    </>
  );
}
```

### 3. Theme Integration
```typescript
// Theme is automatically applied via AppProviders
// Use theme tokens in components
import { Container, Stack, Title } from '@mantine/core';

export function ThemedComponent() {
  return (
    <Container size="lg">
      <Stack gap="md">
        <Title order={1} c="grape.8">
          Aussie Branded Content
        </Title>
      </Stack>
    </Container>
  );
}
```

## API Route Patterns

### 1. Health Check Route
```typescript
// src/app/api/healthcheck/route.ts
export async function GET() {
  try {
    return new Response('👍', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });
  } catch (error) {
    return new Response('Health check failed', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });
  }
}
```

### 2. Agent Enquiry Route
```typescript
// src/app/api/agent-enquiry/route.ts
export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // Process agent enquiry
    const result = await processAgentEnquiry(body);
    
    return Response.json({ success: true, data: result });
  } catch (error) {
    return Response.json(
      { success: false, error: 'Failed to process enquiry' },
      { status: 500 }
    );
  }
}
```

## Middleware Configuration

### 1. Request Headers
```typescript
// src/middleware.ts
export function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-current-path', request.nextUrl.pathname);

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  
  // Cache control
  let tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
  tomorrow = new Date(tomorrow.toDateString());
  tomorrow.setTime(tomorrow.getTime() - 9 * 60 * 60 * 1000);
  
  response.headers.set('Cache-Control', `public`);
  response.headers.set('Expires', tomorrow.toUTCString());
  
  // Static assets
  if (request.nextUrl.pathname.startsWith('/growth-product/_next/static')) {
    response.headers.set('x-robots-tag', 'noindex, nofollow');
  }
  
  return response;
}
```

## Environment Configuration

### 1. Environment Variables
```typescript
const ENV_VARS = {
  GP_API_BASE_URL: process.env['API_BASE_URL'] || '',
  GP_BASE_URL: process.env['BASE_URL'] || '',
  GP_AGENTS_SERVICE_API_BASE_URL: process.env['AGENTS_SERVICE_API_BASE_URL'] || '',
  GP_APP_ENVIRONMENT: process.env['APP_ENVIRONMENT'] || '',
  GP_LAUNCHDARKLY_API_KEY: process.env['LAUNCHDARKLY_API_KEY'] || '',
  GP_LAUNCHDARKLY_CLIENT_ID: process.env['LAUNCHDARKLY_CLIENT_ID'] || '',
  GP_LAUNCHDARKLY_ENV_KEY: process.env['LAUNCHDARKLY_ENV_KEY'] || '',
  GP_MAPBOX_ACCESS_TOKEN: process.env['MAPBOX_ACCESS_TOKEN'] || '',
  GP_AMPLITUDE_WRITE_KEY: process.env['AUSSIE_AMPLITUDE_KEY'] || '',
  GP_AREA_BOUNDARY_MAPS_URL: process.env['AREA_BOUNDARY_MAPS_URL'] || '',
  GP_GOOGLE_MAPS_API_KEY: process.env['GOOGLE_MAPS_API_KEY'] || '',
  GP_GOOGLE_MAPS_ID: process.env['GOOGLE_MAPS_ID'] || '',
  GP_RECAPTCHA_CLIENT_KEY: process.env['RECAPTCHA_CLIENT_KEY'] || '',
  GP_APP_BRAND: Brand.Aussie,
};
```

### 2. Next.js Configuration
```javascript
// next.config.js
const nextConfig = {
  nx: {
    svgr: false,
  },
  experimental: {
    optimizePackageImports: ['@mantine/core', '@mantine/hooks'],
    instrumentationHook: true,
    serverComponentsExternalPackages: ['dd-trace'],
  },
  assetPrefix: '/growth-product',
  images: { unoptimized: true },
  trailingSlash: true,
  webpack: (config) => {
    // Custom webpack configuration
    return config;
  },
};
```

## Monitoring & Analytics

### 1. Datadog RUM Integration
```typescript
// src/datadog-init.tsx
datadogRum.init({
  applicationId: 'c11a8b0a-66a0-478d-8d1f-c8ee7b497315',
  clientToken: 'pubfb8162621270b6adf0dd15d825860dff',
  site: 'datadoghq.com',
  service: 'aussie-contentful-cms',
  env: getEnvVar('GP_APP_ENVIRONMENT'),
  sessionSampleRate: 0,
  sessionReplaySampleRate: 0,
  trackUserInteractions: true,
  trackResources: true,
  trackLongTasks: true,
  defaultPrivacyLevel: 'mask-user-input',
  plugins: [reactPlugin({ router: true })],
});
```

### 2. Performance Monitoring
```typescript
// src/instrumentation.ts
export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('./instrumentation.node');
  }
}
```

## SEO & Metadata Patterns

### 1. Page Metadata
```typescript
export const metadata: Metadata = {
  title: {
    template: '%s | Aussie Homes',
    default: 'Aussie Homes',
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/property/`,
    siteName: 'Aussie Home Loans',
    description: 'Property insights and trends...',
    images: {
      url: 'https://www.aussie.com.au/assets/favicon.ico',
      width: 800,
      height: 600,
    },
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  verification: {
    google: 'ZkzV_8df7LSGNW-A-1PW0P4R8LyUILNgMSVFZWsALaw',
  },
  itunes: {
    appId: '6451372662',
  },
  alternates: {
    canonical: `${process.env.BASE_URL}/property/`,
  },
};
```

### 2. Structured Data
```typescript
const orgJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Corporation',
  '@id': process.env.BASE_URL,
  logo: 'https://www.aussie.com.au/assets/favicon.ico',
  legalName: 'AHL Investments Pty Ltd',
  name: 'Aussie Home Loans',
  sameAs: [
    'https://www.linkedin.com/company/aussie/',
    'https://twitter.com/aussie',
    'https://www.facebook.com/AussieHomeLoans',
  ],
  url: process.env.BASE_URL,
  address: {
    '@type': 'PostalAddress',
    streetAddress: 'L28, 225 George Street',
    addressLocality: 'Sydney',
    addressRegion: 'NSW',
    postalCode: '2000',
    addressCountry: 'AU',
  },
  contactPoint: [
    {
      '@type': 'ContactPoint',
      telephone: '13 13 33',
      contactType: 'customer service',
      areaServed: 'AU',
      availableLanguage: ['English'],
    },
  ],
};
```

## Development Patterns

### 1. Feature-First Development
- Use feature libraries for complete business features
- Keep page components simple and focused
- Leverage shared components and utilities

### 2. Type Safety
- Use TypeScript for all components
- Define proper interfaces for props and data
- Use strict TypeScript configuration

### 3. Performance Optimization
- Use Next.js App Router for automatic optimization
- Implement proper loading states
- Use dynamic imports for heavy components
- Optimize images and assets

### 4. Error Handling
- Implement error boundaries at appropriate levels
- Use proper error pages (error.tsx, global-error.tsx)
- Log errors to monitoring services

## Testing Patterns

### 1. Component Testing
```typescript
// Test feature components
describe('BuyersAgentConfirmation', () => {
  it('renders confirmation component', () => {
    render(<Confirmation />);
    expect(screen.getByText(/confirmation/i)).toBeInTheDocument();
  });
});
```

### 2. Page Testing
```typescript
// Test page components
describe('PropertyPage', () => {
  it('renders property search interface', () => {
    render(<PropertyPage />);
    expect(screen.getByRole('search')).toBeInTheDocument();
  });
});
```

## Deployment Patterns

### 1. Environment-Specific Configuration
```typescript
const AUSSIE_IS_SCRIPTS: Record<string, string> = {
  development: 'https://cdn.evgnet.com/beacon/auscred/aussie_dev/scripts/evergage.min.js',
  staging: 'https://cdn.evgnet.com/beacon/auscred/aussiestaging/scripts/evergage.min.js',
  production: 'https://cdn.evgnet.com/beacon/auscred/aussieproduction/scripts/evergage.min.js',
};
```

### 2. Infrastructure
- Terraform configuration in `infrastructure/`
- CI/CD pipelines in `ci/`
- Environment-specific deployments

## Best Practices

### 1. Code Organization
- Keep page components simple
- Use feature libraries for complex functionality
- Maintain clear separation of concerns
- Follow Next.js App Router conventions

### 2. Performance
- Use Server Components where possible
- Implement proper loading states
- Optimize bundle size with dynamic imports
- Use proper caching strategies

### 3. SEO
- Implement proper metadata for all pages
- Use structured data where appropriate
- Ensure proper canonical URLs
- Optimize for Core Web Vitals

### 4. Accessibility
- Use semantic HTML elements
- Implement proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers

### 5. Security
- Validate all user inputs
- Implement proper CSRF protection
- Use secure headers
- Regular security audits
description:
globs:
alwaysApply: false
---
