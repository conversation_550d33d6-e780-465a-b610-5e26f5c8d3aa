# Event Tracking Guidelines

## Overview
Event tracking is implemented using two main systems:
1. **Analytics Library** - For user interactions and page views
2. **MCP Tracking (Interaction Studio)** - For personalized experiences and marketing automation

## Analytics Library Tracking

### Available Tracking Components

#### 1. ButtonWithTracking
Enhanced button component with automatic analytics tracking.

**Props:**
- `label` (required): Button text for analytics
- `position`: Context/location for tracking
- `purpose`: Intent/purpose for tracking
- `category`: Event category for analytics
- All standard Mantine Button props

**Usage:**
```typescript
import { ButtonWithTracking } from '@gp/ui/components';

<ButtonWithTracking
  label="Get Started"
  position="Hero Section"
  purpose="Lead Generation"
  category={EventCategory.CMS}
  onClick={handleClick}
>
  Get Started
</ButtonWithTracking>
```

#### 2. LinkButtonWithTracking
Link-styled button with tracking capabilities.

**Usage:**
```typescript
import { LinkButtonWithTracking } from '@gp/ui/components';

<LinkButtonWithTracking
  label="Learn More"
  position="Article Card"
  href="/learn-more"
  category={EventCategory.CMS}
>
  Learn More
</LinkButtonWithTracking>
```

#### 3. AnchorWithTracking
Enhanced anchor element with analytics.

**Usage:**
```typescript
import { AnchorWithTracking } from '@gp/ui/components';

<AnchorWithTracking
  label="External Link"
  position="Footer"
  href="https://example.com"
  category={EventCategory.CMS}
>
  Visit Site
</AnchorWithTracking>
```

#### 4. PageTracking
Page view tracking component.

**Props:**
- `name` (required): Page identifier
- `category` (required): Page category
- `customerType`: 'CUSTOMER' | 'TEAM' (default: 'CUSTOMER')
- `withMarketingCloudTracking`: Enable MCP tracking
- `propertyProp`: Property-related data for tracking

**Usage:**
```typescript
import { PageTracking } from '@gp/ui/components';

<PageTracking
  name="Property Search Results"
  category="AUSSIE_HOMES"
  customerType="CUSTOMER"
  withMarketingCloudTracking={true}
  propertyProp={{
    property_search_state: 'NSW',
    property_search_suburb: 'Sydney',
    property_search_council: 'City of Sydney'
  }}
>
  <PropertySearchResults />
</PageTracking>
```

#### 5. FormWithTracking
Form wrapper with automatic form analytics.

**Props:**
- `name` (required): Form identifier
- `form`: Mantine form instance
- `handleSubmit`: Submit handler
- `handleValidationFailure`: Validation error handler
- `shouldWaitForTracking`: Whether to wait for analytics

**Usage:**
```typescript
import { FormWithTracking } from '@gp/ui/components';

<FormWithTracking
  name="Contact Form"
  form={form}
  handleSubmit={handleSubmit}
  shouldWaitForTracking={true}
>
  <TextInput label="Name" {...form.getInputProps('name')} />
  <TextInput label="Email" {...form.getInputProps('email')} />
  <ButtonWithTracking label="Submit" type="submit" />
</FormWithTracking>
```

#### 6. ModalWithTracking
Modal with automatic display/close tracking.

**Props:**
- `name` (required): Modal identifier
- `position`: Context for tracking
- `footer`: Optional footer content
- `footerStyle`: Footer styling

**Usage:**
```typescript
import { ModalWithTracking } from '@gp/ui/components';

<ModalWithTracking
  opened={opened}
  onClose={close}
  name="Contact Modal"
  position="Header"
  footer={
    <ButtonWithTracking
      label="Submit"
      onClick={handleSubmit}
    />
  }
>
  Modal content
</ModalWithTracking>
```

#### 7. AccordionItemWithTracking
Accordion item with tracking.

**Usage:**
```typescript
import { AccordionItemWithTracking } from '@gp/ui/components';

<AccordionItemWithTracking
  value="faq-1"
  name="How much can I borrow?"
  position="FAQ Section"
>
  <AccordionControl>How much can I borrow?</AccordionControl>
  <AccordionPanel>Your borrowing capacity depends on...</AccordionPanel>
</AccordionItemWithTracking>
```

#### 8. CheckboxWithTracking
Checkbox with analytics tracking.

**Usage:**
```typescript
import { CheckboxWithTracking } from '@gp/ui/components';

<CheckboxWithTracking
  label="I agree to terms"
  position="Registration Form"
  {...form.getInputProps('terms')}
/>
```

#### 9. SegmentedControlWithTracking
Segmented control with tracking.

**Usage:**
```typescript
import { SegmentedControlWithTracking } from '@gp/ui/components';

<SegmentedControlWithTracking
  data={[
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' }
  ]}
  position="Settings"
  {...form.getInputProps('option')}
/>
```

#### 10. TabsWithTracking
Tabs component with analytics.

**Usage:**
```typescript
import { TabsWithTracking } from '@gp/ui/components';

<TabsWithTracking
  defaultValue="tab1"
  position="Product Details"
>
  <Tabs.List>
    <Tabs.Tab value="tab1">Overview</Tabs.Tab>
    <Tabs.Tab value="tab2">Features</Tabs.Tab>
  </Tabs.List>
  <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
  <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
</TabsWithTracking>
```

#### 11. AlertWithTracking
Alert component with analytics.

**Usage:**
```typescript
import { AlertWithTracking } from '@gp/ui/components';

<AlertWithTracking
  title="Success!"
  message="Your form has been submitted"
  type="success"
  position="Form Feedback"
/>
```

## MCP Tracking (Interaction Studio)

### sendEvent Function
Direct MCP event tracking for personalized experiences.

**Usage:**
```typescript
import { sendEvent } from '@gp/util/interaction-studio';

// Track user actions
sendEvent('Calculator Used', {
  calculator_type: 'borrowing_power',
  loan_amount: 500000,
  result: 'qualified'
});

// Track page views with property data
sendEvent('Viewed Property Details', {
  property_id: 'prop-123',
  property_type: 'house',
  property_price: 800000,
  suburb: 'Sydney',
  state: 'NSW'
});

// Track user preferences
sendEvent('Preference Updated', {
  loan_type: 'home_loan',
  property_type: 'house',
  budget_range: '500k-800k',
  location_preference: 'Sydney'
});
```

### Common Event Patterns

#### 1. Calculator Events
```typescript
// Borrowing power calculator
sendEvent('Borrowing Power Calculated', {
  calculator_type: 'borrowing_power',
  income: 100000,
  expenses: 3000,
  result: 'qualified',
  max_loan: 500000,
  user_type: 'first_home_buyer'
});

// Repayment calculator
sendEvent('Repayment Calculated', {
  calculator_type: 'repayment',
  loan_amount: 500000,
  interest_rate: 3.5,
  loan_term: 30,
  repayment_amount: 2245
});
```

#### 2. Property Events
```typescript
// Property search
sendEvent('Property Search Performed', {
  search_type: 'suburb_search',
  suburb: 'Sydney',
  state: 'NSW',
  property_type: 'house',
  price_range: '500k-800k',
  bedrooms: 3
});

// Property view
sendEvent('Property Viewed', {
  property_id: 'prop-123',
  property_type: 'house',
  price: 800000,
  suburb: 'Sydney',
  source: 'search_results'
});

// Property saved
sendEvent('Property Saved', {
  property_id: 'prop-123',
  save_type: 'favorites',
  user_type: 'registered'
});
```

#### 3. Form Events
```typescript
// Form started
sendEvent('Form Started', {
  form_name: 'home_loan_application',
  form_type: 'lead_generation',
  source: 'calculator'
});

// Form completed
sendEvent('Form Completed', {
  form_name: 'home_loan_application',
  form_type: 'lead_generation',
  completion_time: 300, // seconds
  fields_completed: 8
});

// Form abandoned
sendEvent('Form Abandoned', {
  form_name: 'home_loan_application',
  form_type: 'lead_generation',
  abandonment_step: 'personal_details',
  time_spent: 120 // seconds
});
```

#### 4. User Journey Events
```typescript
// Page views
sendEvent('Page Viewed', {
  page_name: 'Property Search',
  page_category: 'property_hub',
  user_type: 'returning',
  referrer: 'google'
});

// Navigation events
sendEvent('Navigation Clicked', {
  navigation_type: 'main_menu',
  menu_item: 'Home Loans',
  user_type: 'authenticated'
});

// CTA clicks
sendEvent('CTA Clicked', {
  cta_type: 'primary',
  cta_text: 'Get Started',
  cta_position: 'hero_section',
  page_name: 'Home'
});
```

## Tracking Best Practices

### 1. Consistent Naming
```typescript
// Use consistent event naming patterns
const EVENT_NAMES = {
  // User actions
  BUTTON_CLICKED: 'Button Clicked',
  FORM_SUBMITTED: 'Form Submitted',
  PAGE_VIEWED: 'Page Viewed',
  
  // Business events
  CALCULATOR_USED: 'Calculator Used',
  PROPERTY_VIEWED: 'Property Viewed',
  QUOTE_REQUESTED: 'Quote Requested',
  
  // Engagement events
  VIDEO_PLAYED: 'Video Played',
  DOCUMENT_DOWNLOADED: 'Document Downloaded',
  CHAT_INITIATED: 'Chat Initiated'
} as const;
```

### 2. Property Naming Conventions
```typescript
// Use snake_case for property names
const trackingProperties = {
  // User properties
  user_type: 'first_home_buyer',
  authentication_status: 'authenticated',
  user_id: 'user-123',
  
  // Content properties
  page_name: 'Property Search',
  content_type: 'calculator',
  content_id: 'borrowing-power',
  
  // Business properties
  loan_amount: 500000,
  property_type: 'house',
  suburb: 'Sydney',
  state: 'NSW'
};
```

### 3. Event Categories
```typescript
// Use appropriate event categories
import { EventCategory } from '@lendi/analytics-web';

const categories = {
  CMS: EventCategory.CMS,
  FUNNELS: EventCategory.FUNNELS,
  PROPERTY_REPORT: EventCategory.PROPERTY_REPORT,
  HOME_LOAN_HEALTH_CHECK: EventCategory.HOME_LOAN_HEALTH_CHECK,
  BUYERS_AGENT: EventCategory.BUYERS_AGENT,
  MOBILE_APP: EventCategory.MOBILE_APP
};
```

### 4. Position Tracking
```typescript
// Use descriptive position values
const positions = {
  // Page sections
  HERO_SECTION: 'Hero Section',
  FEATURE_SECTION: 'Feature Section',
  FOOTER: 'Footer',
  
  // Component positions
  NAVIGATION: 'Navigation',
  SIDEBAR: 'Sidebar',
  MODAL: 'Modal',
  
  // Form positions
  CONTACT_FORM: 'Contact Form',
  CALCULATOR_FORM: 'Calculator Form',
  SEARCH_FORM: 'Search Form'
};
```

## Integration Patterns

### 1. Component-Level Tracking
```typescript
// Wrap components with tracking
export function PropertyCard({ property }: PropertyCardProps) {
  return (
    <PageTracking
      name="Property Card"
      category="AUSSIE_HOMES"
      withMarketingCloudTracking={true}
      propertyProp={{
        property_search_state: property.state,
        property_search_suburb: property.suburb
      }}
    >
      <Card>
        <Image src={property.image} alt={property.title} />
        <Title>{property.title}</Title>
        <Text>{property.description}</Text>
        <ButtonWithTracking
          label="View Details"
          position="Property Card"
          purpose="Property Navigation"
        />
      </Card>
    </PageTracking>
  );
}
```

### 2. Page-Level Tracking
```typescript
// Track entire pages
export default function PropertySearchPage() {
  return (
    <PageTracking
      name="Property Search"
      category="AUSSIE_HOMES"
      customerType="CUSTOMER"
      withMarketingCloudTracking={true}
    >
      <PropertySearchForm />
      <PropertyResults />
    </PageTracking>
  );
}
```

### 3. Form Tracking
```typescript
// Track form interactions
export function ContactForm() {
  const form = useForm({
    initialValues: {
      name: '',
      email: '',
      message: ''
    }
  });

  const handleSubmit = (values: typeof form.values) => {
    // Form submission logic
  };

  return (
    <FormWithTracking
      name="Contact Form"
      form={form}
      handleSubmit={handleSubmit}
      shouldWaitForTracking={true}
    >
      <TextInput
        label="Name"
        {...form.getInputProps('name')}
      />
      <TextInput
        label="Email"
        {...form.getInputProps('email')}
      />
      <Textarea
        label="Message"
        {...form.getInputProps('message')}
      />
      <ButtonWithTracking
        label="Send Message"
        type="submit"
        position="Contact Form"
      />
    </FormWithTracking>
  );
}
```

### 4. Custom Event Tracking
```typescript
// Custom tracking with analytics context
import { useContext } from 'react';
import { LDContext } from '@gp/shared/launchdarkly';
import { sendEvent } from '@gp/util/interaction-studio';

export function CustomTrackingComponent() {
  const { analytics } = useContext(LDContext);

  const handleCustomAction = () => {
    // Analytics tracking
    analytics?.trackEvent({
      event_name: 'Custom Action',
      action_type: 'button_click',
      component: 'CustomComponent',
      position: 'Main Content'
    });

    // MCP tracking
    sendEvent('Custom Action Performed', {
      action_type: 'button_click',
      component: 'custom_component',
      user_type: 'authenticated'
    });
  };

  return (
    <ButtonWithTracking
      label="Custom Action"
      onClick={handleCustomAction}
      position="Custom Component"
    />
  );
}
```

## Testing Tracking

### 1. Analytics Testing
```typescript
// Mock analytics for testing
const mockAnalytics = {
  trackEvent: jest.fn(),
  trackPage: jest.fn()
};

// Test tracking events
describe('ButtonWithTracking', () => {
  it('tracks button clicks', () => {
    render(
      <LDContext.Provider value={{ analytics: mockAnalytics }}>
        <ButtonWithTracking
          label="Test Button"
          position="Test Position"
        />
      </LDContext.Provider>
    );

    fireEvent.click(screen.getByText('Test Button'));

    expect(mockAnalytics.trackEvent).toHaveBeenCalledWith({
      event_name: 'Button Clicked',
      buttonType: ButtonType.PRIMARY,
      text: 'Test Button',
      position: 'Test Position',
      authenticationStatus: AuthenticationStatus.UNAUTHENTICATED
    });
  });
});
```

### 2. MCP Testing
```typescript
// Mock MCP tracking
const mockSendEvent = jest.fn();

jest.mock('@gp/util/interaction-studio', () => ({
  sendEvent: mockSendEvent
}));

// Test MCP events
describe('MCP Tracking', () => {
  it('sends MCP events', () => {
    sendEvent('Test Event', {
      test_property: 'test_value'
    });

    expect(mockSendEvent).toHaveBeenCalledWith('Test Event', {
      test_property: 'test_value'
    });
  });
});
```

## Performance Considerations

### 1. Lazy Tracking
```typescript
// Only track in production
const shouldTrack = process.env.NODE_ENV === 'production';

if (shouldTrack) {
  sendEvent('Event Name', eventProperties);
}
```

### 2. Batch Tracking
```typescript
// Batch multiple events
const batchEvents = (events: Array<{ name: string; properties: any }>) => {
  events.forEach(event => {
    sendEvent(event.name, event.properties);
  });
};
```

### 3. Error Handling
```typescript
// Safe tracking with error handling
const safeTrackEvent = (eventName: string, properties?: any) => {
  try {
    sendEvent(eventName, properties);
  } catch (error) {
    console.error('Tracking error:', error);
    // Fallback to analytics only
  }
};
```

## Best Practices Summary

### 1. Always Use Tracking Components
- Use `ButtonWithTracking` instead of plain `Button`
- Use `PageTracking` for page-level tracking
- Use `FormWithTracking` for form interactions

### 2. Provide Meaningful Context
- Always include `position` for component tracking
- Use descriptive `label` values
- Include relevant `category` and `purpose`

### 3. Combine Analytics and MCP
- Use Analytics for user behavior tracking
- Use MCP for personalized experiences
- Coordinate both systems for comprehensive tracking

### 4. Test Tracking Implementation
- Mock tracking functions in tests
- Verify events are sent with correct data
- Test error handling scenarios

### 5. Monitor Performance
- Avoid tracking in development
- Batch events when possible
- Handle tracking errors gracefully
description:
globs:
alwaysApply: false
---
