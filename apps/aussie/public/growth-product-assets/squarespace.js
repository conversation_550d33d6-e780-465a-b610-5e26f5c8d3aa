(function () {
  var inboundParams = new URLSearchParams(window.location.search);
  var hasInboundUtm = false;

  inboundParams.forEach(function (value, key) {
    if (key.startsWith('utm_')) {
      hasInboundUtm = true;
    }
  });

  var defaultParams = {
    utm_source: 'broker_microsite',
    utm_campaign: 'broker_microsite',
    utm_medium: 'broker_microsite',
  };

  // Dynamic broker ID loading function
  function getBrokerId() {
    // Primary method: Get from the current script tag data attribute
    var currentScript =
      document.currentScript ||
      (function () {
        var scripts = document.getElementsByTagName('script');
        return scripts[scripts.length - 1];
      })();

    if (currentScript) {
      var brokerId = currentScript.getAttribute('data-broker-id');
      if (brokerId) {
        return brokerId;
      }
    }

    // Fallback: Try to find any script tag with data-broker-id
    var scriptTag = document.querySelector('script[data-broker-id]');
    if (scriptTag) {
      var dataBrokerId = scriptTag.getAttribute('data-broker-id');
      if (dataBrokerId) {
        return dataBrokerId;
      }
    }

    // Additional fallback: Try URL parameter
    var urlBrokerId = inboundParams.get('brokerid') || inboundParams.get('broker_id');
    if (urlBrokerId) {
      return urlBrokerId;
    }

    // Final fallback - you can remove this or set to empty string
    console.warn('No broker ID found. Please add data-broker-id attribute to the script tag.');
    return 'default-broker-id';
  }

  var brokerIdParam = {
    brokerid: getBrokerId(),
  };

  function addParams(urlObj, params) {
    Object.keys(params).forEach(function (key) {
      if (!urlObj.searchParams.has(key)) {
        urlObj.searchParams.set(key, params[key]);
      }
    });
  }

  var links = document.querySelectorAll('a[href]');

  links.forEach(function (link) {
    var href = link.getAttribute('href');
    if (!href || href.startsWith('#') || href.startsWith('javascript:')) return;

    var url;
    try {
      url = new URL(href, window.location.origin);
    } catch (e) {
      return;
    }

    // Only apply if URL contains aussie.com.au
    if (!url.hostname.includes('aussie.com.au')) return;

    if (hasInboundUtm) {
      inboundParams.forEach(function (value, key) {
        if (key.startsWith('utm_') && !url.searchParams.has(key)) {
          url.searchParams.set(key, value);
        }
      });
    } else {
      addParams(url, defaultParams);
    }

    addParams(url, brokerIdParam);
    link.setAttribute('href', url.toString());
  });
})();
