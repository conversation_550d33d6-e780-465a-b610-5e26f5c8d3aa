import { Container, Stack } from '@mantine/core';
import { Metadata } from 'next';

import { SimpleNavbar } from '@gp/feature/aussie-navbar';

export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh">
      <SimpleNavbar />
      <Container role="main" px="md" py="xxl" flex={1} bg="#F8F8F8" fluid>
        {children}
      </Container>
    </Stack>
  );
}
