import { NextRequest, NextResponse } from 'next/server';

interface BorrowingPowerRequest {
  data: {
    securityValue: number;
    loanAmount: number;
    numberOfApplicants: number;
    dependantAges: number[][];
    postcode: string;
    state: string;
    monthlyExpense: number;
    incomes: number[];
    liabilities: any[];
  };
}

/**
 * API route handler for borrowing power calculations
 */
export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as BorrowingPowerRequest;

    // Call the Lendi API
    const apiResponse = await fetch('https://api.lendi.com.au/v2/serviceability/ctp-mbp/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!apiResponse.ok) {
      console.error(`Lendi API error! Status: ${apiResponse.status}`);
      return NextResponse.json(
        {
          error: `API request failed with status ${apiResponse.status}`,
          details: await apiResponse.text(),
        },
        { status: apiResponse.status }
      );
    }

    const result = await apiResponse.json();
    console.log('Lendi API response:', result);

    // Validate the response structure
    if (!result.data || !result.data.minBorrowingPower || !result.data.maxBorrowingPower) {
      console.error('Invalid API response structure:', result);
      return NextResponse.json(
        { error: 'Invalid response structure from borrowing power API' },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error processing borrowing power calculation:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      },
      { status: 500 }
    );
  }
}
