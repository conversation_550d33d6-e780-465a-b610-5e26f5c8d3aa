import { NextRequest, NextResponse } from 'next/server';

// Define the data types for Zapier webhooks
type ZapierDataType = 'freemiumPremium';

const ZAPIER_ENQUIRY_URL = process.env['AUSSIE_FOR_AGENTS_ENQUIRY_WEBHOOK'] || '';

// Define the request body interface
interface AgentEnquiryRequest {
  dataType: ZapierDataType;
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    agencyName: string;
    role: string;
    state: string;
    postCode: string;
    [key: string]: any; // Allow for additional properties
  };
}

/**
 * API route handler for agent enquiry form submissions
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = (await request.json()) as AgentEnquiryRequest;
    const { dataType, formData } = body;

    // Get the appropriate Zapier webhook URL for the data type
    const zapierWebhookUrl = ZAPIER_ENQUIRY_URL;
    if (!zapierWebhookUrl) {
      return NextResponse.json(
        { error: `No Zapier webhook URL found for data type: ${dataType}` },
        { status: 400 }
      );
    }

    // Use the form data directly since we no longer need to remove the recaptchaToken
    const dataToSend = formData;

    console.log('Sending form data to Zapier:', dataToSend);

    // Send the data to Zapier
    const zapierResponse = await fetch(zapierWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dataToSend),
    });

    if (!zapierResponse.ok) {
      throw new Error(`Zapier API error! Status: ${zapierResponse.status}`);
    }

    const zapierResult = await zapierResponse.json();

    // Return success response
    return NextResponse.json({
      status: 'success',
      message: 'Form data submitted successfully',
      result: zapierResult,
    });
  } catch (error) {
    console.error('Error processing agent enquiry:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      },
      { status: 500 }
    );
  }
}
