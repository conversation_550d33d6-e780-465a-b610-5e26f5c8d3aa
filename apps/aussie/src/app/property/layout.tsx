import { Stack } from '@mantine/core';
import type { Metadata } from 'next';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { FinePrint } from '@gp/feature/property-hub';
import { SessionRedirect } from '@gp/shared/session-redirect';

import '@mantine/charts/styles.css';

export const metadata: Metadata = {
  title: {
    template: '%s | Aussie Homes',
    default: 'Aussie Homes',
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/property/`,
    siteName: 'Aussie Home Loans',
    description:
      'Thinking about buying a property to live in or an investment property? Aussie Homes lets you view property insights and trends, access suburb, state and LGA profiles to help you make informed property and investment decisions.',
    images: {
      url: 'https://www.aussie.com.au/assets/favicon.ico',
      width: 800,
      height: 600,
    },
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  verification: {
    google: 'ZkzV_8df7LSGNW-A-1PW0P4R8LyUILNgMSVFZWsALaw',
  },
  itunes: {
    appId: '6451372662',
  },
  alternates: {
    canonical: `${process.env.BASE_URL}/property/`,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh">
      <MultiNavbar />
      <SessionRedirect allowUnauthenticated>
        <div role="main">{children}</div>
      </SessionRedirect>
      <FinePrint />
      <Footer />
    </Stack>
  );
}
