import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Brand } from '@lendi/lala-utils';

import {
  getPropertyHubAPIs,
  parseListingSearchParams,
  PropertiesForSaleSearchResult,
  SearchFilter,
} from '@gp/data-access/property-hub';
import { getSuburbsBySlugs } from '@gp/database/property-hub';
import { PropertyListingSearchResults } from '@gp/feature/property-hub';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { getLDSingleKindContext, getServerFlag } from '@gp/shared/server-launchdarkly';

export const dynamic = 'force-dynamic';

export default async function Results({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string }>;
}) {
  const _searchParams = await searchParams;
  /**
   * areas can be three types:
   * 1. areas=nsw/eastwood-2122 => string
   * 2. areas=nsw/eastwood-2122&areas=nsw/zetland-2017 array
   * 3. areas=nsw/eastwood-2122,nsw/zetland-2017 string
   */
  const areasParam = _searchParams.areas as string[] | string;
  const _cookies = cookies();
  const targetId = _cookies.get('targetId')?.value || _cookies.get('target_id')?.value; // Generate a new targetId if not present
  const authsession = _cookies.get('authsession')?.value; // sessionId,userId
  const isAuthenticated = !!authsession;
  const [_, userId] = (authsession || '').split(',');
  const ldContext = getLDSingleKindContext(Brand.Aussie, targetId, userId);
  const [enablePreAuthRes, useV2Res] = await Promise.allSettled([
    getServerFlag('gp-unauth-property-search', ldContext, true),
    getServerFlag('gp-listing-search-v2', ldContext, true),
  ]);
  let areas: string[] = typeof areasParam === 'string' ? [] : areasParam;
  if (typeof areasParam === 'string') {
    areas = areasParam.split(',');
  }

  const filter: SearchFilter = parseListingSearchParams(new URLSearchParams(_searchParams));
  filter.areas = areas;
  if (!areas) {
    redirect('/property/');
  }
  const { getPublicPropertiesForSale } = getPropertyHubAPIs();

  const SEARCH_LIMIT = 24;
  const [searchRes, areaRes] = await Promise.allSettled([
    !isAuthenticated
      ? getPublicPropertiesForSale(areas, filter, filter.page, SEARCH_LIMIT)
      : Promise.resolve({ total: 0, propertyList: [], totalPage: 0 }),
    getSuburbsBySlugs(areas),
  ]);

  const searchResult: PropertiesForSaleSearchResult =
    searchRes.status === 'fulfilled'
      ? searchRes.value || { total: 0, propertyList: [], totalPage: 0 }
      : { total: 0, propertyList: [], totalPage: 0 };

  const suburbResult = areaRes.status === 'fulfilled' ? areaRes.value : [];
  if (
    !isAuthenticated &&
    _searchParams.page &&
    searchResult.total < (Number(_searchParams.page) - 1) * SEARCH_LIMIT
  ) {
    const newSearchParam = new URLSearchParams(_searchParams);
    newSearchParam.set('areas', areas.join(','));
    newSearchParam.delete('page');
    redirect(`/property/results/?${newSearchParam}`);
  }

  const suburbOptions = suburbResult.sort((a, b) => areas.indexOf(a.slug) - areas.indexOf(b.slug));

  return (
    <>
      <PropertyListingSearchResults
        defaultAreaOptions={
          suburbOptions.length > 0
            ? suburbOptions.map((suburb) => ({
                value: suburb.slug,
                label: suburb.name,
              }))
            : []
        }
        searchResult={searchResult}
        isAuthed={isAuthenticated}
        enablePreAuth={enablePreAuthRes.status === 'fulfilled' && enablePreAuthRes.value === true}
        useV2={useV2Res.status === 'fulfilled' && useV2Res.value === true}
      />
    </>
  );
}
