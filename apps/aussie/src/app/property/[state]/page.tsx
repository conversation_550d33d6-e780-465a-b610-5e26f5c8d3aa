import { cache } from 'react';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { getState } from '@gp/database/property-hub';
import { StateDetails } from '@gp/feature/property-hub';

export const dynamic = 'force-dynamic';

const BASE_URL = process.env.BASE_URL;

const cachedGetState = cache(async (slug: string) => {
  const property = await getState(slug);
  return property;
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ state: string }>;
}): Promise<Metadata> {
  // read route params
  const slug = (await params).state;
  const state = await cachedGetState(slug);
  if (!state) {
    return {
      robots: {
        index: false,
        follow: false,
      },
    };
  }
  const seoDescription = `Thinking about buying or investing in ${state?.name}? Our state profiles will help you uncover market trends, median property prices as well as narrow down your search by suburb or LGA.`;
  const seoUrl = `${process.env.BASE_URL}/property/${state?.slug}/`;

  return {
    title: `${state?.name} - Property Market and Insights`,
    description: seoDescription,
    openGraph: {
      description: seoDescription,
      url: seoUrl,
    },
    alternates: {
      canonical: seoUrl,
    },
  };
}

export default async function State({ params }: { params: { state: string } }) {
  const state = await cachedGetState(params.state);
  if (!state) return notFound();

  const breadcrumbListLdJson = {
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        item: {
          '@id': `${BASE_URL}`,
          name: 'Home',
        },
      },
      {
        '@type': 'ListItem',
        position: 2,
        item: {
          '@id': `${BASE_URL}/property/`,
          name: 'Property',
        },
      },
      {
        '@type': 'ListItem',
        position: 3,
        item: {
          '@id': `${BASE_URL}/property/${state.slug}/`,
          name: state.name,
        },
      },
    ],
  };

  const stateLdJson = {
    '@context': 'https://schema.org',
    '@type': 'State',
    name: state.name,
    description: `Thinking about buying or investing in ${state?.name}? Our state profiles will help you uncover market trends, median property prices as well as narrow down your search by suburb or LGA.`,
    url: `${BASE_URL}/property/${state.slug}/`,
    address: {
      '@type': 'PostalAddress',
      addressRegion: state.name,
      addressCountry: 'Australia',
    },
  };

  try {
    return (
      <>
        <script
          type="application/ld+json"
          key="org_json_ld"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListLdJson) }}
        />
        <script
          type="application/ld+json"
          key="state_json_ld"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(stateLdJson) }}
        />
        <StateDetails {...state} />
      </>
    );
  } catch (e) {
    console.error(e, `Failed to return ${state} state data`);
    return notFound();
  }
}
