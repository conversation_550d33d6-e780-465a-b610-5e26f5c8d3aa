import { cache } from 'react';
import type { Metadata } from 'next';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import { Brand } from '@lendi/lala-utils';

import { getPropertyHubAPIs, Property } from '@gp/data-access/property-hub';
import { getProperty } from '@gp/database/property-hub';
import { PropertyDetails, PropertyDetailsV2 } from '@gp/feature/property-hub';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { getLDSingleKindContext, getServerFlag } from '@gp/shared/server-launchdarkly';

const BASE_URL = process.env.BASE_URL;

const cachedGetProperty = cache(async (slug: string) => {
  const property = await getProperty(slug);
  return property;
});

const cachedGetListing = cache(async (campaignId?: string) => {
  if (!campaignId) return undefined;
  const { getPublicListingById } = getPropertyHubAPIs();
  const campaign = await getPublicListingById(campaignId);
  return campaign;
});

const mergeProperty = (clproperty?: Property, listing?: Property) => {
  if (!clproperty && !listing) return undefined;
  if (!listing) return clproperty;
  if (!clproperty) return listing;
  return {
    ...clproperty,
    address: listing.address ? listing.address : clproperty.address,
    location: listing.location ? listing.location : clproperty.location,
    listed: listing.listed ? listing.listed : clproperty.listed,
    listedDate: listing.listedDate ? listing.listedDate : clproperty.listedDate,
    data: { ...clproperty.data, ...listing.data },
  } as Property;
};

export async function generateMetadata({
  params,
  searchParams,
}: {
  params: Promise<{ state: string; area: string; address: string }>;
  searchParams: Promise<{ listingId: string | undefined }>;
}): Promise<Metadata> {
  // read route params
  const { state, area, address } = await params;
  const { listingId } = await searchParams;
  const listing = await cachedGetListing(listingId);
  const clProperty = await cachedGetProperty(`${state}/${area}/${address}`);
  const property = mergeProperty(clProperty, listing);
  const seoDescription = `${property?.data.bedrooms} bedroom ${property?.data.type} at ${property?.address}, ${property?.suburb.name}, ${property?.suburb.state.name} ${property?.suburb.postcode}. See median sales price, market insights, photos, floor plans and more property features.`;
  const seoUrl = `${process.env.BASE_URL}/property/${property?.slug}/`;

  if (!property) {
    return {
      robots: {
        index: false,
        follow: false,
      },
    };
  }

  return {
    title: `${property?.address}, ${property?.suburb.name}, ${property?.suburb.state.name} ${property?.suburb.postcode} - ${property?.data.type}`,
    description: seoDescription,
    openGraph: {
      description: seoDescription,
      url: seoUrl,
    },
    alternates: {
      canonical: seoUrl,
    },
  };
}

export default async function Address({
  params,
  searchParams,
}: {
  params: { state: string; area: string; address: string };
  searchParams: Promise<{ listingId: string | undefined }>;
}) {
  const _cookies = cookies();
  const targetId = _cookies.get('targetId')?.value || _cookies.get('target_id')?.value; // Generate a new targetId if not present
  const authsession = _cookies.get('authsession')?.value; // sessionId,userId
  const [_, userId] = (authsession || '').split(',');
  const ldContext = getLDSingleKindContext(Brand.Aussie, targetId, userId);
  const [useV2ListingPage] = await Promise.all([
    getServerFlag('gp-listing-page-v2', ldContext, true),
  ]);
  const { state, area, address } = params;
  const { listingId } = await searchParams;
  const clProperty = await cachedGetProperty(`${state}/${area}/${address}`);
  const listing = await cachedGetListing(listingId);
  const property = mergeProperty(clProperty, listing);
  if (!property) return notFound();

  const propertyLdJson = {
    '@context': 'https://schema.org',
    '@graph': [
      {
        '@type': 'House',
        name: property.address,
        description: `${property.data.bedrooms ? `${property.data.bedrooms}-bedroom ` : ''}${
          property.data.type
        } at ${property.address}, ${property.suburb.name}, ${property.suburb.state.name} ${
          property.suburb.postcode
        }`,
        url: `${BASE_URL}/property/${property.slug}/`,
        image: property.data.photo,
        address: {
          '@type': 'PostalAddress',
          streetAddress: property.address,
          addressLocality: property.suburb.name,
          addressRegion: property.suburb.state.name,
          postalCode: property.suburb.postcode,
          addressCountry: 'Australia',
        },
        ...(property.data.floorArea || property.data.landArea
          ? {
              floorSize: {
                '@type': 'QuantitativeValue',
                value: property.data.floorArea || property.data.landArea,
                unitCode: 'MTR',
                name: 'Square meters',
              },
            }
          : {}),
        numberOfRooms: (property.data.bedrooms ?? 0) + (property.data.bathrooms ?? 0),
        numberOfBedrooms: property.data.bedrooms,
      },
    ],
  };
  if (property.data.forSale) {
    propertyLdJson['@graph'].push({
      '@type': 'RealEstateListing',
      name: `${property.data.bedrooms ? `${property.data.bedrooms}-bedroom ` : ''}${
        property.data.type
      } in ${property.suburb.name}, ${property.suburb.state.name} for Sale`,
      url: `${BASE_URL}/property/${property.slug}/`,
      datePosted: property.data.listedDate
        ? property.data.listedDate
        : property.updatedAt
        ? new Date(property.updatedAt).toISOString().split('T')[0]
        : 'Not supplied',
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${BASE_URL}/property/${property.slug}/`,
      },
      image: property.data.photo ? property.data.photo : '',
      description: `${
        property.data.listingDescription
          ? property.data.listingDescription
          : `${property.data.bedrooms ? `${property.data.bedrooms}-bedroom ` : ''}${
              property.data.type
            } in ${property.suburb.name}, ${property.suburb.state.name} for Sale`
      }`,
      offers: {
        '@type': 'Demand',
        priceSpecification: `${
          property.data.estimatedValue?.estimate
            ? property.data.estimatedValue.estimate
            : 'Not Supplied'
        }`,
        availability: 'https://schema.org/InStock',
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any);
  }

  const breadcrumbListLdJson = {
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        item: {
          '@id': `${BASE_URL}`,
          name: 'Home',
        },
      },
      {
        '@type': 'ListItem',
        position: 2,
        item: {
          '@id': `${BASE_URL}/property/`,
          name: 'Property',
        },
      },
      {
        '@type': 'ListItem',
        position: 3,
        item: {
          '@id': `${BASE_URL}/property/${property.suburb.state.slug}/`,
          name: property.suburb.state.slug,
        },
      },
      {
        '@type': 'ListItem',
        position: 4,
        item: {
          '@id': `${BASE_URL}/property/${property.suburb.slug}/`,
          name: property.suburb.name,
        },
      },
      {
        '@type': 'ListItem',
        position: 5,
        item: {
          '@id': `${BASE_URL}/property/${property.slug}/`,
          name: property.address,
        },
      },
    ],
  };
  return (
    <>
      <script
        type="application/ld+json"
        key="org_json_ld"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListLdJson) }}
      />
      <script
        type="application/ld+json"
        key="property_json_ld"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(propertyLdJson) }}
      />
      {useV2ListingPage ? <PropertyDetailsV2 {...property} /> : <PropertyDetails {...property} />}
    </>
  );
}
