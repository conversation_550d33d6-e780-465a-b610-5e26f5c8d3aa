import { cache } from 'react';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { Suburb } from '@gp/data-access/property-hub';
import { getCouncil, getSuburb } from '@gp/database/property-hub';
import { CouncilDetails, SuburbDetails } from '@gp/feature/property-hub';

export const dynamic = 'force-dynamic';

const BASE_URL = process.env.BASE_URL;

const cachedGetCouncil = cache(async (slug: string) => {
  const property = await getCouncil(slug);
  return property;
});

const cachedGetSuburb = cache(async (slug: string) => {
  const property = await getSuburb(slug);
  return property;
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ state: string; area: string }>;
}): Promise<Metadata> {
  // read route params
  const { state, area } = await params;
  const isCouncil = area.endsWith('council');
  const entity = isCouncil
    ? await cachedGetCouncil(`${state}/${area}`)
    : await cachedGetSuburb(`${state}/${area}`);
  const postcode = entity && !isCouncil ? (entity as Suburb).postcode : '';
  const name = entity?.name;
  const seoDescription = `Is ${name}, ${
    postcode ? `${postcode} ` : ''
  }${state.toUpperCase()} the right area for you to buy or invest in property? Learn more about the ${
    isCouncil ? 'LGA' : 'suburb'
  }, including median property prices and current market trends.`;
  const seoUrl = `${process.env.BASE_URL}/property/${entity?.slug}/`;

  if (!entity) {
    return {
      robots: {
        index: false,
        follow: false,
      },
    };
  }
  return {
    title: `${name}, ${
      postcode ? `${postcode} ` : ''
    }${state.toUpperCase()} - Property Market and Insights`,
    description: seoDescription,
    openGraph: {
      description: seoDescription,
      url: seoUrl,
    },
    alternates: {
      canonical: seoUrl,
    },
  };
}

export default async function Area({ params }: { params: { state: string; area: string } }) {
  const { state, area } = params;
  const isCouncil = area.endsWith('council');

  try {
    if (isCouncil) {
      const council = await cachedGetCouncil(`${state}/${area}`);
      if (!council) return notFound();
      const breadcrumbListLdJson = {
        '@context': 'http://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            item: {
              '@id': `${BASE_URL}`,
              name: 'Home',
            },
          },
          {
            '@type': 'ListItem',
            position: 2,
            item: {
              '@id': `${BASE_URL}/property/`,
              name: 'Property',
            },
          },
          {
            '@type': 'ListItem',
            position: 3,
            item: {
              '@id': `${BASE_URL}/property/${council.state.slug}/`,
              name: council.state.name,
            },
          },
          {
            '@type': 'ListItem',
            position: 4,
            item: {
              '@id': `${BASE_URL}/property/${council.slug}/`,
              name: `${council.name}, ${council.state.name}`,
            },
          },
        ],
      };
      const cityLdJson = {
        '@context': 'https://schema.org',
        '@type': 'City',
        name: `${council.name}, ${council.state.name}`,
        description: `Is ${council.name}, ${council.state.name} the right area for you to buy or invest in property? Learn more about the LGA, including median property prices and current market trends.`,
        url: `${BASE_URL}/property/${council.slug}/`,
        address: {
          '@type': 'PostalAddress',
          addressLocality: council.name,
          addressRegion: council.state.name,
          addressCountry: 'Australia',
        },
      };
      return (
        <>
          <script
            type="application/ld+json"
            key="org_json_ld"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListLdJson) }}
          />
          <script
            type="application/ld+json"
            key="ct_json_ld"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(cityLdJson) }}
          />
          <CouncilDetails {...council} />
        </>
      );
    }
    const suburb = await cachedGetSuburb(`${state}/${area}`);
    if (!suburb) return notFound();
    const itemListElement = [
      {
        '@type': 'ListItem',
        position: 1,
        item: {
          '@id': `${BASE_URL}`,
          name: 'Home',
        },
      },
      {
        '@type': 'ListItem',
        position: 2,
        item: {
          '@id': `${BASE_URL}/property/`,
          name: 'Property',
        },
      },
      {
        '@type': 'ListItem',
        position: 3,
        item: {
          '@id': `${BASE_URL}/property/${suburb.state.slug}/`,
          name: suburb.state.name,
        },
      },
    ];
    if (suburb.council && suburb.council.slug) {
      itemListElement.push({
        '@type': 'ListItem',
        position: itemListElement.length + 1,
        item: {
          '@id': `${BASE_URL}/property/${suburb.council?.slug}/`,
          name: `${suburb.council?.name}, ${suburb.state.name}`,
        },
      });
    }
    itemListElement.push({
      '@type': 'ListItem',
      position: itemListElement.length + 1,
      item: {
        '@id': `${BASE_URL}/property/${suburb.slug}/`,
        name: `${suburb.name}, ${suburb.state.name}`,
      },
    });
    const breadcrumbListLdJson = {
      '@context': 'http://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement,
    };
    const cityLdJson = {
      '@context': 'https://schema.org',
      '@type': 'City',
      name: `${suburb.name}, ${suburb.postcode} ${suburb.state.name}`,
      description: `Is ${suburb.name}, ${suburb.postcode} ${suburb.state.name} the right area for you to buy or invest in property? Learn more about the suburb, including median property prices and current market trends.`,
      url: `${BASE_URL}/property/${suburb.slug}/`,
      address: {
        '@type': 'PostalAddress',
        addressLocality: suburb.name,
        addressRegion: suburb.council?.name || suburb.state.name,
        addressCountry: 'Australia',
      },
    };
    return (
      <>
        <script
          type="application/ld+json"
          key="org_json_ld"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListLdJson) }}
        />
        <script
          type="application/ld+json"
          key="ct_json_ld"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(cityLdJson) }}
        />
        <SuburbDetails {...suburb} />
      </>
    );
  } catch (e) {
    console.error(e, `Failed to return ${state}/${area} data`);
    return notFound();
  }
}
