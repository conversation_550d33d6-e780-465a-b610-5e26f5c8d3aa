import { Container } from '@mantine/core';
import type { Metadata } from 'next';

import { PropertySearch } from '@gp/feature/property-hub';

import classes from './style.module.css';

export const metadata: Metadata = {
  title: 'Search for Properties, Real Estate & Location Insights | Aussie Homes',
  description:
    'Thinking about buying a property to live in or an investment property? Aussie Homes lets you view property insights and trends, access suburb, state and LGA profiles to help you make informed property and investment decisions.',
};

export const dynamic = 'force-dynamic';

export default async function Property({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const defaultTab = (await searchParams).defaultTab as string;
  return (
    <Container role="main" fluid className={classes.main}>
      <PropertySearch defaultTab={defaultTab} />;
    </Container>
  );
}
