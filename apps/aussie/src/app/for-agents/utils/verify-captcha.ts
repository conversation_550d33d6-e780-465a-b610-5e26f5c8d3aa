declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

const getRecaptchaClientKey = (): string => {
  if (typeof window === 'undefined') return '';
  return window?.sessionStorage?.getItem('GP_RECAPTCHA_CLIENT_KEY') || '';
};

export const onReadyCaptcha = (
  onSuccess: (token?: string) => void,
  onError: (error: { error: string }) => void
): void => {
  try {
    const RECAPTCHA_CLIENT_KEY = getRecaptchaClientKey();

    if (!window.grecaptcha) {
      onError({ error: 'reCAPTCHA not loaded properly' });
      return;
    }

    window.grecaptcha.ready(() => {
      window.grecaptcha
        .execute(RECAPTCHA_CLIENT_KEY, { action: 'submit' })
        .then((token: string) => {
          onSuccess(token);
        })
        .catch(() => {
          onError({ error: 'reCAPTCHA verification failed' });
        });
    });
  } catch (error) {
    onError({ error: 'reCAPTCHA not loaded properly' });
  }
};
