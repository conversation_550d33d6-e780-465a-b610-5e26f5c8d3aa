/**
 * Services for submitting form data directly to Zapier
 */

export enum ZapierDataType {
  agentEnquiry = 'freemiumPremium', // Using the existing Zapier hook for freemiumPremium
}

interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  agencyName: string;
  role: string;
  state: string;
  postCode: string;
  recaptchaToken?: string;
}

// Zapier webhook URL
const ZAPIER_ENQUIRY_URL = 'https://hooks.zapier.com/hooks/catch/9825322/bq90a8u/';

export const sendZapierData = async (
  type: ZapierDataType,
  formData: FormValues
): Promise<{ status: 'success' | 'error'; message?: string }> => {
  try {
    return new Promise((resolve) => {
      const iframe = document.createElement('iframe');
      iframe.name = 'zapier-submit-frame';
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      const form = document.createElement('form');
      form.method = 'POST';
      form.action = ZAPIER_ENQUIRY_URL;
      form.target = 'zapier-submit-frame';
      form.style.display = 'none';

      Object.entries(formData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = String(value);
          form.appendChild(input);
        }
      });

      const timestampInput = document.createElement('input');
      timestampInput.type = 'hidden';
      timestampInput.name = '_timestamp';
      timestampInput.value = Date.now().toString();
      form.appendChild(timestampInput);

      const timeoutId = setTimeout(() => {
        console.log('Zapier submission timed out, but assuming success');
        cleanup();
        resolve({
          status: 'success',
          message: 'Form submitted (timeout occurred, but assuming success)',
        });
      }, 5000);

      iframe.onload = () => {
        clearTimeout(timeoutId);
        console.log('Form data submitted successfully to Zapier');
        cleanup();
        resolve({
          status: 'success',
          message: 'Form data submitted successfully',
        });
      };

      const cleanup = () => {
        if (form.parentNode) document.body.removeChild(form);
        if (iframe.parentNode) document.body.removeChild(iframe);
      };

      document.body.appendChild(form);
      form.submit();
    });
  } catch (error) {
    console.error('Error submitting form data:', error);
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};
