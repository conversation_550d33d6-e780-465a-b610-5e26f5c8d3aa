/**
 * Utility functions for form validation
 */

/**
 * Check if a string contains a URL
 */
export const isUrlPresent = (value: string): boolean => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return urlRegex.test(value);
};

/**
 * Validate Australian phone number
 */
export const isValidPhoneNumber = (value: string): boolean => {
  // Allow for formats like: +61 4XX XXX XXX, 04XX XXX XXX, 4XX XXX XXX
  // Remove spaces, dashes, and parentheses
  const cleanedValue = value.replace(/[\s\-()]/g, '');

  // Check if it's a valid Australian mobile number
  if (cleanedValue.startsWith('+61')) {
    return /^\+61\d{9}$/.test(cleanedValue);
  } else if (cleanedValue.startsWith('0')) {
    return /^0\d{9}$/.test(cleanedValue);
  } else {
    return /^\d{9}$/.test(cleanedValue);
  }
};

/**
 * Check if a string contains only alphabetic characters
 */
export const isAlpha = (value: string): boolean => {
  return /^[A-Za-z]+$/.test(value);
};

/**
 * Check if a string is empty
 */
export const isEmpty = (value: string): boolean => {
  return value.trim() === '';
};

/**
 * Check if a string is a valid email
 */
export const isEmail = (value: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
};

/**
 * Check if a string is a valid integer within a range
 */
export const isInt = (
  value: string,
  options: { allow_leading_zeroes?: boolean; min?: number; max?: number } = {}
): boolean => {
  const { allow_leading_zeroes = false, min, max } = options;

  // Check if it's a valid integer
  const regex = allow_leading_zeroes ? /^\d+$/ : /^(0|[1-9]\d*)$/;
  if (!regex.test(value)) {
    return false;
  }

  // Convert to number for range checking
  const num = parseInt(value, 10);

  // Check range if specified
  if (min !== undefined && num < min) {
    return false;
  }
  if (max !== undefined && num > max) {
    return false;
  }

  return true;
};
