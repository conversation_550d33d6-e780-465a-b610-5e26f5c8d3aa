import { Stack } from '@mantine/core';
import type { Metadata } from 'next';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { SessionRedirect } from '@gp/shared/session-redirect';

export const metadata: Metadata = {
  title: 'Aussie for Agents | VPA-Free Listings | Aussie Home Loans',
  description:
    'Access unlimited free listings, 5 million+ customers, and qualified vendor leads with Aussie for Agents. Our VPA-free platform helps real estate agents grow their business with seamless CRM integration and premium brand visibility.',
  openGraph: {
    type: 'website',
    url: process.env.BASE_URL,
    siteName: 'Aussie Home Loans',
    description:
      'Access unlimited free listings, 5 million+ customers, and qualified vendor leads with Aussie for Agents. Our VPA-free platform helps real estate agents grow their business with seamless CRM integration and premium brand visibility.',
    images: {
      url: 'https://www.aussie.com.au/assets/favicon.ico',
      width: 800,
      height: 600,
    },
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  verification: {
    google: 'ZkzV_8df7LSGNW-A-1PW0P4R8LyUILNgMSVFZWsALaw',
  },
  itunes: {
    appId: '6451372662',
  },
  alternates: {
    canonical: `${process.env.BASE_URL}/for-agents/`,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh">
      <MultiNavbar />
      <SessionRedirect allowUnauthenticated>{children}</SessionRedirect>
      <Footer />
    </Stack>
  );
}
