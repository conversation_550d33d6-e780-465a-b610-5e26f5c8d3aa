export const dynamic = 'force-dynamic';

import { PageTracking } from '@gp/ui/components';

import { AdvantagesSection } from './components/AdvantagesSection';
import { AgencyLogos } from './components/AgencyLogos';
import { AnnouncementBanner } from './components/AnnouncementBanner';
import { CollaborationSection } from './components/CollaborationSection';
import { DisclaimerSection } from './components/DisclaimerSection';
import { HeroSection } from './components/HeroSection';
import { PrecisionDataSection } from './components/PrecisionDataSection';
import { RecaptchaScript } from './components/RecaptchaScript';
import { SellReadyVendorsSection } from './components/SellReadyVendorsSection';
import { UnlimitedListingsSection } from './components/UnlimitedListingsSection';
import { WinMoreSection } from './components/WinMoreSection';
import { YourBrandCustomersSection } from './components/YourBrandCustomersSection';
import { YourBrandTopMindSection } from './components/YourBrandTopMindSection';

export default function ForAgentsPage() {
  return (
    <PageTracking name="For Agents" category="AUSSIE_HOMES_FOR_AGENTS" withMarketingCloudTracking>
      <AnnouncementBanner />
      <RecaptchaScript />
      <HeroSection />
      <AdvantagesSection />
      <WinMoreSection />
      <AgencyLogos />
      <UnlimitedListingsSection />
      <YourBrandCustomersSection />
      <PrecisionDataSection />
      <SellReadyVendorsSection />
      <YourBrandTopMindSection />
      <CollaborationSection />
      <DisclaimerSection />
    </PageTracking>
  );
}
