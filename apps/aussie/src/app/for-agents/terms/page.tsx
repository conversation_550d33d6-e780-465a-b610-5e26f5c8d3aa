import React from 'react';
import type { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Dynamically import TermsContent with ssr: false
const TermsContent = dynamic(() => import('./TermsContent'), {
  ssr: false,
});

export const metadata: Metadata = {
  title: 'Terms & Conditions | Aussie for Agents | Aussie Home Loans',
  description:
    'Terms and conditions for Aussie for Agents platform. Read our terms of use for real estate agents using the Aussie for Agents platform.',
};

export default function TermsPage() {
  return (
    <>
      <TermsContent />
    </>
  );
}
