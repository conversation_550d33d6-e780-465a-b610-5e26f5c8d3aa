'use client';

import React from 'react';
import {
  BackgroundImage,
  Box,
  Button,
  ButtonProps,
  Container,
  Flex,
  Image,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';

import { accentYellow, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

const MantineButton = ({
  children,
  onClick,
  ...props
}: { children: React.ReactNode; onClick?: React.MouseEventHandler<HTMLButtonElement> } & Omit<
  ButtonProps,
  'onClick'
>) => (
  <Button
    radius="xl"
    onClick={onClick}
    fw={700}
    h={45}
    w={159}
    styles={{
      root: {
        padding: '9.5px 16px',
      },
      label: {
        fontFamily: "'Barlow', sans-serif",
        fontSize: '16px',
        lineHeight: 1.25,
      },
    }}
    {...props}
  >
    {children}
  </Button>
);

const IconTextItem = ({ iconSrc, children }: { iconSrc: string; children: React.ReactNode }) => (
  <Flex align="center" gap="md">
    <Box w={73} h={73}>
      <Image src={iconSrc} alt="" w={73} h={73} />
    </Box>
    <Box>{children}</Box>
  </Flex>
);

const RegularText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz={18}
    lh={1.333}
    c={white}
    span
  >
    {children}
  </Text>
);

export const YourBrandTopMindSection = () => {
  const [opened, { open, close }] = useDisclosure(false);
  const isMobile = useMediaQuery('(max-width: 767px)');

  return (
    <Box
      component="section"
      pos="relative"
      py={{ base: 'xl', md: 80 }}
      style={{ overflow: 'hidden' }}
      mih={700}
    >
      {isMobile ? (
        <BackgroundImage
          src="/growth-product-assets/for-agents/unlimited-listings-bg-mobile.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          style={{
            zIndex: 0,
            backgroundSize: 'auto 100%',
            backgroundRepeat: 'repeat-x',
          }}
        />
      ) : (
        <BackgroundImage
          src="/growth-product-assets/for-agents/your-brand-top-of-mind-background.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          style={{ zIndex: 0 }}
        />
      )}
      <Container
        size="xl"
        pos="relative"
        style={{ zIndex: 1 }}
        px={{ base: 29, sm: 40, md: 70, lg: 80 }}
      >
        {isMobile ? (
          <Flex direction="column" align="flex-start" gap="xl">
            {}
            <Box w="100%" ta="center">
              <Image
                src="/growth-product-assets/for-agents/agents-image.png"
                alt="Real estate agent illustration"
                maw={600}
                fit="contain"
                mx="auto"
              />
            </Box>

            <Box ta="left">
              <Title
                order={2}
                fz={36}
                fw={700}
                c={white}
                mb="xl"
                lh={1.047}
                style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
              >
                <span style={{ color: accentYellow }}>Your brand, </span>top of mind
              </Title>

              <Stack gap="xl" mb="xl">
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-link.svg">
                  <RegularText>
                    Uncover hidden vendor opportunities through website buyer enquiries
                  </RegularText>
                </IconTextItem>
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-eye.svg">
                  <RegularText>Gain premium brand visibility</RegularText>
                </IconTextItem>
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-house.svg">
                  <RegularText>
                    Generate more quality appraisal opportunities effortlessly
                  </RegularText>
                </IconTextItem>
              </Stack>

              <MantineButton
                bg={primaryPurple}
                c={white}
                onClick={open}
                styles={{
                  root: {
                    border: '1px solid white',
                    '&:hover': { backgroundColor: '#5F2D85' },
                    textTransform: 'uppercase',
                  },
                }}
                h={45}
                w={159}
              >
                Learn more
              </MantineButton>
            </Box>
          </Flex>
        ) : (
          <Flex direction="row" align="center" gap="6rem">
            <Box style={{ flex: 3 / 5 }} ta="center">
              <Image
                src="/growth-product-assets/for-agents/agents-image.png"
                alt="Real estate agent illustration"
                maw={600}
                fit="contain"
                mx="auto"
              />
            </Box>
            <Box style={{ flex: 2 / 5 }} ta="left">
              <Title
                order={2}
                fz={{ md: 36, lg: 43 }}
                fw={700}
                c={white}
                mb="xl"
                lh={1.047}
                style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
              >
                <span style={{ color: accentYellow }}>Your brand, </span>top of mind
              </Title>

              <Stack gap="xl" mb="xl">
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-link.svg">
                  <RegularText>
                    Uncover hidden vendor opportunities through website buyer enquiries
                  </RegularText>
                </IconTextItem>
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-eye.svg">
                  <RegularText>Gain premium brand visibility</RegularText>
                </IconTextItem>
                <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-house.svg">
                  <RegularText>
                    Generate more quality appraisal opportunities effortlessly
                  </RegularText>
                </IconTextItem>
              </Stack>

              <MantineButton
                bg={primaryPurple}
                c={white}
                onClick={open}
                styles={{
                  root: {
                    border: '1px solid white',
                    '&:hover': { backgroundColor: '#5F2D85' },
                  },
                  label: { textTransform: 'initial' },
                }}
              >
                Learn more
              </MantineButton>
            </Box>
          </Flex>
        )}{' '}
      </Container>
      <EnquiryFormModal opened={opened} onClose={close} />
    </Box>
  );
};
