import { BackgroundImage, Box, Button, Container, Flex, Group, Image, Title } from '@mantine/core';

import { accentYellow, darkPurpleText, lightPurple, white } from '../styles/colors';

export const HeroSection = () => {
  return (
    <Box bg={lightPurple} pos="relative" style={{ overflow: 'hidden', zIndex: 1 }}>
      <Box
        pos="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        style={{ zIndex: 2 }}
        display={{ base: 'block', md: 'none' }}
      >
        <BackgroundImage
          src="/growth-product-assets/for-agents/agents-mobile-bg-vector.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          style={{ objectFit: 'contain', opacity: 1 }}
        />
      </Box>
      <Box
        pos="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        style={{ zIndex: 2 }}
        display={{ base: 'none', md: 'block' }}
      >
        <BackgroundImage
          src="/growth-product-assets/for-agents/hero-bg-vector.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          style={{ objectFit: 'contain', opacity: 1 }}
        />
      </Box>
      <Box
        pt={{ base: 30, md: 60 }}
        px={{ base: 29, sm: 40, md: 70, lg: 140 }}
        pb={{ base: 60, md: 220 }}
        pos="relative"
        style={{ zIndex: 3 }}
      >
        <Container size="xl" maw={1160}>
          <Flex direction={{ base: 'column', lg: 'row' }} align="center" justify="space-between">
            <Box style={{ flex: 2 / 5 }}>
              <Box w="100%">
                <Group justify="flex-start" mb={{ base: 40, lg: 60 }} display="inline-flex">
                  {' '}
                  <Image
                    src="/growth-product-assets/for-agents/aussie-for-agents-logo.svg"
                    alt="Aussie for Agents Logo"
                    w={{ base: 250, md: 330 }}
                    h="auto"
                  />
                </Group>
              </Box>
              <Title
                order={1}
                c={white}
                fz={{ base: 38, sm: 45, lg: 50 }}
                fw={700}
                lh={{ base: 1.05, lg: 1.05 }}
                mb={{ base: 25, lg: 35 }}
                style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
              >
                Agents, it’s time for a change.
                <br />
                <span style={{ color: accentYellow }}>VPA-free listings. Forever.</span>
              </Title>
              <Button
                bg={white}
                c={darkPurpleText}
                size="md"
                radius="xl"
                mt={{ base: 30, lg: 40 }}
                fw={700}
                component="a"
                href="https://agents.aussie.com.au/#/signup"
                target="_blank"
                rel="noopener noreferrer"
                style={{ height: 40, paddingLeft: '1.4rem', paddingRight: '1.4rem' }}
                w={{ base: 140, md: 160 }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: '#e6bb00',
                    },
                  },
                  label: {
                    fontFamily: "'Barlow', sans-serif",
                    textTransform: 'unset',
                    fontSize: '0.875rem',
                    '@media (min-width: 768px)': {
                      fontSize: '1rem',
                    },
                  },
                }}
              >
                Sign up
              </Button>
              <Button
                bg={accentYellow}
                c={darkPurpleText}
                size="md"
                radius="xl"
                mt={{ base: 30, lg: 40 }}
                ml={10}
                fw={700}
                component="a"
                href="https://agents.aussie.com.au/#/login"
                target="_blank"
                rel="noopener noreferrer"
                style={{ height: 40, paddingLeft: '1.4rem', paddingRight: '1.4rem' }}
                w={{ base: 140, md: 160 }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: '#ffd11f',
                    },
                  },
                  label: {
                    fontFamily: "'Barlow', sans-serif",
                    textTransform: 'unset',
                    fontSize: '0.875rem',
                    '@media (min-width: 768px)': {
                      fontSize: '1rem',
                    },
                  },
                }}
              >
                Log in
              </Button>
            </Box>

            {}
            <Box
              style={{ flex: 3 / 5 }}
              pos="relative"
              mt={{ base: 40, lg: 0 }}
              mb={{ base: 45, lg: 0 }}
            >
              <Image
                src="/growth-product-assets/for-agents/phone-laptop-mockup.png"
                alt="Laptop showing Aussie for Agents platform"
                radius="md"
                style={{ zIndex: 10, maxWidth: '100%' }}
                maw={{ base: '100%', lg: '105%' }}
              />
            </Box>
          </Flex>
        </Container>
      </Box>
    </Box>
  );
};
