'use client';

import React, { useEffect, useState } from 'react';
import Script from 'next/script';

export function RecaptchaScript() {
  const [recaptchaKey, setRecaptchaKey] = useState<string>('');

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const key = window.sessionStorage.getItem('GP_RECAPTCHA_CLIENT_KEY') || '';
    setRecaptchaKey(key);
  }, []);

  if (!recaptchaKey) {
    return null;
  }

  return (
    <Script
      src={`https://www.google.com/recaptcha/api.js?render=${recaptchaKey}`}
      strategy="afterInteractive"
      id="recaptcha-script"
    />
  );
}
