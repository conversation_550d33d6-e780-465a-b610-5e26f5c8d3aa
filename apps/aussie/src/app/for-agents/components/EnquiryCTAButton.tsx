'use client';

import { Button, ButtonProps } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { darkPurpleText, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

interface EnquiryCTAButtonProps extends Omit<ButtonProps, 'onClick'> {
  buttonText: string;
  buttonVariant?: 'default' | 'filled';
}

export function EnquiryCTAButton({
  buttonText,
  buttonVariant = 'default',
  ...rest
}: EnquiryCTAButtonProps) {
  const [opened, { open, close }] = useDisclosure(false);

  const baseStyles = {
    root: {
      '&:hover': { backgroundColor: darkPurpleText },
    },
    label: {
      fontFamily: "'Barlow', sans-serif",
      fontSize: '1rem',
    },
  };

  const variantStyles =
    buttonVariant === 'filled'
      ? {
          bg: primaryPurple,
          c: white,
          bd: '1px solid transparent',
        }
      : {
          bg: white,
          c: primaryPurple,
          bd: '1px solid',
        };

  return (
    <>
      <Button
        onClick={open}
        radius="xl"
        fw={700}
        size="md"
        styles={{
          ...baseStyles,
          root: { ...baseStyles.root, ...variantStyles },
        }}
        {...rest}
      >
        {buttonText}
      </Button>
      <EnquiryFormModal opened={opened} onClose={close} />
    </>
  );
}
