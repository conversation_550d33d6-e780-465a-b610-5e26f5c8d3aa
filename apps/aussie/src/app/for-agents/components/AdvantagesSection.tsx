'use client';

import React from 'react';
import {
  Box,
  Button,
  Center,
  Container,
  Flex,
  Image,
  Paper,
  SimpleGrid,
  Text,
  Title,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { darkGreyText, darkPurpleText, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

const cardShadow = 'lg';

interface AdvantageCardProps {
  iconSrc: string;
  title: React.ReactNode;
  description: string;
}

const AdvantageCard: React.FC<AdvantageCardProps> = ({ iconSrc, title, description }) => (
  <Paper
    shadow={cardShadow}
    radius={22}
    bg={white}
    h={{ base: 'auto', md: 295 }}
    w={{ base: '100%', md: 212 }}
    mx="auto"
    p={{ base: 'md', md: 0 }}
    pt={{ base: 'md', md: 10 }}
    style={{
      overflow: 'hidden',
    }}
  >
    <Flex direction={{ base: 'row', md: 'column' }} align={{ base: 'flex-start', md: 'center' }}>
      <Box
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
        }}
        mr={{ base: 'md', md: 0 }}
        pt={{ base: 0, md: 'lg' }}
        pb={{ base: 0, md: 'md' }}
        w={{ base: 70, md: '100%' }}
        h={{ base: 70, md: 100 }}
      >
        <Image src={iconSrc} alt="" maw={{ base: 70, md: 100 }} mah={70} fit="contain" /> {}
      </Box>

      <Box
        style={{ flexGrow: 1 }}
        ta={{ base: 'left', md: 'center' }}
        px={{ base: 0, md: 'sm' }}
        pb={{ base: 0, md: 'md' }}
        pt={{ base: 0, md: 8 }}
        mt={{ base: 0, md: 0 }}
      >
        <Title
          order={3}
          fz={{ base: 20, md: 22 }}
          fw={700}
          c={darkPurpleText}
          mb={{ base: 'xs', md: 'sm' }}
          lh={1.18}
          style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
        >
          {title}
        </Title>
        <Text
          c={darkGreyText}
          fz={{ base: 14, md: 16 }}
          lh={1.25}
          style={{ fontFamily: "'Barlow', sans-serif" }}
        >
          {description}
        </Text>
      </Box>
    </Flex>
  </Paper>
);

export const AdvantagesSection = () => {
  const [opened, { open, close }] = useDisclosure(false);
  const advantages: AdvantageCardProps[] = [
    {
      iconSrc: '/growth-product-assets/for-agents/icon-free-listings.svg',
      title: (
        <>
          VPA-free
          <br />
          listings
        </>
      ),
      description:
        'Forever on Aussie’s Property Marketplace. Off-market, pre-market and on-market.',
    },
    // {
    //   iconSrc: '/growth-product-assets/for-agents/icon-vpa-free.svg',
    //   title: (
    //     <>
    //       VPA-free
    //       <br />
    //       listings
    //     </>
    //   ),
    //   description: 'On Aussie’s Property Marketplace. Simple CRM connections.',
    // },
    {
      iconSrc: '/growth-product-assets/for-agents/icon-customers.svg',
      title: (
        <>
          Leverage our
          <br />
          5m+ customers
        </>
      ),
      description: 'Including high-intent, pre-approved buyers and vendors.',
    },
    {
      iconSrc: '/growth-product-assets/for-agents/icon-vendors.svg',
      title: (
        <>
          Qualified,
          <br />
          ready vendors
        </>
      ),
      description: 'Potential appraisal opportunities through Aussie’s Seller Assist',
    },
    {
      iconSrc: '/growth-product-assets/for-agents/icon-prospects.svg',
      title: (
        <>
          Prioritised
          <br />
          prospects
        </>
      ),
      description: 'Vendor lead scoring to help close more deals.',
    },
  ];

  return (
    <Box
      component="section"
      py={{ base: 40, md: 60 }}
      px={{ base: 29, sm: 40, md: 70, lg: 140 }}
      mt={{ base: -120, md: -205 }}
      style={{ zIndex: 40, position: 'relative' }}
    >
      <Container size="xl" maw={935}>
        <SimpleGrid
          cols={{ base: 1, sm: 2, lg: 4 }}
          spacing={{ base: '30px' }}
          mb={{ base: 'lg', md: 'xl' }}
        >
          {advantages.map((advantage, index) => (
            <AdvantageCard key={index} {...advantage} />
          ))}
        </SimpleGrid>
        <Center>
          <Button
            bg={white}
            c={primaryPurple}
            onClick={open}
            bd={'1px solid'}
            miw={{ base: 160, md: 180 }}
            mt={{ base: 0, md: 5 }}
            mb={{ base: -60, md: -100 }}
            size="md"
            styles={{
              root: {
                '&:hover': { backgroundColor: darkPurpleText },
                '@media (max-width: 768px)': {
                  padding: '6px 12px',
                },
              },
              label: {
                textTransform: 'initial',
                fontSize: 16,
                '@media (max-width: 768px)': {
                  fontSize: 14,
                },
              },
            }}
          >
            Contact us
          </Button>
        </Center>
      </Container>
      <EnquiryFormModal opened={opened} onClose={close} />
    </Box>
  );
};
