'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>ert, Box, Button, Grid, Select, Text, TextInput } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import Link from 'next/link';

import { ModalWithTracking } from '@gp/ui/components';

import { AGENT_ROLES, STATES } from '../utils/constants';
import {
  isAlpha,
  isEmail,
  isEmpty,
  isInt,
  isUrlPresent,
  isValidPhoneNumber,
} from '../utils/validators';
import { sendZapierData, ZapierDataType } from '../utils/zapier-services';

interface EnquiryFormModalProps {
  opened: boolean;
  onClose: () => void;
}

interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  agencyName: string;
  role: string;
  state: string;
  postCode: string;
}

const initialValues: FormValues = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  agencyName: '',
  state: '',
  postCode: '',
  role: '',
};

export function EnquiryFormModal({ opened, onClose }: EnquiryFormModalProps) {
  const { height, width } = useViewportSize();
  const [formValues, setFormValues] = useState<FormValues>(initialValues);
  const [errorValues, setErrorValues] = useState<FormValues>(initialValues);
  const [isValidForm, setIsValidForm] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submittedStatus, setSubmittedStatus] = useState<'pending' | 'success' | 'fail'>('pending');

  const { agencyName, email, firstName, lastName, phone, role, state, postCode } = formValues;

  useEffect(() => {
    if (typeof window === 'undefined') return;
    // Check if there's any saved agent data in localStorage
    const agentDetails = window.localStorage.getItem('agent')
      ? JSON.parse(window.localStorage.getItem('agent') || '{}')
      : {};

    setFormValues((values) => ({ ...values, ...agentDetails }));

    return () => {
      if (typeof window === 'undefined') return;

      // Clean up when component unmounts
      window.localStorage.removeItem('agent');
    };
  }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    await submitForm();
  };

  const submitForm = async () => {
    setIsSubmitting(true);

    const result = validateForm(formValues);
    const { isValidForm, errorValues } = result;

    if (!isValidForm) {
      setErrorValues(errorValues);
      setIsSubmitting(false);
    } else {
      try {
        const response = await sendZapierData(ZapierDataType.agentEnquiry, formValues);

        if (response.status === 'success') {
          setSubmittedStatus('success');
          setFormValues(initialValues);
          // Don't close the modal automatically, let the user see the success state
        } else {
          setSubmittedStatus('fail');
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        setSubmittedStatus('fail');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const settingFormValue = (obj: { value: string; field: keyof FormValues }) => {
    const { value, field } = obj;
    setFormValues((formValues) => ({ ...formValues, [field]: value }));
  };

  const validateForm = (formValues: FormValues) => {
    let isValid = true;
    const error = { ...errorValues };

    // Check for empty fields and URLs
    Object.keys(formValues).forEach((key) => {
      const field = key as keyof FormValues;
      const value = formValues[field];

      if (isEmpty(value)) {
        error[field] = '*Mandatory field';
        isValid = false;
      } else if (isUrlPresent(value)) {
        error[field] = '*URL not allowed';
        isValid = false;
      } else {
        error[field] = '';
      }
    });

    if (!isValid) {
      setIsValidForm(isValid);
      return { isValidForm: isValid, errorValues: error };
    }

    // Validate specific fields
    Object.keys(formValues).forEach((key) => {
      const field = key as keyof FormValues;
      const value = formValues[field];

      switch (field) {
        case 'firstName':
        case 'lastName':
          if (!isAlpha(value)) {
            error[field] = '*Only letters are allowed';
            isValid = false;
          }
          break;
        case 'postCode':
          if (!isInt(value, { allow_leading_zeroes: true, min: 800, max: 9999 })) {
            error[field] = '*Invalid postcode';
            isValid = false;
          }
          break;
        case 'email':
          if (!isEmail(value)) {
            error.email = '*Invalid email';
            isValid = false;
          }
          break;
        case 'phone':
          if (!isValidPhoneNumber(value)) {
            error.phone = '*Invalid phone number';
            isValid = false;
          }
          break;
      }
    });

    setIsValidForm(isValid);
    return { isValidForm: isValid, errorValues: error };
  };

  const handleBlur = (obj: { value: string; field: keyof FormValues }) => {
    const { value, field } = obj;
    const error = { ...errorValues };

    if (isEmpty(value)) {
      error[field] = '*Mandatory field';
      setErrorValues(error);
      return;
    }

    if (isUrlPresent(value)) {
      error[field] = '*URL not allowed';
      setErrorValues(error);
      return;
    }

    error[field] = '';

    switch (field) {
      case 'firstName':
      case 'lastName':
        error[field] = !isAlpha(value) ? '*Only letters are allowed' : '';
        break;
      case 'email':
        error.email = !isEmail(value) ? '*Invalid email' : '';
        break;
      case 'phone':
        error.phone = !isValidPhoneNumber(value) ? '*Invalid phone number' : '';
        break;
      case 'postCode':
        error[field] = !isInt(value, { allow_leading_zeroes: true, min: 800, max: 9999 })
          ? '*Invalid postcode'
          : '';
        break;
    }

    setErrorValues(error);
  };

  const handleChange = (obj: { value: string; field: keyof FormValues }) => {
    const { value, field } = obj;
    settingFormValue(obj);

    // Validate the form with the new value
    validateForm({ ...formValues, [field]: value });
  };

  return (
    <ModalWithTracking
      opened={opened}
      onClose={onClose}
      title="Contact Us"
      name="Agent Enquiry Modal"
      centered
      size="lg"
    >
      <form
        onSubmit={handleSubmit}
        style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
      >
        <Box style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {/* Scrollable content area */}
          <Box
            style={{
              flex: '1 1 auto',
              overflowY: 'auto',
              maxHeight: width <= 768 ? 'calc(80vh - 120px)' : `calc(${height * 0.8}px - 120px)`,
              paddingInline: 20,
              paddingTop: 20,
              paddingBottom: 30,
            }}
          >
            {submittedStatus === 'success' ? (
              <Alert title="Success!" color="green">
                Thanks for your interest. A member of our team will be in touch shortly.
              </Alert>
            ) : submittedStatus === 'fail' ? (
              <Alert title="Success!" color="green">
                Thanks for your interest. A member of our team will be in touch shortly.
              </Alert>
            ) : (
              <Grid>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="First Name"
                    placeholder="Enter your first name"
                    required
                    value={firstName}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'firstName' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'firstName' })}
                    error={errorValues.firstName}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Last Name"
                    placeholder="Enter your last name"
                    required
                    value={lastName}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'lastName' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'lastName' })}
                    error={errorValues.lastName}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="Role"
                    placeholder="Select your role"
                    required
                    data={AGENT_ROLES}
                    value={role}
                    onChange={(value) => handleChange({ value: value || '', field: 'role' })}
                    onBlur={() => handleBlur({ value: role, field: 'role' })}
                    error={errorValues.role}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Name of Agency"
                    placeholder="Enter agency name"
                    required
                    value={agencyName}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'agencyName' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'agencyName' })}
                    error={errorValues.agencyName}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    type="email"
                    label="Email"
                    placeholder="Enter your email"
                    required
                    value={email}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'email' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'email' })}
                    error={errorValues.email}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    type="tel"
                    label="Phone"
                    placeholder="Enter your phone number"
                    required
                    value={phone}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'phone' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'phone' })}
                    error={errorValues.phone}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <Select
                    label="State"
                    placeholder="Select your state"
                    required
                    data={STATES}
                    value={state}
                    onChange={(value) => handleChange({ value: value || '', field: 'state' })}
                    onBlur={() => handleBlur({ value: state, field: 'state' })}
                    error={errorValues.state}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 6 }}>
                  <TextInput
                    label="Postcode"
                    placeholder="Enter your postcode"
                    required
                    value={postCode}
                    onChange={(e) => handleChange({ value: e.target.value, field: 'postCode' })}
                    onBlur={(e) => handleBlur({ value: e.target.value, field: 'postCode' })}
                    error={errorValues.postCode}
                  />
                </Grid.Col>
              </Grid>
            )}

            <Text size="xs" c="dimmed" mt={20}>
              *Mandatory
            </Text>
          </Box>

          {/* Fixed footer area with button and disclaimer */}
          <Box
            style={{
              flex: '0 0 auto',
              padding: 20,
              borderTop: '1px solid #eee',
              backgroundColor: 'white',
            }}
          >
            {submittedStatus === 'success' ? (
              <Button color="green" fullWidth mb={10} onClick={onClose}>
                Submitted Successfully! Click to Close
              </Button>
            ) : (
              <Button
                onClick={() => submitForm()}
                disabled={!isValidForm || isSubmitting || submittedStatus === 'fail'}
                loading={isSubmitting}
                fullWidth
                mb={10}
              >
                {!isSubmitting ? 'Submit Enquiry' : 'Submitting...'}
              </Button>
            )}

            <Box>
              <Text size="xs" c="dimmed" ta="center">
                By submitting an enquiry, you agree to Aussie&apos;s{' '}
                <Link
                  href="/for-agents/terms"
                  target="_blank"
                  style={{ color: 'inherit', textDecoration: 'underline' }}
                >
                  Terms and Conditions
                </Link>
                , Privacy Policy and Collection Statement.
              </Text>
            </Box>
          </Box>
        </Box>
      </form>
    </ModalWithTracking>
  );
}
