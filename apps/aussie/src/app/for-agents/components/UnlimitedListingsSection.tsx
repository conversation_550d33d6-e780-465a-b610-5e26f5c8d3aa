'use client';

import React from 'react';
import {
  BackgroundImage,
  Box,
  Button,
  ButtonProps,
  Container,
  Flex,
  Image,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { accentYellow, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

const MantineButton = ({
  children,
  onClick,
  ...props
}: { children: React.ReactNode; onClick?: React.MouseEventHandler<HTMLButtonElement> } & Omit<
  ButtonProps,
  'onClick'
>) => (
  <Button
    radius="xl"
    fw={700}
    onClick={onClick}
    styles={{
      root: {
        height: 45,
        width: 159,
        padding: '9.5px 16px',
        '@media (max-width: 768px)': {
          height: 40,
          width: 135,
          padding: '8px 14px',
        },
      },
      label: {
        fontFamily: "'Barlow', sans-serif",
        fontSize: '16px',
        lineHeight: 1.25,
        '@media (max-width: 768px)': {
          fontSize: '14px',
        },
      },
    }}
    {...props}
  >
    {children}
  </Button>
);

export const UnlimitedListingsSection = () => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <Flex
        component="section"
        pos="relative"
        py={{ base: 50, md: 80 }}
        mih={{ base: 500, md: 660 }}
        style={{
          overflow: 'hidden',
          position: 'relative',
          alignItems: 'center',
        }}
      >
        <BackgroundImage
          src="/growth-product-assets/for-agents/unlimited-listings-bg-mobile.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          display={{ base: 'block', md: 'none' }}
          style={{
            zIndex: 0,
            backgroundSize: 'auto 100%',
            backgroundRepeat: 'repeat-x',
          }}
        />
        <BackgroundImage
          src="/growth-product-assets/for-agents/unlimited-listings-background.svg"
          pos="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          display={{ base: 'none', md: 'block' }}
          style={{ zIndex: 0 }}
        />
        <Container
          size="xl"
          pos="relative"
          style={{ zIndex: 1 }}
          px={{ base: 29, sm: 40, md: 70, lg: 80 }}
        >
          <Flex
            direction={{ base: 'column', md: 'row' }}
            align="center"
            style={{ alignItems: 'space-between' }}
          >
            <Box
              w={{ base: '100%', md: '60%' }}
              mb={{ base: 30, md: 0 }}
              mt={{ base: 10, md: 0 }}
              pr={{ base: 0, md: 40 }}
              ta={{ base: 'center', md: 'center' }}
              style={{ order: 1, flex: '0 0 auto' }}
            >
              <Image
                src="/growth-product-assets/for-agents/unlimited-listings-image.png"
                alt="Illustration for unlimited listings"
                maw={{ base: '100%', md: 596 }}
                mah={{ md: 442 }}
                fit="contain"
                mx="auto"
              />
            </Box>

            <Box
              w={{ base: '100%', md: '40%' }}
              ta={{ base: 'left', md: 'left' }}
              style={{ order: 2, flex: '1 1 auto' }}
            >
              <Title
                order={2}
                fz={{ base: 36, md: 36, lg: 43 }}
                fw={700}
                mb={{ base: 'md', md: 'lg' }}
                lh={1.047}
                style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
              >
                <Text c="white" inherit>
                  Unlimited <span style={{ color: accentYellow }}>free</span>
                </Text>
                <Text c="white" inherit>
                  listings
                </Text>
              </Title>
              <Box mb={{ base: 'lg', md: 'xl' }}>
                <Stack gap="md">
                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={white} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={{ base: 16, md: 18 }}
                      lh={1.333}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={white}
                    >
                      Market your off-market, pre-market and on-market properties, including
                      auctions, on Aussie&apos;s Property Marketplace under our{' '}
                      <Text span fw={700}>
                        VPA-free access model
                      </Text>
                    </Text>
                  </Flex>

                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={white} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={{ base: 16, md: 18 }}
                      lh={1.333}
                      fw={700}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={white}
                    >
                      Seamless CRM integration
                    </Text>
                  </Flex>

                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={white} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={{ base: 16, md: 18 }}
                      lh={1.333}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={white}
                    >
                      Full search capabilities to help drive{' '}
                      <Text fz={{ base: 16, md: 18 }} span fw={700}>
                        faster sales
                      </Text>
                    </Text>
                  </Flex>
                </Stack>
              </Box>

              <Box ta="left" mt={{ base: 10, md: 0 }}>
                <MantineButton
                  bg={primaryPurple}
                  c={white}
                  onClick={open}
                  style={{ minWidth: 150, height: 45 }}
                  styles={{
                    root: {
                      border: `1px solid ${white}`,
                      '&:hover': { backgroundColor: '#5F2D85' },
                    },
                    label: { textTransform: 'initial' },
                  }}
                >
                  Contact us
                </MantineButton>
              </Box>
            </Box>
          </Flex>
        </Container>
      </Flex>
      <EnquiryFormModal opened={opened} onClose={close} />
    </>
  );
};
