import { Anchor, Box, Container, Group, Text } from '@mantine/core';

import { accentYellow, primaryPurple } from '../styles/colors';

export const AnnouncementBanner = () => {
  return (
    <Box bg={accentYellow} py={{ base: 8, md: 10 }} style={{ width: '100%' }}>
      <Container size="xl" ta="center">
        <Group justify="center" gap={5} wrap="wrap">
          <Text
            c={primaryPurple}
            fz={{ base: 14, md: 16 }}
            fw={500}
            px={30}
            lh={{ base: 1.4, md: 1.875 }}
            style={{ fontFamily: "'Barlow', sans-serif" }}
            span
          >
            Agents, be the first to experience Aussie for Agents. Register{' '}
            <Anchor
              href="https://agents.aussie.com.au/#/signup"
              target="_blank"
              c={primaryPurple}
              fz={{ base: 14, md: 16 }}
              fw={700}
              lh={{ base: 1.4, md: 1.875 }}
              style={{ fontFamily: "'Barlow', sans-serif", textDecoration: 'underline' }}
            >
              here
            </Anchor>
          </Text>
        </Group>
      </Container>
    </Box>
  );
};
