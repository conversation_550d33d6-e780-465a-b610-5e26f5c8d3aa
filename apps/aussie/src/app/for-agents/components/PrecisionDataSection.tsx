'use client';

import { <PERSON>, But<PERSON>, Container, Flex, Image, Stack, Text, Title } from '@mantine/core';

import { darkText, primaryPurple, white } from '../styles/colors';

export const PrecisionDataSection = () => {
  return (
    <Box component="section" py={{ base: 'xl', md: 80 }} pb={0} px={{ base: 29, sm: 'xl' }}>
      <Container size="xl">
        <Box p={{ base: 0, md: 40 }} py={{ base: 40, md: 40 }}>
          <Flex
            direction={{ base: 'column', lg: 'row' }}
            align="center"
            gap={{ base: 'xl', lg: '4rem' }}
          >
            <Box>
              <Image
                src="/growth-product-assets/for-agents/personalised-agent-hub-1.png"
                alt="Graph showing data insights"
                maw={653}
                mah={524}
                fit="contain"
                mx="auto"
                radius="lg"
              />
            </Box>

            <Box style={{ flex: 1 }} ta={'left'}>
              <Title
                order={2}
                fz={{ base: 30, md: 36, lg: 43 }}
                fw={700}
                c={darkText}
                mb="xl"
                lh={1.047}
                style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
              >
                Your personalised
                <br />
                Agent Hub
              </Title>

              <Box mb="xl">
                <Stack gap="md" maw={{ base: '100%', md: 500 }}>
                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={18}
                      lh={1.333}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={darkText}
                    >
                      Identify high-potential prospects with vendor lead scoring and data insights
                    </Text>
                  </Flex>

                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={18}
                      lh={1.333}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={darkText}
                    >
                      Focus your prospecting time on clients ready to convert
                    </Text>
                  </Flex>

                  <Flex align="flex-start" gap="md">
                    <Box mt={12}>
                      <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                    </Box>
                    <Text
                      fz={18}
                      lh={1.333}
                      style={{ fontFamily: "'Barlow', sans-serif" }}
                      c={darkText}
                    >
                      Uncover hidden vendor opportunities through buyer enquiries
                    </Text>
                  </Flex>
                </Stack>
              </Box>

              <Button
                bg={primaryPurple}
                c={white}
                component="a"
                href="https://agents.aussie.com.au/#/signup"
                target="_blank"
                rel="noopener noreferrer"
                radius="xl"
                fw={700}
                h={45}
                w={181}
                mt="20px"
                styles={{
                  root: {
                    padding: '9.5px 16px',
                    '&:hover': { backgroundColor: '#5F2D85' },
                  },
                  label: {
                    fontFamily: "'Barlow', sans-serif",
                    fontSize: '16px',
                    lineHeight: 1.25,
                    textTransform: 'initial',
                  },
                }}
              >
                Sign up
              </Button>
            </Box>
          </Flex>
        </Box>
      </Container>
    </Box>
  );
};
