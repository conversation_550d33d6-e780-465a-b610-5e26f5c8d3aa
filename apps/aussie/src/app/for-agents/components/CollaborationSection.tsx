'use client';

import React from 'react';
import {
  Box,
  Button,
  ButtonProps,
  Container,
  Flex,
  Image,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { darkPurpleText, darkText, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

const MantineButton = ({
  children,
  onClick,
  ...props
}: { children: React.ReactNode; onClick?: React.MouseEventHandler<HTMLButtonElement> } & Omit<
  ButtonProps,
  'onClick'
>) => (
  <Button
    radius="xl"
    onClick={onClick}
    fw={700}
    size="md"
    styles={{
      label: {
        fontFamily: "'Barlow', sans-serif",
        fontSize: '1rem',
      },
    }}
    {...props}
  >
    {children}
  </Button>
);

const NumberedTextItem = ({ number, children }: { number: number; children: React.ReactNode }) => (
  <Flex align="flex-start" gap="md">
    {' '}
    <Box
      w={40}
      h={40}
      style={{
        borderRadius: '50%',
        border: `2px solid ${primaryPurple}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
      }}
      mt={4}
    >
      <Text
        c={primaryPurple}
        fw={700}
        fz={26}
        lh={0.92}
        style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
      >
        {number}
      </Text>
    </Box>
    <Box pt={4}>{children}</Box>
  </Flex>
);

const BoldText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz="1.25rem"
    lh={1.2}
    c={primaryPurple}
    fw={700}
    span
  >
    {children}
  </Text>
);

const RegularText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz={18}
    lh={1.2}
    c={darkText}
    span
  >
    {children}
  </Text>
);

export const CollaborationSection = () => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      {' '}
      <Box component="section" pos="relative" bg={white} style={{ overflow: 'hidden' }}>
        <Box pos="relative">
          <Box
            style={{ position: 'relative' }}
            display={{ base: 'block', md: 'none' }}
            mb="xl"
            w="100%"
            px={0}
          >
            <Box w="100%" style={{ overflow: 'hidden' }} pos="relative">
              <Image
                src="/growth-product-assets/for-agents/collaboration-property.png"
                alt="Illustration of property collaboration"
                radius={0}
                fit="cover"
                w="100%"
                style={{ objectPosition: 'center' }}
              />
            </Box>
          </Box>
          <Container
            size="xl"
            px={{ base: 29, sm: 40, md: 70, lg: 80 }}
            pos="relative"
            style={{ zIndex: 2 }}
          >
            <Flex direction={{ base: 'column-reverse', lg: 'row' }} align="center">
              <Box style={{ flex: 1 }} ta={'left'} py={{ base: 'xl', md: 40 }}>
                <Title
                  order={2}
                  fz={{ base: 30, md: 36, lg: 43 }}
                  fw={700}
                  c={darkText}
                  mb="xl"
                  lh={1.1}
                  style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
                >
                  Collaboration, your way
                </Title>
                <Stack gap="xl" mb="xl">
                  <NumberedTextItem number={1}>
                    <RegularText>List your properties on our marketplace at no cost</RegularText>
                  </NumberedTextItem>
                  <NumberedTextItem number={2}>
                    <RegularText>
                      One stop platform to unlock data insights, and access potential Seller Assist
                      leads
                    </RegularText>
                  </NumberedTextItem>
                  <NumberedTextItem number={3}>
                    <RegularText>
                      Opt-in opportunities to choose us as your home loan partner through referrals
                      or custom solutions for high volume agencies
                    </RegularText>
                  </NumberedTextItem>
                </Stack>
                <MantineButton
                  variant="filled"
                  bg={white}
                  onClick={open}
                  c={primaryPurple}
                  bd={'1px solid'}
                  size="md"
                  mt={'18px'}
                  radius="xl"
                  fw={700}
                  style={{ minWidth: 193, height: 45 }}
                  styles={{
                    root: {
                      '&:hover': { backgroundColor: darkPurpleText },
                    },
                    label: {
                      fontFamily: "'Barlow', sans-serif",
                      fontSize: '1rem',
                      textTransform: 'initial',
                    },
                  }}
                >
                  Contact us
                </MantineButton>
                <Button
                  variant="filled"
                  bg={white}
                  onClick={open}
                  c={primaryPurple}
                  component="a"
                  href="https://agents.aussie.com.au/#/login"
                  target="_blank"
                  rel="noopener noreferrer"
                  bd={'1px solid'}
                  size="md"
                  mt={'18px'}
                  radius="xl"
                  fw={700}
                  ml={10}
                  style={{ minWidth: 193, height: 45 }}
                  styles={{
                    root: {
                      '&:hover': { backgroundColor: darkPurpleText },
                    },
                    label: {
                      fontFamily: "'Barlow', sans-serif",
                      fontSize: '1rem',
                      textTransform: 'initial',
                    },
                  }}
                >
                  Log in
                </Button>
              </Box>

              <Box
                style={{ flex: 1 }}
                display={{ base: 'none', lg: 'block' }}
                h={{ lg: 600 }}
                w="50%"
              />
            </Flex>
          </Container>

          <Box
            pos="absolute"
            top={0}
            right={0}
            h="100%"
            w="50%"
            style={{
              zIndex: 1,
              position: 'relative',
              overflow: 'hidden',
              backgroundImage: 'url(/growth-product-assets/for-agents/collaboration-your-way.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'left',
            }}
            display={{ base: 'none', lg: 'block' }}
          />
        </Box>
      </Box>
      <EnquiryFormModal opened={opened} onClose={close} />
    </>
  );
};
