'use client';

import React from 'react';
import { Box, Button, Container, Flex, Grid, Image, Stack, Text, Title } from '@mantine/core';

import { darkPurpleText, darkText, primaryPurple, white } from '../styles/colors';

const IconTextItem = ({ children }: { children: React.ReactNode }) => (
  <Flex align="flex-start" gap="md">
    <Box w={24} h={24} mt={4} style={{ flexShrink: 0 }}>
      <Image src="/growth-product-assets/for-agents/icon-check-yellow.svg" alt="" w={24} h={24} />
    </Box>
    <Box>{children}</Box>
  </Flex>
);

const BoldText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz={{ base: 16, md: 18 }}
    lh={1.2}
    c={darkText}
    fw={700}
    span
  >
    {children}
  </Text>
);

const RegularText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz={{ base: 16, md: 18 }}
    lh={1.2}
    c={darkText}
    span
  >
    {children}
  </Text>
);

export const WinMoreSection = () => {
  return (
    <Box component="section" py={{ base: 50, md: 100 }} bg={white}>
      <Container size="xl" px={{ base: 29, sm: 40, md: 70, lg: 80 }}>
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          align="center"
          gap={{ base: 'xl', lg: '4rem' }}
        >
          <Box style={{ flex: 1 }} ta={'left'}>
            <Title
              order={2}
              fz={{ base: 32, md: 36, lg: 43 }}
              fw={700}
              c={darkText}
              mb={{ base: 'lg', md: 'xl' }}
              lh={1.1}
              style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
            >
              Connect with the{' '}
              <Image
                display={'inline'}
                src={'/growth-product-assets/for-agents/aussie-logo.svg'}
                mah="100%"
                maw={{ base: 140, md: 163 }}
                fit="contain"
              />{' '}
              network, home of:
            </Title>
            <Grid gutter={{ base: 'md', md: 'xl' }} mb={{ base: 'lg', md: 'xl' }}>
              <Grid.Col span={{ base: 12, sm: 6 }}>
                <Stack
                  gap="md"
                  styles={{
                    root: { '@media (min-width: 768px)': { gap: 'var(--mantine-spacing-xl)' } },
                  }}
                >
                  <IconTextItem>
                    <BoldText>Australia’s largest </BoldText>
                    <RegularText>online and retail </RegularText>
                    <BoldText>mortgage broker</BoldText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>$100b</BoldText>
                    <RegularText> mortgage portfolio representing </RegularText>
                    <BoldText>$300b</BoldText>
                    <RegularText> in real estate</RegularText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>33+ years </BoldText>
                    <RegularText>of expertise</RegularText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>1,300+</BoldText>
                    <RegularText> local brokers</RegularText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>220+</BoldText>
                    <RegularText> stores nationwide</RegularText>
                  </IconTextItem>
                </Stack>
              </Grid.Col>
              <Grid.Col span={{ base: 12, sm: 6 }}>
                <Stack
                  gap="md"
                  styles={{
                    root: { '@media (min-width: 768px)': { gap: 'var(--mantine-spacing-xl)' } },
                  }}
                >
                  <IconTextItem>
                    <BoldText>10,000s monthly </BoldText>
                    <RegularText>vendor and buyer engagements</RegularText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>Household name brand </BoldText>
                    <RegularText>among the</RegularText>
                    <BoldText> most trusted in financial services</BoldText>
                  </IconTextItem>
                  <IconTextItem>
                    <BoldText>5m+ customers </BoldText>
                    <RegularText>growing by more than 800,000 a year</RegularText>
                  </IconTextItem>
                </Stack>
              </Grid.Col>
            </Grid>
            <Button
              component="a"
              href="https://agents.aussie.com.au/#/signup"
              target="_blank"
              rel="noopener noreferrer"
              variant="default"
              bg={white}
              c={primaryPurple}
              bd={'1px solid'}
              radius="xl"
              fw={700}
              miw={{ base: 135, md: 155 }}
              styles={{
                root: {
                  '&:hover': { backgroundColor: darkPurpleText },
                  height: 45,
                  '@media (max-width: 768px)': {
                    height: 40,
                    padding: '6px 14px',
                  },
                },
                label: {
                  fontFamily: "'Barlow', sans-serif",
                  fontSize: '1rem',
                  textTransform: 'initial',
                  '@media (max-width: 768px)': {
                    fontSize: '0.875rem',
                  },
                },
              }}
            >
              Sign up
            </Button>
          </Box>
          <Box style={{ flex: 1 }} ta="center" mt={{ base: 30, lg: 0 }}>
            <Image
              src="/growth-product-assets/for-agents/win-more-listings.png"
              alt="Illustration showing listings and growth"
              maw={{ base: '100%', md: 600 }}
              fit="contain"
              mx="auto"
            />
          </Box>
        </Flex>
      </Container>
    </Box>
  );
};
