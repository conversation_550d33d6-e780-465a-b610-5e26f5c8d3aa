'use client';

import React from 'react';
import {
  Box,
  Button,
  ButtonProps,
  Container,
  Flex,
  Image,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { darkText, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

const MantineButton = ({
  children,
  onClick,
  ...props
}: { children: React.ReactNode; onClick?: React.MouseEventHandler<HTMLButtonElement> } & Omit<
  ButtonProps,
  'onClick'
>) => (
  <Button
    radius="xl"
    onClick={onClick}
    fw={700}
    h={45}
    w={150}
    styles={{
      root: {
        padding: '9.5px 16px',
      },
      label: {
        fontFamily: "'Barlow', sans-serif",
        fontSize: '16px',
        lineHeight: 1.25,
      },
    }}
    {...props}
  >
    {children}
  </Button>
);

export const SellReadyVendorsSection = () => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <Box component="section" py={{ base: 'xl', md: 80 }} pt={30} px={{ base: 29, sm: 'xl' }}>
        <Container size="xl">
          <Box p={{ base: 0, md: 40 }} py={{ base: 40, md: 40 }}>
            <Flex
              direction={{ base: 'column-reverse', lg: 'row' }}
              align="center"
              gap={{ base: 'xl', lg: '4rem' }}
            >
              <Box style={{ flex: 1 }} ta={'left'}>
                <Title
                  order={2}
                  fz={{ base: 30, md: 36, lg: 43 }}
                  fw={700}
                  c={darkText}
                  mb="xl"
                  lh={1.047}
                  style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
                >
                  Sell-ready vendors, <br />
                  delivered to you
                </Title>

                <Box mb="xl">
                  <Stack gap="md">
                    <Flex align="flex-start" gap="md">
                      <Box mt={12}>
                        <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                      </Box>
                      <Text
                        fz={18}
                        lh={1.222}
                        style={{ fontFamily: "'Barlow', sans-serif" }}
                        c={darkText}
                      >
                        Receive vendor leads from upgraders and downsizers
                      </Text>
                    </Flex>

                    <Flex align="flex-start" gap="md">
                      <Box mt={12}>
                        <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                      </Box>
                      <Text
                        fz={18}
                        lh={1.222}
                        style={{ fontFamily: "'Barlow', sans-serif" }}
                        c={darkText}
                      >
                        Pre-qualified for accuracy to save you time
                      </Text>
                    </Flex>

                    <Flex align="flex-start" gap="md">
                      <Box mt={12}>
                        <Box w={5} h={5} bg={darkText} style={{ borderRadius: '50%' }} />
                      </Box>
                      <Text
                        fz={18}
                        lh={1.222}
                        style={{ fontFamily: "'Barlow', sans-serif" }}
                        c={darkText}
                      >
                        Transparent fee payable after you’ve secured your commission
                      </Text>
                    </Flex>
                  </Stack>
                </Box>

                <MantineButton
                  bg={primaryPurple}
                  c={white}
                  onClick={open}
                  styles={{
                    root: { '&:hover': { backgroundColor: '#5F2D85' }, marginTop: '20px' },
                    label: {
                      textTransform: 'initial',
                    },
                  }}
                >
                  Learn more
                </MantineButton>
              </Box>

              <Box>
                <Image
                  src="/growth-product-assets/for-agents/personalised-agent-hub-2.png"
                  alt="Illustration showing vendor leads"
                  maw={653}
                  mah={512}
                  fit="contain"
                  mx="auto"
                  radius="lg"
                />
              </Box>
            </Flex>
          </Box>
        </Container>
      </Box>
      <EnquiryFormModal opened={opened} onClose={close} />
    </>
  );
};
