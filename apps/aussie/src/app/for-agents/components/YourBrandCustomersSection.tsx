'use client';

import React from 'react';
import { Box, Container, Flex, Image, Stack, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';

import { darkText, lightGrayBg } from '../styles/colors';

const IconTextItem = ({ iconSrc, children }: { iconSrc: string; children: React.ReactNode }) => (
  <Flex align="flex-start" gap="md">
    <Box w={90} h={90} mt={4}>
      <Image src={iconSrc} alt="" w={90} h={90} /> {}
    </Box>
    <Box pt={4}>{children}</Box>
  </Flex>
);

const RegularText = ({ children }: { children: React.ReactNode }) => (
  <Text
    component="span"
    style={{ fontFamily: "'Barlow', sans-serif" }}
    fz={{ base: '0.875rem', md: '1.125rem' }}
    lh={1.2}
    c={darkText}
    span
  >
    {children}
  </Text>
);

export const YourBrandCustomersSection = () => {
  const isMobile = useMediaQuery('(max-width: 767px)');

  return (
    <Box component="section" bg={lightGrayBg} style={{ overflow: 'hidden' }}>
      <Box pos="relative">
        {isMobile && (
          <Box
            style={{
              position: 'relative',
              width: '100vw',
              marginLeft: 'calc(-50vw + 50%)',
              marginRight: 'calc(-50vw + 50%)',
            }}
            ta="center"
            mb="xl"
          >
            <Image
              src="/growth-product-assets/for-agents/your-brand-5-million.png"
              alt="Diverse group of people representing customers"
              fit="cover"
              h="100%"
              w="100%"
              style={{ objectPosition: 'center' }}
            />
          </Box>
        )}
        <Container
          size="xl"
          px={{ base: 29, sm: 40, md: 70, lg: 80 }}
          pos="relative"
          style={{ zIndex: 2 }}
        >
          {isMobile ? (
            <Flex direction="column" align="flex-start">
              <Box ta="left" py={{ base: 'xl' }}>
                <Title
                  order={2}
                  fz={32}
                  fw={700}
                  c={darkText}
                  mb="xl"
                  lh={1.1}
                  style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
                >
                  The Aussie difference
                </Title>
                <Stack gap="xl">
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-handshake.svg">
                    <RegularText>
                      Aussie’s market advantage: Established customer base on both buy and sell
                      sides, built-in audience reach and decades of earned brand trust — what others
                      must build from scratch
                    </RegularText>
                  </IconTextItem>
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-vendors-buyers.svg">
                    <RegularText>
                      Our nationwide presence includes 1,300 local brokers and 220 stores located
                      across the country
                    </RegularText>
                  </IconTextItem>
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-broker.svg">
                    <RegularText>
                      We’re not just another portal offering a new solution. We have the proven
                      expertise and resources to genuinely help grow your business
                    </RegularText>
                  </IconTextItem>
                </Stack>
              </Box>
            </Flex>
          ) : (
            <Flex direction="row" align="center">
              <Box style={{ flex: 1 }} display="block" h={{ lg: 600 }} w="50%" />
              <Box style={{ flex: 1 }} ta="left" py={{ base: 'xl', md: 40 }}>
                <Title
                  order={2}
                  fz={{ md: 36, lg: 43 }}
                  fw={700}
                  c={darkText}
                  mb="xl"
                  lh={1.1}
                  style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
                >
                  The Aussie difference
                </Title>
                <Stack gap="xl">
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-handshake.svg">
                    <RegularText>
                      Aussie’s market advantage: Established customer base on both buy and sell
                      sides, built-in audience reach and decades of earned brand trust — what others
                      must build from scratch
                    </RegularText>
                  </IconTextItem>
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-vendors-buyers.svg">
                    <RegularText>
                      Our nationwide presence includes 1,300 local brokers and 220 stores located
                      across the country
                    </RegularText>
                  </IconTextItem>
                  <IconTextItem iconSrc="/growth-product-assets/for-agents/icon-broker.svg">
                    <RegularText>
                      We’re not just another portal offering a new solution. We have the proven
                      expertise and resources to genuinely help grow your business
                    </RegularText>
                  </IconTextItem>
                </Stack>
              </Box>
            </Flex>
          )}{' '}
        </Container>
        <Box
          pos="absolute"
          top={0}
          left={0}
          mih="100%"
          w="50%"
          style={{
            zIndex: 1,
            position: 'relative',
            overflow: 'hidden',
            backgroundImage: 'url(/growth-product-assets/for-agents/your-brand-customers.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'right',
          }}
          display={{ base: 'none', sm: 'block' }}
        />
      </Box>
    </Box>
  );
};
