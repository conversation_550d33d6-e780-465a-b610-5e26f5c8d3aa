'use client';

import { useEffect, useState } from 'react';
import { Box, Button, Center, Container, Image, SimpleGrid, Title } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { darkPurpleText, darkText, primaryPurple, white } from '../styles/colors';
import { EnquiryFormModal } from './EnquiryFormModal';

export const AgencyLogos = () => {
  const [hasMounted, setHasMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [opened, { open, close }] = useDisclosure(false);

  useEffect(() => {
    setHasMounted(true);

    if (typeof window !== 'undefined') {
      setIsMobile(window.innerWidth < 768);
    }

    const handleResize = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < 768);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);

      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  const logos = [
    { src: '/growth-product-assets/for-agents/logo-biggin-scott.svg', alt: 'Biggin Scott' },
    { src: '/growth-product-assets/for-agents/logo-harcourts.svg', alt: 'Harcourts' },
    { src: '/growth-product-assets/for-agents/logo-first-national.png', alt: 'First National' },
    { src: '/growth-product-assets/for-agents/logo-belle.png', alt: 'Belle Property' },
    { src: '/growth-product-assets/for-agents/logo-stockdale.svg', alt: 'Stockdale & Leggo' },
    { src: '/growth-product-assets/for-agents/logo-mcgrath.svg', alt: 'McGrath' },

    { src: '/growth-product-assets/for-agents/logo-raine-horne.svg', alt: 'Raine & Horne' },
    { src: '/growth-product-assets/for-agents/logo-century21.svg', alt: 'Century 21' },
    { src: '/growth-product-assets/for-agents/logo-lj-hooker.svg', alt: 'LJ Hooker' },
    { src: '/growth-product-assets/for-agents/logo-obrien.png', alt: "O'Brien" },
    { src: '/growth-product-assets/for-agents/logo-professionals.png', alt: 'Professionals' },
    { src: '/growth-product-assets/for-agents/logo-at-realty.png', alt: '@realty' },

    { src: '/growth-product-assets/for-agents/logo-elders.png', alt: 'Elders' },
    { src: '/growth-product-assets/for-agents/logo-barry-plant.svg', alt: 'Barry Plant' },
    { src: '/growth-product-assets/for-agents/logo-one-agency.svg', alt: 'One Agency' },
    { src: '/growth-product-assets/for-agents/logo-urban.png', alt: 'Urban' },
    { src: '/growth-product-assets/for-agents/logo-area-specialists.png', alt: 'Area Specialists' },
    { src: '/growth-product-assets/for-agents/logo-kay-burton.png', alt: 'Kay & Burton' },

    { src: '/growth-product-assets/for-agents/logo-the-agency.png', alt: 'The Agency' },
    { src: '/growth-product-assets/for-agents/logo-prd.svg', alt: 'PRD' },
    { src: '/growth-product-assets/for-agents/logo-eview.png', alt: 'Eview Group' },
    { src: '/growth-product-assets/for-agents/logo-exp.png', alt: 'eXp Realty' },
    { src: '/growth-product-assets/for-agents/logo-oxbridge.png', alt: 'Oxbridge' },
    { src: '/growth-product-assets/for-agents/logo-remax.svg', alt: 'RE/MAX' },
  ];

  return (
    <>
      <Box component="section" pb={{ base: 'xl', md: 110 }}>
        <Container size="xl" ta="center" maw={1160}>
          <Title
            order={2}
            fz={{ base: 30, md: 36, lg: 43 }}
            fw={700}
            c={darkText}
            mb={{ base: 'xl', md: '3rem' }}
            lh={1.05}
            style={{ fontFamily: "'TT Commons Pro', sans-serif" }}
          >
            Top agencies are in.
            <br />
            Are you?
          </Title>

          <style
            dangerouslySetInnerHTML={{
              __html: `
              .grecaptcha-badge { visibility: hidden; }
            @media (max-width: 767px) {
              .logo-scroll-container::-webkit-scrollbar {
                height: 8px;
              }
              .logo-scroll-container::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 10px;
              }
              .logo-scroll-container::-webkit-scrollbar-thumb {
                background: ${primaryPurple};
                border-radius: 10px;
              }
              .logo-scroll-container::-webkit-scrollbar-thumb:hover {
                background: ${darkPurpleText};
              }
              .logo-scroll-container {
                scrollbar-width: thin;
                scrollbar-color: ${primaryPurple} #f1f1f1;
              }
            }
          `,
            }}
          />

          {hasMounted ? (
            isMobile ? (
              <Box
                className="logo-scroll-container"
                style={{
                  overflowX: 'auto',
                  marginBottom: '2rem',
                }}
              >
                <Box
                  style={{
                    display: 'grid',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    gridAutoFlow: 'column',
                    gridAutoColumns: '120px',
                    gap: '1rem',
                    padding: '1rem 0',
                    paddingLeft: '29px',
                    paddingRight: '29px',
                    width: 'max-content',
                  }}
                >
                  {logos.map((logo, index) => (
                    <Center key={`mobile-${index}`} h={48}>
                      <Image src={logo.src} alt={logo.alt} mah={45} maw={120} fit="contain" />
                    </Center>
                  ))}
                </Box>
              </Box>
            ) : (
              <SimpleGrid
                cols={{ sm: 4, md: 6 }}
                spacing={{ base: 'lg', md: 'xl' }}
                verticalSpacing={50}
                mb={{ base: 'xl', md: 50 }}
                mx="auto"
              >
                {logos.map((logo, index) => (
                  <Center key={`desktop-${index}`} h={48}>
                    <Image src={logo.src} alt={logo.alt} mah="100%" maw={120} fit="contain" />
                  </Center>
                ))}
              </SimpleGrid>
            )
          ) : (
            <>
              <Box
                className="logo-scroll-container"
                style={{
                  overflowX: 'auto',
                  marginBottom: '2rem',
                  display: 'none',
                  '@media (max-width: 767px)': {
                    display: 'block',
                  },
                }}
              >
                <Box
                  style={{
                    display: 'grid',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    gridAutoFlow: 'column',
                    gridAutoColumns: '120px',
                    gap: '1rem',
                    padding: '1rem 0',
                    paddingLeft: '29px',
                    paddingRight: '29px',
                    width: 'max-content',
                  }}
                >
                  {logos.map((logo, index) => (
                    <Center key={`mobile-ssr-${index}`} h={48}>
                      <Image src={logo.src} alt={logo.alt} mah={40} maw={120} fit="contain" />
                    </Center>
                  ))}
                </Box>
              </Box>

              <SimpleGrid
                cols={{ sm: 4, md: 6 }}
                spacing={{ base: 'lg', md: 'xl' }}
                verticalSpacing={50}
                mb={{ base: 'xl', md: 50 }}
                mx="auto"
                style={{
                  '@media (max-width: 767px)': {
                    display: 'none',
                  },
                }}
              >
                {logos.map((logo, index) => (
                  <Center key={`desktop-ssr-${index}`} h={48}>
                    <Image src={logo.src} alt={logo.alt} mah="100%" maw={120} fit="contain" />
                  </Center>
                ))}
              </SimpleGrid>
            </>
          )}

          <Button
            variant="filled"
            bg={white}
            c={primaryPurple}
            bd={'1px solid'}
            size="md"
            mt={'18px'}
            radius="xl"
            fw={700}
            onClick={open}
            style={{ minWidth: 193, height: 45 }}
            styles={{
              root: {
                '&:hover': { backgroundColor: darkPurpleText },
              },
              label: {
                fontFamily: "'Barlow', sans-serif",
                fontSize: '1rem',
                textTransform: 'initial',
              },
            }}
          >
            Add your listings
          </Button>
        </Container>
      </Box>
      <EnquiryFormModal opened={opened} onClose={close} />
    </>
  );
};
