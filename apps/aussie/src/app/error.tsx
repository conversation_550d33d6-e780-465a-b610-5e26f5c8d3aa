'use client';

import { useEffect } from 'react';
import { Stack } from '@mantine/core';

import { SimpleNavbar } from '@gp/feature/aussie-navbar';
import { ErrorProvider } from '@gp/shared/app-providers';

export default function Error({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    // update to Datadog error logging
    console.error(error);
  }, [error]);

  return (
    <html>
      <body>
        <Stack gap={0} mih="100vh">
          <SimpleNavbar />
          <ErrorProvider errorCode={500} />
        </Stack>
      </body>
    </html>
  );
}
