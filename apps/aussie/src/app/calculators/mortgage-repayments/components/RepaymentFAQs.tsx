'use client';

import React from 'react';
import { Box, Container, Stack, Text, Title } from '@mantine/core';

import classes from './RepaymentFAQs.module.css';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: 'HOW DO I CALCULATE MORTGAGE REPAYMENTS?',
    answer:
      'Mortgage repayments are calculated by taking your loan amount, interest rate, loan term, repayment frequency and repayment type into account.\n\nUse our Home Loan Repayments Calculator to get an estimate of your monthly, fortnightly or weekly repayments.',
  },
  {
    question: 'HOW MUCH WILL MY MORTGAGE REPAYMENTS BE?',
    answer:
      'Your mortgage repayments will depend on several factors, including your loan amount, interest rate, loan term, repayment frequency, repayment type and loan type.\n\nIf you have a fixed rate home loan, your repayments will remain the same for the duration of your fixed period. If you have a variable rate home loan, your repayments could change.\n\nUse our Mortgage Repayments Calculator to get an estimate of your monthly, fortnightly or weekly repayments.',
  },
  {
    question: 'HOW MUCH IS THE AVERAGE MORTGAGE REPAYMENT IN AUSTRALIA?',
    answer:
      'Nationally, here are the average monthly repayments for a new home loan:\n\n• Existing home – $3,290\n• Newly built home – $3,098\n• New construction – $3,163\n\nThese calculations were based on a P&I home loan with an interest rate of 5.18% and a 30-year loan term. These estimates were based on the average variable rate for October.*\n\n*Canstar, What is the average new mortgage in Australia?, December 2022',
  },
  {
    question: 'HOW IS INTEREST CALCULATED ON A MORTGAGE?',
    answer:
      'Your loan balance is multiplied by your interest rate each day and divided by 365 days to get your daily interest charge.\n\nAt the end of the month, each of the daily interest charges for the month are added together to get your monthly interest rate amount.\n\nIf your repayments are fortnightly or weekly, then this amount is charged accordingly.',
  },
  {
    question: 'HOW DO INTEREST RATES AFFECT MORTGAGE REPAYMENTS?',
    answer:
      'Interest rates are the additional charge on top of your loan amount and are charged by banks to cover the cost of the loan.\n\nIf your interest rate increases, so will your mortgage repayments.',
  },
  {
    question: 'CAN I CHANGE MY REPAYMENTS AFTER I TAKE OUT A HOME LOAN?',
    answer:
      "Yes, you will be able to change your repayments after you take out your home loan. You will need to speak to your lender directly or through a mortgage broker to discuss these changes.\n\nIn some instances, you will have to refinance to change the terms of your home loan. For example, if you'd like to switch from a variable rate to a fixed rate, you will need to refinance.\n\nSome changes, like switching from monthly to fortnightly repayments, won't require a refinance and will just require you to speak with your lender to make the arrangements.",
  },
  {
    question: 'CAN I REPAY MY HOME LOAN SOONER?',
    answer:
      "Yes, if you're on a variable rate home loan you'll typically be allowed to make unlimited extra repayments to pay down your principal amount and pay off your home loan sooner.\n\nIf you're on a fixed rate home loan, you'll likely have a limit on the number of extra repayments you can make, if at all.",
  },
  {
    question: 'WHAT ARE PRINCIPAL AND INTEREST REPAYMENTS?',
    answer:
      'Principal and interest (P&I) repayments mean you are paying off your borrowed loan amount in addition to the interest accrued and charged by your lender.',
  },
  {
    question: 'WHAT ARE INTEREST-ONLY REPAYMENTS?',
    answer:
      'Interest-only repayments mean that you are only paying off the interest accrued on your home loan, but not the actual borrowed amount (also known as principal).\n\nInterest-only repayments have and end date as you will eventually need to pay down your principal, unless you sell your property beforehand.',
  },
  {
    question: 'CAN USING AN OFFSET ACCOUNT LOWER MY REPAYMENTS?',
    answer:
      "Yes, you can use an offset account to lower your repayments. An offset account is a type of savings account linked to your home loan.\n\nThe money placed in this account offsets your home loan balance, reducing the interest you are charged each month.\n\nFor example, if you have an outstanding loan balance of $400,000, but place $100,000 in your offset, you'll only be charged interest on $300,000. So, the more money in your offset account, the less interest you pay.\n\nKeep in mind that you can only have an offset account of you have a variable rate home loan, or the variable portion of a split rate home loan.",
  },
];

export function RepaymentFAQs() {
  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="xl">
        <Title order={2} size="h1" ta="center" c="gray.9" className={classes.faqTitle}>
          Repayment Calculator FAQs
        </Title>

        <Box className={classes.faqContainer}>
          <Stack gap="xs">
            {faqData.map((faq, index) => (
              <details key={index} className={classes.faqItem}>
                <summary className={classes.faqSummary}>
                  <span className={classes.faqChevron}>▶</span>
                  <span className={classes.faqQuestion}>{faq.question}</span>
                </summary>
                <div className={classes.faqContent}>
                  <Text size="md" c="gray.7">
                    {faq.answer.split('\n\n').map((paragraph, index) => (
                      <React.Fragment key={index}>
                        {paragraph.split('\n').map((line, lineIndex) => (
                          <React.Fragment key={lineIndex}>
                            {line}
                            {lineIndex < paragraph.split('\n').length - 1 && <br />}
                          </React.Fragment>
                        ))}
                        {index < faq.answer.split('\n\n').length - 1 && (
                          <>
                            <br />
                            <br />
                          </>
                        )}
                      </React.Fragment>
                    ))}
                  </Text>
                </div>
              </details>
            ))}
          </Stack>
        </Box>
      </Stack>
    </Container>
  );
}
