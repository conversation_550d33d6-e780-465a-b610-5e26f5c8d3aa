.disclaimerText {
  font-family: <PERSON>, sans-serif;
}

.dottedDivider {
  border-top: 2px dotted #c7c7c5;
}

.gridDivider {
  display: flex;
  align-items: stretch;
  justify-content: center;
  padding: 0 0;
}

.verticalDivider {
  border-left: 2px dotted #c7c7c5;
  min-height: 400px;
  align-self: stretch;
}

.stickyContainer {
  z-index: 10;
}

.resultCard {
  border: 2px solid var(--mantine-color-grape-8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
}

.brokerCard {
  border: 2px solid var(--mantine-color-grape-8);
  border-top: none;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
