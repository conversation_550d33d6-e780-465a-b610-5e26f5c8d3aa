'use client';

import React, { useEffect, useState } from 'react';
import {
  Alert,
  Box,
  Grid,
  NumberInput,
  Paper,
  Radio,
  Select,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useForm } from '@mantine/form';

import { ButtonWithTracking, FormWithTracking } from '@gp/ui/components';
import { calculateRepaymentsAndExtraRepayments } from '@gp/util/calculators';
import { sendEvent } from '@gp/util/interaction-studio';

import { formatCurrency } from '../../shared/utils';
import {
  DEFAULT_VALUES,
  FORM_LABELS,
  INTEREST_ONLY_PERIOD_OPTIONS,
  LOAN_TYPE_OPTIONS,
  REPAYMENT_FREQUENCY_OPTIONS,
  RESULT_TEXT,
  VALIDATION_MESSAGES,
} from '../constants';
import { RepaymentDisplay } from './RepaymentDisplay';

import classes from './RepaymentCalculator.module.css';

interface FormValues {
  loanAmount: number;
  loanType: string;
  loanTerm: number;
  interestRate: number;
  repaymentFrequency: string;
  extraMonthlyRepayment: number;
  interestOnlyYears?: number;
  extraRepaymentsStartYear?: number;
}

interface RepaymentResult {
  monthlyRepayment: number;
  totalRepayments: number;
  totalInterest: number;
  interestOnlyPeriodRepayment?: number;
  remainderPeriodRepayment?: number;
  yearsSaved?: number;
  interestSaved?: number;
  error?: string;
}

export function RepaymentCalculator() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<RepaymentResult | null>(null);

  const form = useForm<FormValues>({
    initialValues: {
      loanAmount: DEFAULT_VALUES.LOAN_AMOUNT,
      loanType: 'principal_interest',
      loanTerm: DEFAULT_VALUES.LOAN_TERM,
      interestRate: DEFAULT_VALUES.INTEREST_RATE,
      repaymentFrequency: 'monthly',
      extraMonthlyRepayment: DEFAULT_VALUES.EXTRA_MONTHLY_REPAYMENT,
      interestOnlyYears: 4,
      extraRepaymentsStartYear: undefined,
    },
    validate: {
      loanAmount: (value) => {
        if (value < 10000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        if (value > 100000000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        return value > 0 ? null : VALIDATION_MESSAGES.LOAN_AMOUNT_REQUIRED;
      },
      interestRate: (value) => (value > 0 ? null : VALIDATION_MESSAGES.INTEREST_RATE_REQUIRED),
      loanTerm: (value) => (value > 0 ? null : VALIDATION_MESSAGES.LOAN_TERM_REQUIRED),
      interestOnlyYears: (value, values) =>
        values.loanType === 'interest_only'
          ? value && value >= 1 && value <= 5
            ? null
            : VALIDATION_MESSAGES.INTEREST_ONLY_YEARS_REQUIRED
          : null,
      extraRepaymentsStartYear: (value, values) =>
        value && (value < 1 || value > values.loanTerm)
          ? VALIDATION_MESSAGES.EXTRA_REPAYMENTS_START_YEAR_REQUIRED
          : null,
    },
  });

  const calculateRepayments = async (values: FormValues) => {
    // Don't calculate if form has validation errors
    if (form.errors.loanAmount || form.errors.interestRate || form.errors.loanTerm) {
      return;
    }

    setLoading(true);

    try {
      // Convert repayment frequency to number
      const frequencyMap: { [key: string]: number } = {
        monthly: 12,
        fortnightly: 26,
        weekly: 52,
      };
      const frequency = frequencyMap[values.repaymentFrequency] || 12;

      // Calculate repayments using the utility function directly
      const results = calculateRepaymentsAndExtraRepayments(
        values.loanAmount,
        values.interestRate,
        values.extraMonthlyRepayment,
        values.loanTerm,
        frequency,
        values.extraRepaymentsStartYear || 0,
        values.loanType === 'interest_only' ? values.interestOnlyYears || 4 : 0
      );

      // Process the results based on loan type
      if (values.loanType === 'interest_only') {
        setResult({
          monthlyRepayment: results.interestOnlyPeriodRecurringAmount,
          totalRepayments: results.totalRepayments,
          totalInterest: results.totalInterest,
          interestOnlyPeriodRepayment: results.interestOnlyPeriodRecurringAmount,
          remainderPeriodRepayment: results.totalRecurringRepayment,
          yearsSaved: results.yearsSaved,
          interestSaved: results.interestSaved,
        });
      } else {
        setResult({
          monthlyRepayment: results.totalRecurringRepayment,
          totalRepayments: results.totalRepayments,
          totalInterest: results.totalInterest,
          yearsSaved: results.yearsSaved,
          interestSaved: results.interestSaved,
        });
      }

      // Track recalculation event
      sendEvent('Repayment Calculated', {
        calculator_type: 'repayment',
        loan_amount: values.loanAmount,
        interest_rate: values.interestRate,
        loan_term_years: values.loanTerm,
        repayment_frequency: values.repaymentFrequency,
        extra_monthly_repayment: values.extraMonthlyRepayment,
      });
    } catch (error) {
      console.error('Error calculating repayments:', error);
      setResult({
        monthlyRepayment: 0,
        totalRepayments: 0,
        totalInterest: 0,
        error: 'Unable to calculate repayments. Please try again later.',
      });
    } finally {
      setLoading(false);
    }
  };

  // Auto-calculate when form values change
  useEffect(() => {
    const values = form.values;
    if (values.loanAmount > 0 && values.interestRate > 0 && values.loanTerm > 0) {
      calculateRepayments(values);
    }
  }, [form.values]);

  return (
    <Stack gap="xl">
      <style>
        {`
          @keyframes pulse {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.4;
            }
          }
        `}
      </style>

      <Box ta="center" py="xl">
        <Title order={1} mb="md" c="grape.8">
          Mortgage Repayment Calculator
        </Title>
        <Text size="lg" c="gray.6">
          Calculate your estimated mortgage repayments and see how much you could save with extra
          repayments
        </Text>
      </Box>

      <FormWithTracking
        name="Repayment Calculator"
        form={form}
        handleSubmit={() => {
          // Form submission is handled by auto-calculation
        }}
        shouldWaitForTracking
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 5 }}>
            <Stack gap="lg">
              {/* Loan Details */}
              <Stack gap="xl">
                <Title order={3} c="grape.8">
                  {FORM_LABELS.LOAN_DETAILS}
                </Title>

                {/* Loan Amount */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.LOAN_AMOUNT}
                  </Text>
                  <NumberInput
                    placeholder="Enter loan amount"
                    value={form.values.loanAmount}
                    thousandSeparator=","
                    hideControls
                    onChange={(value: number | string) => {
                      if (value === '') {
                        form.setFieldValue('loanAmount', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('loanAmount', value);
                      }
                      // Trigger validation immediately
                      form.validateField('loanAmount');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.loanAmount) {
                        formatCurrency(form.values.loanAmount);
                        // The NumberInput will automatically format with thousandSeparator and prefix
                      }
                      sendEvent('Calculator Field Updated', {
                        calculator_type: 'repayment',
                        field: 'loan_amount',
                        value: form.values.loanAmount ?? null,
                      });
                    }}
                    error={form.errors.loanAmount}
                    step={1000}
                    allowNegative={false}
                    size="lg"
                    radius="md"
                  />
                </Stack>

                {/* Loan Type */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.LOAN_TYPE}
                  </Text>
                  <Radio.Group
                    value={form.values.loanType}
                    onChange={(value) => form.setFieldValue('loanType', value)}
                    size="lg"
                  >
                    <Stack gap="xs">
                      {LOAN_TYPE_OPTIONS.map((option) => (
                        <Radio
                          key={option.value}
                          value={option.value}
                          label={option.label}
                          onChange={() =>
                            sendEvent('Calculator Field Updated', {
                              calculator_type: 'repayment',
                              field: 'loan_type',
                              value: option.value,
                            })
                          }
                        />
                      ))}
                    </Stack>
                  </Radio.Group>
                </Stack>

                {/* Interest Only Period - Only show when loan type is interest only */}
                {form.values.loanType === 'interest_only' && (
                  <Stack gap="md">
                    <Text fw={600} size="md" c="gray.8">
                      Interest only period in years
                    </Text>
                    <Select
                      data={INTEREST_ONLY_PERIOD_OPTIONS}
                      value={form.values.interestOnlyYears?.toString() || '4'}
                      onChange={(value) => {
                        const next = value ? parseInt(value, 10) : 4;
                        form.setFieldValue('interestOnlyYears', next);
                      }}
                      onBlur={() =>
                        sendEvent('Calculator Field Updated', {
                          calculator_type: 'repayment',
                          field: 'interest_only_years',
                          value: form.values.interestOnlyYears || 0,
                        })
                      }
                      size="lg"
                      radius="md"
                      placeholder="Select interest only period"
                    />
                  </Stack>
                )}

                {/* Loan Term */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.LOAN_TERM}
                  </Text>
                  <NumberInput
                    placeholder="Enter loan term in years"
                    value={form.values.loanTerm}
                    onChange={(value: number | string) => {
                      if (value === '') {
                        form.setFieldValue('loanTerm', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('loanTerm', value);
                      }
                      // Trigger validation immediately
                      form.validateField('loanTerm');
                    }}
                    onBlur={() =>
                      sendEvent('Calculator Field Updated', {
                        calculator_type: 'repayment',
                        field: 'loan_term',
                        value: form.values.loanTerm ?? null,
                      })
                    }
                    error={form.errors.loanTerm}
                    min={1}
                    max={50}
                    step={1}
                    allowNegative={false}
                    size="lg"
                    radius="md"
                  />
                </Stack>

                {/* Interest Rate */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.INTEREST_RATE}
                  </Text>
                  <NumberInput
                    placeholder="Enter interest rate"
                    value={form.values.interestRate}
                    onChange={(value: number | string) => {
                      if (value === '') {
                        form.setFieldValue('interestRate', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('interestRate', value);
                      }
                      // Trigger validation immediately
                      form.validateField('interestRate');
                    }}
                    onBlur={() =>
                      sendEvent('Calculator Field Updated', {
                        calculator_type: 'repayment',
                        field: 'interest_rate',
                        value: form.values.interestRate ?? null,
                      })
                    }
                    error={form.errors.interestRate}
                    min={0}
                    max={20}
                    step={0.1}
                    decimalScale={2}
                    allowNegative={false}
                    size="lg"
                    radius="md"
                  />
                </Stack>

                {/* Repayment Frequency */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.REPAYMENT_FREQUENCY}
                  </Text>
                  <Select
                    data={REPAYMENT_FREQUENCY_OPTIONS}
                    value={form.values.repaymentFrequency}
                    onChange={(value) =>
                      form.setFieldValue('repaymentFrequency', value || 'monthly')
                    }
                    onBlur={() =>
                      sendEvent('Calculator Field Updated', {
                        calculator_type: 'repayment',
                        field: 'repayment_frequency',
                        value: form.values.repaymentFrequency,
                      })
                    }
                    size="lg"
                    radius="md"
                    placeholder="Select repayment frequency"
                  />
                </Stack>

                {/* Extra Repayments */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    {FORM_LABELS.EXTRA_REPAYMENTS}
                  </Text>
                  <Text size="sm" c="gray.7" className={classes.disclaimerText}>
                    {FORM_LABELS.EXTRA_MONTHLY_REPAYMENT}
                  </Text>
                  <NumberInput
                    placeholder="Enter extra amount"
                    value={form.values.extraMonthlyRepayment}
                    thousandSeparator=","
                    hideControls
                    onChange={(value: number | string) => {
                      if (value === '') {
                        form.setFieldValue('extraMonthlyRepayment', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('extraMonthlyRepayment', value);
                      }
                      // Trigger validation immediately
                      form.validateField('extraMonthlyRepayment');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.extraMonthlyRepayment) {
                        formatCurrency(form.values.extraMonthlyRepayment);
                        // The NumberInput will automatically format with thousandSeparator and prefix
                      }
                      sendEvent('Calculator Field Updated', {
                        calculator_type: 'repayment',
                        field: 'extra_monthly_repayment',
                        value: form.values.extraMonthlyRepayment ?? null,
                      });
                    }}
                    min={0}
                    step={100}
                    allowNegative={false}
                    size="lg"
                    radius="md"
                  />
                </Stack>

                {/* Extra Repayments From Year - Only show when extra repayments > 0 */}
                {form.values.extraMonthlyRepayment > 0 && (
                  <Stack gap="md">
                    <Text fw={600} size="md" c="gray.8">
                      Extra repayments from year (optional)
                    </Text>
                    <NumberInput
                      placeholder="Enter start year"
                      value={form.values.extraRepaymentsStartYear || undefined}
                      onChange={(value: number | string) => {
                        const next = typeof value === 'number' ? value : undefined;
                        form.setFieldValue('extraRepaymentsStartYear', next);
                      }}
                      onBlur={() =>
                        sendEvent('Calculator Field Updated', {
                          calculator_type: 'repayment',
                          field: 'extra_repayments_start_year',
                          value: form.values.extraRepaymentsStartYear || 0,
                        })
                      }
                      error={form.errors.extraRepaymentsStartYear}
                      min={1}
                      max={form.values.loanTerm}
                      step={1}
                      size="lg"
                      radius="md"
                    />
                  </Stack>
                )}
              </Stack>
            </Stack>
          </Grid.Col>

          {/* Divider - vertical on desktop, horizontal on mobile */}
          <Grid.Col span={1} visibleFrom="md" className={classes.gridDivider}>
            <Box w="1px" className={classes.verticalDivider} />
          </Grid.Col>

          {/* Mobile divider */}
          <Grid.Col span={12} hiddenFrom="md">
            <Box w="100%" h="1px" my="xl" className={classes.dottedDivider} />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 5 }}>
            <Box pos="sticky" top="20px" className={classes.stickyContainer}>
              {result?.error && (
                <Alert color="red" mb="lg">
                  {result.error}
                </Alert>
              )}

              <Stack gap={0}>
                {/* White Results Section */}
                <Paper p="lg" bg="white" className={classes.resultCard}>
                  <RepaymentDisplay
                    monthlyRepayment={result?.monthlyRepayment || 0}
                    totalRepayments={result?.totalRepayments || 0}
                    totalInterest={result?.totalInterest || 0}
                    interestOnlyPeriodRepayment={result?.interestOnlyPeriodRepayment}
                    remainderPeriodRepayment={result?.remainderPeriodRepayment}
                    yearsSaved={result?.yearsSaved}
                    interestSaved={result?.interestSaved}
                    loading={loading}
                    isInterestOnly={form.values.loanType === 'interest_only'}
                    interestOnlyYears={form.values.interestOnlyYears}
                  />
                </Paper>

                {/* Purple Talk to Broker Section */}
                <Paper p="xl" bg="grape.8" className={classes.brokerCard}>
                  <Stack gap="md" align="center">
                    <Text size="xl" fw={600} c="white" ta="center">
                      {RESULT_TEXT.TALK_TO_BROKER}
                    </Text>

                    <Text size="md" c="white" ta="center">
                      {RESULT_TEXT.BROKER_DESCRIPTION}
                    </Text>

                    <ButtonWithTracking
                      size="lg"
                      bg="yellow.5"
                      c="grape.8"
                      fw={600}
                      mt="sm"
                      radius="xl"
                      w="280px"
                      label={RESULT_TEXT.BOOK_APPOINTMENT}
                      position="Repayment Calculator"
                      purpose="book_appointment"
                      onClick={() => {
                        sendEvent('CTA Clicked', {
                          cta_text: RESULT_TEXT.BOOK_APPOINTMENT,
                          cta_position: 'Repayment Calculator',
                        });
                        window.location.href = '/book-appointment/';
                      }}
                    >
                      {RESULT_TEXT.BOOK_APPOINTMENT}
                    </ButtonWithTracking>
                  </Stack>
                </Paper>
              </Stack>
            </Box>
          </Grid.Col>
        </Grid>
      </FormWithTracking>
    </Stack>
  );
}
