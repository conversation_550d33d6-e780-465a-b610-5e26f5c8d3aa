.loadingContainer {
  min-height: 200px;
  width: 100%;
}

.loadingText {
  line-height: 1.2;
  animation: pulse 1.5s ease-in-out infinite;
}

.resultContainer {
  width: 100%;
}

.resultRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  min-height: 20px;
}

.resultRowWithBorder {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  min-height: 20px;
}

.resultGrid {
  width: 100%;
}

.resultGridItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  min-height: 20px;
}

.resultGridItemWithBorder {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  min-height: 20px;
}

.dottedDivider {
  border-top: 1px dotted #c7c7c5;
}

.resultText {
  line-height: 1.2;
}

.resultGrid {
  width: 100%;
}

.resultCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
