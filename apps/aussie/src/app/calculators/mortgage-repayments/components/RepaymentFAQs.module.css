.faqTitle {
  font-family: tt-commons-pro, sans-serif;
}

.faqContainer {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.faqItem {
  border: none;
  margin-bottom: 0;
  width: 100%;
}

.faqSummary {
  cursor: pointer;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: tt-commons-pro, sans-serif;
  font-weight: 600;
  color: #5c5f66;
  font-size: 14px;
  line-height: 1.4;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
  box-sizing: border-box;
  transition: color 0.2s ease;
}

.faqSummary:hover {
  color: #6c757d;
}

.faqQuestion {
  flex: 1;
}

.faqChevron {
  font-size: 12px;
  color: #5c5f66;
  line-height: 1;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.faqItem[open] .faqChevron {
  transform: rotate(90deg);
}

.faqItem:not([open]) .faqChevron {
  transform: rotate(0deg);
}

.faqContent {
  padding: 20px 0;
  padding-left: 24px;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
}

.faqContent :global(p) {
  font-family: Barlow, sans-serif;
  line-height: 1.6;
  margin: 0;
  width: 100%;
}

/* Hide default details marker */
.faqSummary::-webkit-details-marker {
  display: none;
}

.faqSummary::marker {
  display: none;
}
