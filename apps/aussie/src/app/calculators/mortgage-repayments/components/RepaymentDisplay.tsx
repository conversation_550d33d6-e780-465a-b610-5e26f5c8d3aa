import React from 'react';
import { Box, Grid, Stack, Text } from '@mantine/core';

import classes from './RepaymentDisplay.module.css';

interface RepaymentDisplayProps {
  monthlyRepayment: number;
  totalRepayments: number;
  totalInterest: number;
  interestOnlyPeriodRepayment?: number;
  remainderPeriodRepayment?: number;
  yearsSaved?: number;
  interestSaved?: number;
  loading: boolean;
  isInterestOnly?: boolean;
  interestOnlyYears?: number;
}

export function RepaymentDisplay({
  monthlyRepayment,
  totalRepayments,
  totalInterest,
  interestOnlyPeriodRepayment,
  remainderPeriodRepayment,
  yearsSaved,
  interestSaved,
  loading,
  isInterestOnly = false,
  interestOnlyYears = 4,
}: RepaymentDisplayProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <Stack gap="md" align="center" className={classes.loadingContainer}>
        <Text size="xl" fw={600} c="gray.8" ta="center">
          Your estimated repayments
        </Text>

        <Box className={classes.loadingContainer}>
          <Text size="3rem" fw={700} c="grape.8" ta="center" className={classes.loadingText}>
            Calculating...
          </Text>
        </Box>

        <Stack gap="xs" mt="lg" className={classes.resultContainer}>
          <Box className={classes.resultRow}>
            <Text size="sm" c="gray.7">
              Total repayments
            </Text>
            <Text size="sm" fw={600} c="gray.8">
              --
            </Text>
          </Box>

          <Box className={classes.resultRowWithBorder}>
            <Text size="sm" c="gray.7">
              Total interest
            </Text>
            <Text size="sm" fw={600} c="gray.8">
              --
            </Text>
          </Box>
        </Stack>
      </Stack>
    );
  }

  return (
    <Stack gap="md" align="center" className={classes.loadingContainer}>
      <Text size="xl" fw={600} c="gray.8" ta="center">
        Your estimated repayments
      </Text>

      {isInterestOnly && interestOnlyPeriodRepayment ? (
        // Interest-only loan display
        <Stack gap="md" className={classes.resultContainer}>
          <Box className={classes.resultRow}>
            <Box>
              <Text size="3rem" fw={700} c="grape.8" className={classes.resultText}>
                {formatCurrency(interestOnlyPeriodRepayment)}
                <Text component="span" size="sm" c="grape.8">
                  /month
                </Text>
              </Text>
              <Text size="sm" c="gray.7">
                First {interestOnlyYears} {interestOnlyYears === 1 ? 'year' : 'years'}
              </Text>
            </Box>
            <Box>
              <Text size="3rem" fw={700} c="grape.8" className={classes.resultText}>
                {formatCurrency(remainderPeriodRepayment || 0)}
                <Text component="span" size="sm" c="grape.8">
                  /month
                </Text>
              </Text>
              <Text size="sm" c="gray.7">
                Remainder of loan term
              </Text>
            </Box>
          </Box>
        </Stack>
      ) : (
        // Principal and interest loan display
        <Stack gap="xs" align="center">
          <Text size="3rem" fw={700} c="grape.8" ta="center" className={classes.resultText}>
            {formatCurrency(monthlyRepayment)}
            <Text component="span" size="sm" c="grape.8">
              /month
            </Text>
          </Text>
          <Text size="sm" c="gray.7" ta="center">
            Remainder of loan term
          </Text>
        </Stack>
      )}

      <Box w="100%" h="1px" bg="gray.3" mt="md" className={classes.dottedDivider} />

      <Grid gutter="md" mt="lg" className={classes.resultGrid}>
        <Grid.Col span={6}>
          <Box className={classes.resultCard}>
            <Text size="xs" c="gray.7" ta="center">
              TOTAL REPAYMENTS
            </Text>
            <Text size="sm" fw={600} c="gray.8" ta="center">
              {formatCurrency(totalRepayments)}
            </Text>
          </Box>
        </Grid.Col>

        <Grid.Col span={6}>
          <Box className={classes.resultCard}>
            <Text size="xs" c="gray.7" ta="center">
              TOTAL INTEREST
            </Text>
            <Text size="sm" fw={600} c="gray.8" ta="center">
              {formatCurrency(totalInterest)}
            </Text>
          </Box>
        </Grid.Col>

        <Grid.Col span={6}>
          <Box className={classes.resultCard}>
            <Text size="xs" c="gray.7" ta="center">
              YEARS SAVED
            </Text>
            <Text size="sm" fw={600} c="gray.8" ta="center">
              {yearsSaved ? `${Math.round(yearsSaved)} years` : '0 years'}
            </Text>
          </Box>
        </Grid.Col>

        <Grid.Col span={6}>
          <Box className={classes.resultCard}>
            <Text size="xs" c="gray.7" ta="center">
              INTEREST SAVED
            </Text>
            <Text size="sm" fw={600} c="gray.8" ta="center">
              {formatCurrency(interestSaved || 0)}
            </Text>
          </Box>
        </Grid.Col>
      </Grid>
    </Stack>
  );
}
