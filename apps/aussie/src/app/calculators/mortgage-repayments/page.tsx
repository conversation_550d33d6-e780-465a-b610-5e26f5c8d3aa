export const dynamic = 'force-dynamic';

import { Box, Container, Stack, Text, Title } from '@mantine/core';
import type { Metadata } from 'next';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { PageTracking } from '@gp/ui/components';

import { CalculatorDisclaimer, LegalDisclaimer, OtherCalculators } from '../shared';
import { CalculatorType } from '../shared/constants';
import { RepaymentCalculator } from './components/RepaymentCalculator';
import { RepaymentFAQs } from './components/RepaymentFAQs';

export const metadata: Metadata = {
  title: 'Mortgage Repayment Calculator | How much will my repayments be? | Aussie',
  description:
    'Use our mortgage repayment calculator to estimate your home loan repayments based on your loan amount, interest rate and loan term.',
  alternates: {
    canonical: `${process.env.BASE_URL}/calculators/repayment-calculator/`,
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/calculators/repayment-calculator/`,
    title: 'Mortgage Repayment Calculator | How much will my repayments be? | Aussie',
    description:
      'Use our mortgage repayment calculator to estimate your home loan repayments based on your loan amount, interest rate and loan term.',
    siteName: 'Aussie Home Loans',
    images: [{ url: 'https://www.aussie.com.au/assets/favicon.ico', width: 800, height: 600 }],
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RepaymentCalculatorPage() {
  return (
    <PageTracking name="Repayment Calculator" category="AUSSIE_HOMES" withMarketingCloudTracking>
      <Box bg="white" h="100vh">
        <script
          type="application/ld+json"
          key="breadcrumb_json_ld"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: `${process.env.BASE_URL}/`,
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: 'Calculators',
                  item: `${process.env.BASE_URL}/calculators/`,
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  name: 'Repayment Calculator',
                  item: `${process.env.BASE_URL}/calculators/repayment-calculator/`,
                },
              ],
            }),
          }}
        />
        <MultiNavbar />
        <Container size="responsive" py={{ base: 'lg', sm: 'xxl' }}>
          <RepaymentCalculator />
        </Container>
        <CalculatorDisclaimer type={CalculatorType.REPAYMENT} />

        <RepaymentFAQs />

        <Box bg="white">
          <OtherCalculators
            calculators={[
              CalculatorType.SAVINGS,
              CalculatorType.BORROWING_POWER,
              CalculatorType.STAMP_DUTY,
              CalculatorType.EXTRA_REPAYMENTS,
            ]}
          />
        </Box>
        <Box bg="white">
          <LegalDisclaimer />
        </Box>
        <Box bg="white">
          <Footer />
        </Box>
      </Box>
    </PageTracking>
  );
}
