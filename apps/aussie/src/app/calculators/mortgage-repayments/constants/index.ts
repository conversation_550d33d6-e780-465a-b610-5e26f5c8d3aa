// Import shared constants
import { ENVIRONMENT_URLS } from '../../shared/constants';

export {
  ANIMATIONS,
  API_ENDPOINTS,
  COLORS,
  COMMON_TEXT,
  ENVIRONMENT_URLS,
  FONTS,
  LOAN_TERM_OPTIONS,
  LOAN_TYPE_OPTIONS,
  REPAYMENT_FREQUENCY_OPTIONS,
} from '../../shared/constants';

// Text constants
export const FORM_LABELS = {
  LOAN_DETAILS: 'Loan Details',
  LOAN_AMOUNT: 'How much do you want to borrow?',
  LOAN_TYPE: 'What type of loan do you want?',
  LOAN_TERM: 'What is your loan term?',
  INTEREST_RATE: 'What is your interest rate?',
  REPAYMENT_FREQUENCY: 'How often do you want to make repayments?',
  EXTRA_REPAYMENTS: 'Extra repayments',
  EXTRA_MONTHLY_REPAYMENT: 'How much extra can you pay each month?',
} as const;

export const RESULT_TEXT = {
  REPAYMENT_TITLE: 'Your estimated monthly repayment is',
  TOTAL_REPAYMENTS: 'Total repayments over loan term',
  TOTAL_INTEREST: 'Total interest paid',
  LOAN_TERM: 'Loan term',
  REPAYMENT_FREQUENCY: 'Repayment frequency',
  LENDER_DISCLAIMER: 'This is an estimate only. Actual repayments may vary.',
  BROKER_DISCLAIMER:
    'Your Aussie Broker can provide a more accurate repayment estimate based on your lender of choice.',
  TALK_TO_BROKER: 'Talk to a broker now',
  BROKER_DESCRIPTION:
    'Run the numbers with our calculators, then book an appointment with an Aussie Broker to discuss your options.',
  BOOK_APPOINTMENT: 'Book an appointment',
  CALCULATOR_DISCLAIMER:
    'This calculator provides an estimate only. Actual repayments may vary based on lender criteria and current market conditions.',
} as const;

export const BUTTON_TEXT = {
  CALCULATE: 'Calculate repayments',
  LOADING: 'Calculating...',
} as const;

// API constants - specific to repayment
export const REPAYMENT_API = {
  ENDPOINT: '/api/repayment-calculator',
  DEV_URL: `${ENVIRONMENT_URLS.DEV}/api/repayment-calculator`,
  STG_URL: `${ENVIRONMENT_URLS.STG}/api/repayment-calculator`,
  PROD_URL: `${ENVIRONMENT_URLS.PROD}/api/repayment-calculator`,
} as const;

// Interest only period options
export const INTEREST_ONLY_PERIOD_OPTIONS = [
  { value: '1', label: '1 year' },
  { value: '2', label: '2 years' },
  { value: '3', label: '3 years' },
  { value: '4', label: '4 years' },
  { value: '5', label: '5 years' },
] as const;

// Default values
export const DEFAULT_VALUES = {
  LOAN_AMOUNT: 450000,
  INTEREST_RATE: 6.09,
  LOAN_TERM: 30,
  EXTRA_MONTHLY_REPAYMENT: 0,
} as const;

// Validation constants
export const VALIDATION_MESSAGES = {
  LOAN_AMOUNT_MIN_MAX: 'Loan amount must be min $10,000 and max $100 million',
  LOAN_AMOUNT_REQUIRED: 'Loan amount must be greater than 0',
  INTEREST_RATE_REQUIRED: 'Interest rate must be greater than 0',
  LOAN_TERM_REQUIRED: 'Loan term must be greater than 0',
  INTEREST_ONLY_YEARS_REQUIRED: 'Interest only years must be between 1 and 5',
  EXTRA_REPAYMENTS_START_YEAR_REQUIRED: 'Extra repayments start year must be within loan term',
} as const;
