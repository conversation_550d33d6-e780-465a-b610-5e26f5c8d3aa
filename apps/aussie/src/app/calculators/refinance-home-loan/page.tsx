export const dynamic = 'force-dynamic';

import { Container, Stack, Text, Title } from '@mantine/core';
import type { Metadata } from 'next';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { PageTracking } from '@gp/ui/components';

import { CalculatorDisclaimer, OtherCalculators } from '../shared';
import { CalculatorType } from '../shared/constants';
import { RefinanceCalculator } from './components/RefinanceCalculator';
import { RefinanceFAQs } from './components/RefinanceFAQs';

export const metadata: Metadata = {
  title: 'Refinance Home Loan Calculator | Calculate Refinancing Savings | Aussie Home Loans',
  description:
    "Calculate your potential savings when refinancing your home loan with Aussie's online refinance calculator. See how much you could save with a better interest rate.",
  alternates: {
    canonical: `${process.env.BASE_URL}/calculators/refinance-home-loan/`,
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/calculators/refinance-home-loan/`,
    title: 'Refinance Home Loan Calculator | Calculate Refinancing Savings | Aussie Home Loans',
    description:
      "Calculate your potential savings when refinancing your home loan with Aussie's online refinance calculator. See how much you could save with a better interest rate.",
    siteName: 'Aussie Home Loans',
    images: [{ url: 'https://www.aussie.com.au/assets/favicon.ico', width: 800, height: 600 }],
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RefinanceHomeLoanCalculatorPage() {
  return (
    <PageTracking
      name="Refinance Home Loan Calculator"
      category="AUSSIE_HOMES"
      withMarketingCloudTracking
    >
      <div style={{ backgroundColor: 'white', minHeight: '100vh' }}>
        <script
          type="application/ld+json"
          key="breadcrumb_json_ld"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: `${process.env.BASE_URL}/`,
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: 'Calculators',
                  item: `${process.env.BASE_URL}/calculators/`,
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  name: 'Refinance Home Loan',
                  item: `${process.env.BASE_URL}/calculators/refinance-home-loan/`,
                },
              ],
            }),
          }}
        />
        <MultiNavbar />
        <Container size="responsive" py={{ base: 'lg', sm: 'xxl' }}>
          <RefinanceCalculator />
        </Container>

        <CalculatorDisclaimer type={CalculatorType.REFINANCE} />

        <RefinanceFAQs />

        <div style={{ backgroundColor: '#ffffff' }}>
          <OtherCalculators
            calculators={[
              CalculatorType.REPAYMENTS,
              CalculatorType.SAVINGS,
              CalculatorType.BORROWING_POWER,
              CalculatorType.STAMP_DUTY,
            ]}
          />
        </div>
        <div style={{ backgroundColor: '#ffffff' }}>
          <Footer />
        </div>
      </div>
    </PageTracking>
  );
}
