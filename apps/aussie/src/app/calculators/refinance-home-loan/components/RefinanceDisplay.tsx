'use client';

import React from 'react';
import { Box, Group, Paper, Stack, Text, Title } from '@mantine/core';

import { LinkButtonWithTracking } from '@gp/ui/components';
import { sendEvent } from '@gp/util/interaction-studio';

import { formatCurrency } from '../../shared/utils';
import { COLORS, COMMON_TEXT, FONTS, RESULT_TEXT } from '../constants';

interface RefinanceResult {
  monthlySavings: number;
  totalInterestSaved: number;
  currentMonthlyPayment: number;
  newMonthlyPayment: number;
  totalRepaymentsNewLoan: number;
  totalInterestNewLoan: number;
  brokerMonthlySavings: number;
  brokerTotalSavings: number;
}

interface RefinanceDisplayProps {
  result: RefinanceResult | null;
  brokerRate: number;
  newInterestRate?: number;
}

export function RefinanceDisplay({ result, brokerRate, newInterestRate }: RefinanceDisplayProps) {
  // Empty state values
  const emptyResult = {
    monthlySavings: null,
    totalInterestSaved: null,
    currentMonthlyPayment: null,
    newMonthlyPayment: null,
    totalRepaymentsNewLoan: null,
    totalInterestNewLoan: null,
    brokerMonthlySavings: null,
    brokerTotalSavings: null,
  };

  const displayResult = result || emptyResult;

  return (
    <Stack gap="xl">
      {/* Refinancing Summary */}
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="xl">
          <Title order={2} size="h3" c={COLORS.GRAY_8} style={{ fontFamily: FONTS.PRIMARY }}>
            {RESULT_TEXT.REFINANCING_SUMMARY}
          </Title>

          {/* Summary of Savings */}
          <Box>
            <Title order={3} size="h4" c={'#4b1f68'} mb="md" style={{ fontFamily: FONTS.PRIMARY }}>
              {RESULT_TEXT.SUMMARY_OF_SAVINGS}
            </Title>
            <Paper
              p="lg"
              radius="md"
              style={{
                backgroundColor: '#f8f9fa',
                border: `1px solid ${COLORS.DIVIDER_COLOR}`,
              }}
            >
              <Stack gap="md">
                <Group justify="space-between">
                  <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                    {RESULT_TEXT.MONTHLY_SAVINGS}
                  </Text>
                  <Text size="xl" fw={700} c={COLORS.GRAPE_8} style={{ fontFamily: FONTS.PRIMARY }}>
                    {formatCurrency(displayResult.monthlySavings)}
                  </Text>
                </Group>
                <Group justify="space-between">
                  <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                    {RESULT_TEXT.TOTAL_INTEREST_SAVED}
                  </Text>
                  <Text size="xl" fw={700} c={COLORS.GRAPE_8} style={{ fontFamily: FONTS.PRIMARY }}>
                    {formatCurrency(displayResult.totalInterestSaved)}
                  </Text>
                </Group>
              </Stack>
            </Paper>
          </Box>

          {/* Current vs New Loan */}
          <Title
            order={3}
            size="h4"
            c={COLORS.GRAPE_8}
            mb="md"
            style={{ fontFamily: FONTS.PRIMARY }}
          >
            {RESULT_TEXT.CURRENT_VS_NEW_LOAN}
          </Title>
          <Stack gap="md">
            <Group justify="space-between">
              <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                {RESULT_TEXT.CURRENT_MONTHLY_PAYMENT}
              </Text>
              <Text size="lg" fw={600} c={COLORS.GRAY_8} style={{ fontFamily: FONTS.PRIMARY }}>
                {formatCurrency(displayResult.currentMonthlyPayment)}
              </Text>
            </Group>
            <Group justify="space-between">
              <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                {RESULT_TEXT.NEW_MONTHLY_PAYMENT}
              </Text>
              <Text size="lg" fw={600} c={COLORS.GRAY_8} style={{ fontFamily: FONTS.PRIMARY }}>
                {formatCurrency(displayResult.newMonthlyPayment)}
              </Text>
            </Group>
            <Group justify="space-between">
              <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                {RESULT_TEXT.TOTAL_REPAYMENTS_NEW_LOAN}
              </Text>
              <Text size="lg" fw={600} c={COLORS.GRAY_8} style={{ fontFamily: FONTS.PRIMARY }}>
                {formatCurrency(displayResult.totalRepaymentsNewLoan)}
              </Text>
            </Group>
            <Group justify="space-between">
              <Text size="md" c={COLORS.GRAY_7} style={{ fontFamily: FONTS.SECONDARY }}>
                {RESULT_TEXT.TOTAL_INTEREST_NEW_LOAN}
              </Text>
              <Text size="lg" fw={600} c={COLORS.GRAY_8} style={{ fontFamily: FONTS.PRIMARY }}>
                {formatCurrency(displayResult.totalInterestNewLoan)}
              </Text>
            </Group>
          </Stack>
        </Stack>
      </Paper>

      {/* Broker Section */}
      <Paper p="xl" radius="md" bg="grape.8">
        <Stack gap="lg" align="center">
          <Title
            order={3}
            size="h4"
            c={COLORS.WHITE}
            ta="center"
            style={{ fontFamily: 'tt-commons-pro, sans-serif' }}
          >
            Talk to a broker now
          </Title>

          <Text size="md" c={COLORS.WHITE} ta="center" style={{ fontFamily: FONTS.SECONDARY }}>
            Run the numbers with our calculators, then book an appointment with an Aussie Broker to
            discuss your options.
          </Text>

          <LinkButtonWithTracking
            size="lg"
            bg="yellow.5"
            c={COLORS.GRAPE_8}
            fw={600}
            href="/book-appointment"
            label={COMMON_TEXT.BOOK_APPOINTMENT}
            position="Refinance Calculator"
            purpose="book_appointment"
            onClick={() =>
              sendEvent('CTA Clicked', {
                cta_text: COMMON_TEXT.BOOK_APPOINTMENT,
                cta_position: 'Refinance Calculator',
              })
            }
          >
            {COMMON_TEXT.BOOK_APPOINTMENT}
          </LinkButtonWithTracking>
        </Stack>
      </Paper>
    </Stack>
  );
}
