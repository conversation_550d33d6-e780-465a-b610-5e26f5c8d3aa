'use client';

import React, { useEffect, useState } from 'react';
import { Alert, Box, Grid, NumberInput, Paper, Stack, Text, Title } from '@mantine/core';
import { useForm } from '@mantine/form';

import { ButtonWithTracking } from '@gp/ui/components';
import { sendEvent } from '@gp/util/interaction-studio';

import { formatCurrency } from '../../shared/utils';
import {
  BROKER_RATE,
  BUTTON_TEXT,
  COLORS,
  DEFAULT_VALUES,
  FONTS,
  FORM_LABELS,
  LOAN_TERM_OPTIONS,
  RESULT_TEXT,
  VALIDATION_MESSAGES,
} from '../constants';
import { RefinanceDisplay } from './RefinanceDisplay';

/**
 * Calculate monthly mortgage payment using the standard formula
 */
function calculateMonthlyPayment(principal: number, annualRate: number, years: number): number {
  const monthlyRate = annualRate / 100 / 12;
  const totalPayments = years * 12;

  if (monthlyRate === 0) {
    return principal / totalPayments;
  }

  const numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, totalPayments);
  const denominator = Math.pow(1 + monthlyRate, totalPayments) - 1;

  return numerator / denominator;
}

/**
 * Calculate total interest paid over the loan term
 */
function calculateTotalInterest(monthlyPayment: number, principal: number, years: number): number {
  const totalPayments = years * 12;
  const totalRepayments = monthlyPayment * totalPayments;
  return totalRepayments - principal;
}

interface FormValues {
  currentLoanAmount: number;
  currentInterestRate: number;
  remainingLoanTerm: number;
  newInterestRate: number;
  newLoanTerm: number;
}

interface RefinanceResult {
  monthlySavings: number;
  totalInterestSaved: number;
  currentMonthlyPayment: number;
  newMonthlyPayment: number;
  totalRepaymentsNewLoan: number;
  totalInterestNewLoan: number;
  brokerMonthlySavings: number;
  brokerTotalSavings: number;
  error?: string;
}

export function RefinanceCalculator() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<RefinanceResult | null>(null);

  const form = useForm<FormValues>({
    initialValues: {
      currentLoanAmount: DEFAULT_VALUES.CURRENT_LOAN_AMOUNT,
      currentInterestRate: DEFAULT_VALUES.CURRENT_INTEREST_RATE,
      remainingLoanTerm: DEFAULT_VALUES.REMAINING_LOAN_TERM,
      newInterestRate: DEFAULT_VALUES.NEW_INTEREST_RATE,
      newLoanTerm: DEFAULT_VALUES.NEW_LOAN_TERM,
    },
    validate: {
      currentLoanAmount: (value) => {
        if (!value || value <= 0) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_REQUIRED;
        }
        if (value < 10000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        if (value > 100000000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        return null;
      },
      currentInterestRate: (value) =>
        value > 0 ? null : 'Current interest rate must be greater than 0',
      remainingLoanTerm: (value) =>
        value > 0 ? null : 'Remaining loan term must be greater than 0',
      newInterestRate: (value) => (value > 0 ? null : 'New interest rate must be greater than 0'),
      newLoanTerm: (value) => (value > 0 ? null : 'New loan term must be greater than 0'),
    },
  });

  const calculateSavings = async (values: FormValues) => {
    // Don't calculate if form has validation errors
    if (Object.keys(form.errors).length > 0) {
      return;
    }

    setLoading(true);

    try {
      // Calculate current loan payments
      const currentMonthlyPayment = calculateMonthlyPayment(
        values.currentLoanAmount,
        values.currentInterestRate,
        values.remainingLoanTerm
      );
      const currentTotalInterest = calculateTotalInterest(
        currentMonthlyPayment,
        values.currentLoanAmount,
        values.remainingLoanTerm
      );

      // Calculate new loan payments
      const newMonthlyPayment = calculateMonthlyPayment(
        values.currentLoanAmount,
        values.newInterestRate,
        values.newLoanTerm
      );
      const newTotalInterest = calculateTotalInterest(
        newMonthlyPayment,
        values.currentLoanAmount,
        values.newLoanTerm
      );

      // Calculate savings
      const monthlySavings = currentMonthlyPayment - newMonthlyPayment;
      const totalInterestSaved = currentTotalInterest - newTotalInterest;

      // Calculate broker scenario (using 5% rate)
      const brokerRate = 5.0;
      const brokerMonthlyPayment = calculateMonthlyPayment(
        values.currentLoanAmount,
        brokerRate,
        values.newLoanTerm
      );
      const brokerTotalInterest = calculateTotalInterest(
        brokerMonthlyPayment,
        values.currentLoanAmount,
        values.newLoanTerm
      );

      const brokerMonthlySavings = currentMonthlyPayment - brokerMonthlyPayment;
      const brokerTotalSavings = currentTotalInterest - brokerTotalInterest;

      const result: RefinanceResult = {
        monthlySavings,
        totalInterestSaved,
        currentMonthlyPayment,
        newMonthlyPayment,
        totalRepaymentsNewLoan: newMonthlyPayment * values.newLoanTerm * 12,
        totalInterestNewLoan: newTotalInterest,
        brokerMonthlySavings,
        brokerTotalSavings,
      };

      setResult(result);

      // Track calculation event
      sendEvent('Refinance Calculated', {
        calculator_type: 'refinance',
        current_loan_amount: values.currentLoanAmount,
        current_interest_rate: values.currentInterestRate,
        new_interest_rate: values.newInterestRate,
        remaining_loan_term: values.remainingLoanTerm,
        new_loan_term: values.newLoanTerm,
      });
    } catch (error) {
      console.error('Error calculating refinance savings:', error);
      setResult({
        monthlySavings: 0,
        totalInterestSaved: 0,
        currentMonthlyPayment: 0,
        newMonthlyPayment: 0,
        totalRepaymentsNewLoan: 0,
        totalInterestNewLoan: 0,
        brokerMonthlySavings: 0,
        brokerTotalSavings: 0,
        error: error instanceof Error ? error.message : 'An error occurred during calculation',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = form.onSubmit((values) => {
    calculateSavings(values);
  });

  return (
    <Box>
      <Grid gutter="xl">
        {/* Left Column - Calculator Form */}
        <Grid.Col span={{ base: 12, lg: 6 }}>
          <Paper p="xl" radius="md" withBorder>
            <Stack gap="xl">
              <Title order={1} size="h2" c={COLORS.GRAPE_8} style={{ fontFamily: FONTS.PRIMARY }}>
                {FORM_LABELS.REFINANCE_CALCULATOR}
              </Title>

              <form onSubmit={handleSubmit}>
                <Stack gap="lg">
                  <NumberInput
                    label={FORM_LABELS.CURRENT_LOAN_AMOUNT}
                    placeholder="Enter current loan amount"
                    value={form.values.currentLoanAmount}
                    onChange={(value) => {
                      if (value === '') {
                        form.setFieldValue('currentLoanAmount', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('currentLoanAmount', value);
                      }
                      // Trigger validation immediately
                      form.validateField('currentLoanAmount');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.currentLoanAmount) {
                        formatCurrency(form.values.currentLoanAmount);
                        // The NumberInput will automatically format with thousandSeparator and prefix
                      }
                    }}
                    error={form.errors.currentLoanAmount}
                    min={10000}
                    max={100000000}
                    step={1000}
                    allowNegative={false}
                    thousandSeparator=","
                    prefix="$"
                    styles={{
                      label: {
                        fontFamily: FONTS.SECONDARY,
                        fontWeight: 600,
                        color: COLORS.GRAY_8,
                      },
                    }}
                  />

                  <NumberInput
                    label={FORM_LABELS.CURRENT_INTEREST_RATE}
                    placeholder="Enter current interest rate"
                    value={form.values.currentInterestRate}
                    onChange={(value) => {
                      if (value === '') {
                        form.setFieldValue('currentInterestRate', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('currentInterestRate', value);
                      }
                      // Trigger validation immediately
                      form.validateField('currentInterestRate');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.currentInterestRate) {
                        // Interest rate is already a number, no additional formatting needed
                      }
                    }}
                    error={form.errors.currentInterestRate}
                    min={0}
                    max={100}
                    decimalScale={2}
                    allowNegative={false}
                    styles={{
                      label: {
                        fontFamily: FONTS.SECONDARY,
                        fontWeight: 600,
                        color: COLORS.GRAY_8,
                      },
                    }}
                  />

                  <NumberInput
                    label={FORM_LABELS.REMAINING_LOAN_TERM}
                    placeholder="Enter remaining loan term"
                    value={form.values.remainingLoanTerm}
                    onChange={(value) => {
                      if (value === '') {
                        form.setFieldValue('remainingLoanTerm', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('remainingLoanTerm', value);
                      }
                      // Trigger validation immediately
                      form.validateField('remainingLoanTerm');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.remainingLoanTerm) {
                        // Loan term is already a number, no additional formatting needed
                      }
                    }}
                    error={form.errors.remainingLoanTerm}
                    min={1}
                    max={50}
                    allowNegative={false}
                    styles={{
                      label: {
                        fontFamily: FONTS.SECONDARY,
                        fontWeight: 600,
                        color: COLORS.GRAY_8,
                      },
                    }}
                  />

                  <NumberInput
                    label={FORM_LABELS.NEW_INTEREST_RATE}
                    placeholder="Enter new interest rate"
                    value={form.values.newInterestRate}
                    onChange={(value) => {
                      if (value === '') {
                        form.setFieldValue('newInterestRate', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('newInterestRate', value);
                      }
                      // Trigger validation immediately
                      form.validateField('newInterestRate');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.newInterestRate) {
                        // Interest rate is already a number, no additional formatting needed
                      }
                    }}
                    error={form.errors.newInterestRate}
                    min={0}
                    max={100}
                    decimalScale={2}
                    allowNegative={false}
                    styles={{
                      label: {
                        fontFamily: FONTS.SECONDARY,
                        fontWeight: 600,
                        color: COLORS.GRAY_8,
                      },
                    }}
                  />

                  <NumberInput
                    label={FORM_LABELS.NEW_LOAN_TERM}
                    placeholder="Enter new loan term"
                    value={form.values.newLoanTerm}
                    onChange={(value) => {
                      if (value === '') {
                        form.setFieldValue('newLoanTerm', 0);
                      } else if (typeof value === 'number') {
                        form.setFieldValue('newLoanTerm', value);
                      }
                      // Trigger validation immediately
                      form.validateField('newLoanTerm');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.newLoanTerm) {
                        // Loan term is already a number, no additional formatting needed
                      }
                    }}
                    error={form.errors.newLoanTerm}
                    min={1}
                    max={50}
                    allowNegative={false}
                    styles={{
                      label: {
                        fontFamily: FONTS.SECONDARY,
                        fontWeight: 600,
                        color: COLORS.GRAY_8,
                      },
                    }}
                  />

                  <ButtonWithTracking
                    label={loading ? BUTTON_TEXT.LOADING : BUTTON_TEXT.CALCULATE_SAVINGS}
                    type="submit"
                    size="lg"
                    loading={loading}
                    bg={COLORS.GRAPE_8}
                    c="white"
                    position="Refinance Calculator"
                    purpose="calculate_savings"
                    onClick={() =>
                      sendEvent('Repayment Calculated', {
                        calculator_type: 'refinance',
                        current_rate: form.values.currentInterestRate,
                        new_rate: form.values.newInterestRate,
                      })
                    }
                  >
                    {loading ? BUTTON_TEXT.LOADING : BUTTON_TEXT.CALCULATE_SAVINGS}
                  </ButtonWithTracking>
                </Stack>
              </form>
            </Stack>
          </Paper>
        </Grid.Col>

        {/* Right Column - Results Display */}
        <Grid.Col span={{ base: 12, lg: 6 }}>
          <RefinanceDisplay
            result={result}
            brokerRate={BROKER_RATE}
            newInterestRate={form.values.newInterestRate}
          />
        </Grid.Col>
      </Grid>

      {result?.error && (
        <Alert
          title="Calculation Error"
          color="red"
          mt="md"
          style={{ fontFamily: FONTS.SECONDARY }}
        >
          {result.error}
        </Alert>
      )}
    </Box>
  );
}
