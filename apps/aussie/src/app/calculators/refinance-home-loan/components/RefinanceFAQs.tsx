'use client';

import React from 'react';
import { Box, Container, Stack, Text, Title } from '@mantine/core';

const faqData = [
  {
    question: 'What is refinancing?',
    answer:
      'Refinancing is the process of replacing your existing home loan with a new one, usually to get a better interest rate, change loan terms, or access equity in your property.',
  },
  {
    question: 'When should I consider refinancing?',
    answer:
      'You might consider refinancing when interest rates have dropped, your financial situation has improved, you want to consolidate debt, or you need to access equity in your property.',
  },
  {
    question: 'What costs are involved in refinancing?',
    answer:
      "Refinancing costs can include application fees, valuation fees, legal fees, discharge fees from your current lender, and potentially break costs if you're on a fixed rate loan.",
  },
  {
    question: 'How do I know if refinancing is worth it?',
    answer:
      'Compare the costs of refinancing with the potential savings.\n\nConsider factors like the interest rate difference, loan term, fees, and how long you plan to stay in the property.',
  },
  {
    question: 'Can I refinance if I have bad credit?',
    answer:
      "It may be more challenging to refinance with bad credit, but it's not impossible.\n\nYou might need to work on improving your credit score first or consider lenders who specialize in bad credit loans.",
  },
  {
    question: 'How long does the refinancing process take?',
    answer:
      'The refinancing process typically takes 2-4 weeks, but it can vary depending on the lender, your circumstances, and how quickly you provide required documentation.',
  },
];

export function RefinanceFAQs() {
  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="xl">
        <Title
          order={2}
          size="h1"
          ta="center"
          c="gray.9"
          style={{ fontFamily: 'tt-commons-pro, sans-serif' }}
        >
          Frequently Asked Questions
        </Title>

        <Box
          style={{
            maxWidth: '800px',
            margin: '0 auto',
            width: '100%',
          }}
        >
          <Stack gap="xs">
            {faqData.map((faq, index) => (
              <details
                key={index}
                style={{
                  border: 'none',
                  marginBottom: '0',
                  width: '100%',
                }}
              >
                <summary
                  style={{
                    cursor: 'pointer',
                    padding: '16px 0',
                    borderBottom: '1px solid #e9ecef',
                    listStyle: 'none',
                    fontFamily: 'tt-commons-pro, sans-serif',
                    fontWeight: 600,
                    fontSize: '16px',
                    color: '#495057',
                  }}
                >
                  {faq.question}
                </summary>
                <Box
                  style={{
                    padding: '16px 0',
                    borderBottom: '1px solid #e9ecef',
                  }}
                >
                  <Text
                    size="sm"
                    c="gray.7"
                    style={{ fontFamily: 'Barlow, sans-serif', lineHeight: 1.6 }}
                  >
                    {faq.answer.split('\n\n').map((paragraph, index) => (
                      <React.Fragment key={index}>
                        {paragraph.split('\n').map((line, lineIndex) => (
                          <React.Fragment key={lineIndex}>
                            {line}
                            {lineIndex < paragraph.split('\n').length - 1 && <br />}
                          </React.Fragment>
                        ))}
                        {index < faq.answer.split('\n\n').length - 1 && (
                          <>
                            <br />
                            <br />
                          </>
                        )}
                      </React.Fragment>
                    ))}
                  </Text>
                </Box>
              </details>
            ))}
          </Stack>
        </Box>
      </Stack>
    </Container>
  );
}
