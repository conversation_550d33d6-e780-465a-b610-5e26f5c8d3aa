export enum CalculatorType {
  STAMP_DUTY = 'stamp_duty',
  SAVINGS = 'savings',
  REPAYMENTS = 'repayments',
  EXTRA_REPAYMENTS = 'extra_repayments',
  BORROWING_POWER = 'borrowing_power',
  REFINANCE = 'refinance',
  REPAYMENT = 'repayment',
}

export interface CalculatorItem {
  id: CalculatorType;
  title: string;
  description: string;
  linkText: string;
  href: string;
  icon: string;
}

export const calculatorData: Record<CalculatorType, CalculatorItem> = {
  [CalculatorType.STAMP_DUTY]: {
    id: CalculatorType.STAMP_DUTY,
    title: 'Stamp Duty Calculator',
    description:
      'Find out how much you may need to pay in stamp duty and see what assistance could be available to you.',
    linkText: 'Calculate stamp duty',
    href: '/calculators/stamp-duty-calculator/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/4L0nL1pwpOG1g5wrYfXa03/7ea82b89fa2d6b9ca5257de3ba5f3bf2/Broker.svg?w=64&q=75',
  },
  [CalculatorType.SAVINGS]: {
    id: CalculatorType.SAVINGS,
    title: 'Savings Calculator',
    description:
      'Work out how long you can expect to be saving to have the right amount for a home loan deposit.',
    linkText: 'Calculate savings',
    href: '/calculators/mortgage-deposit-calculator/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/8jtTz7j1MT98XXVqF5gui/ac805992a436921211acdb880fa4b8e3/Deposit.svg?w=64&q=75',
  },
  [CalculatorType.REPAYMENTS]: {
    id: CalculatorType.REPAYMENTS,
    title: 'Repayments Calculator',
    description:
      'Calculate your estimated home loan repayments and see how an interest change could impact your budget.',
    linkText: 'Calculate repayments',
    href: '/calculators/repayment-calculator/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/4L0nL1pwpOG1g5wrYfXa03/7ea82b89fa2d6b9ca5257de3ba5f3bf2/Broker.svg?w=64&q=75',
  },
  [CalculatorType.EXTRA_REPAYMENTS]: {
    id: CalculatorType.EXTRA_REPAYMENTS,
    title: 'Extra Repayments Calculator',
    description:
      'Work out if you can pay down your home loan faster and save money by making regular extra repayments.',
    linkText: 'Calculate extra repayments',
    href: '/calculators/mortgage-extra-repayment-calculator/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/31VaCk6U1EMQ5HqWLS1BUm/c0b67863fbdf154355b38e3e2a256edb/Expert.svg?w=64&q=75',
  },
  [CalculatorType.BORROWING_POWER]: {
    id: CalculatorType.BORROWING_POWER,
    title: 'Borrowing Power Calculator',
    description:
      'Get an idea of how much you may be able to borrow to get started on your property journey.',
    linkText: 'Calculate borrowing power',
    href: '/calculators/borrowing-power/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/31VaCk6U1EMQ5HqWLS1BUm/c0b67863fbdf154355b38e3e2a256edb/Expert.svg?w=64&q=75',
  },
  [CalculatorType.REFINANCE]: {
    id: CalculatorType.REFINANCE,
    title: 'Refinance Home Loan Calculator',
    description:
      'Calculate your potential savings when refinancing your home loan and see how much you could save with a better interest rate.',
    linkText: 'Calculate refinance savings',
    href: '/calculators/refinance-home-loan/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/4L0nL1pwpOG1g5wrYfXa03/7ea82b89fa2d6b9ca5257de3ba5f3bf2/Broker.svg?w=64&q=75',
  },
  [CalculatorType.REPAYMENT]: {
    id: CalculatorType.REPAYMENT,
    title: 'Repayment Calculator',
    description:
      'Calculate your estimated home loan repayments and see how an interest change could impact your budget.',
    linkText: 'Calculate repayments',
    href: '/calculators/repayment-calculator/',
    icon: 'https://images.ctfassets.net/nbklqgdg5mdx/4L0nL1pwpOG1g5wrYfXa03/7ea82b89fa2d6b9ca5257de3ba5f3bf2/Broker.svg?w=64&q=75',
  },
};

// Common form field options
export const LOAN_TERM_OPTIONS = [
  { value: '15', label: '15 years' },
  { value: '20', label: '20 years' },
  { value: '25', label: '25 years' },
  { value: '30', label: '30 years' },
];

export const LOAN_TYPE_OPTIONS = [
  { value: 'principal_interest', label: 'Principal and Interest' },
  { value: 'interest_only', label: 'Interest Only' },
];

export const REPAYMENT_FREQUENCY_OPTIONS = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'fortnightly', label: 'Fortnightly' },
  { value: 'weekly', label: 'Weekly' },
];

export const STATE_OPTIONS = [
  { value: 'NSW', label: 'NSW' },
  { value: 'VIC', label: 'VIC' },
  { value: 'QLD', label: 'QLD' },
  { value: 'WA', label: 'WA' },
  { value: 'SA', label: 'SA' },
  { value: 'TAS', label: 'TAS' },
  { value: 'ACT', label: 'ACT' },
  { value: 'NT', label: 'NT' },
];

export const APPLICANT_OPTIONS = [
  { value: '1', label: 'Me' },
  { value: '2', label: 'Me plus one' },
];

export const DEPENDANT_OPTIONS = [
  { value: '0', label: '0' },
  { value: '1', label: '1' },
  { value: '2', label: '2' },
  { value: '3', label: '3' },
  { value: '4', label: '4' },
  { value: '5', label: '5+' },
];

// Common text constants
export const COMMON_TEXT = {
  TO: 'to',
  CALCULATE: 'Calculate',
  LOADING: 'Calculating...',
  BOOK_APPOINTMENT: 'Book an appointment',
  TALK_TO_BROKER: 'Talk to a broker now',
  CALCULATOR_DISCLAIMER:
    'This calculator provides an estimate only. Actual results may vary based on lender criteria and current market conditions.',
  LENDER_DISCLAIMER: 'This is an estimate only. Actual results may vary.',
  BROKER_DISCLAIMER:
    'Your Aussie Broker can provide a more accurate estimate based on your lender of choice.',
  BROKER_DESCRIPTION:
    'Calculate your results, then book an appointment with an Aussie Broker to discuss your options.',
} as const;

// Common style constants
export const COLORS = {
  GRAPE_8: 'grape.8',
  GRAPE_7: 'grape.7',
  GRAPE_9: 'grape.9',
  GRAY_6: 'gray.6',
  GRAY_7: 'gray.7',
  GRAY_8: 'gray.8',
  YELLOW_4: 'yellow.4',
  YELLOW_5: 'yellow.5',
  WHITE: 'white',
  BORDER_COLOR: '#6f42c1',
  DIVIDER_COLOR: '#c7c7c5',
} as const;

export const FONTS = {
  PRIMARY: 'tt-commons-pro, sans-serif',
  SECONDARY: 'Barlow, sans-serif',
} as const;

export const ANIMATIONS = {
  PULSE: 'pulse 1.5s ease-in-out infinite',
  TRANSITION: '0.8s ease-in-out',
} as const;

// Environment-specific base URLs
export const ENVIRONMENT_URLS = {
  DEV: 'https://gp-aussie-latest.lendi-paas-dev.net',
  STG: 'https://gp-aussie-latest.lendi-paas-stg.net',
  PROD: 'https://gp-aussie-latest.lendi-paas.net',
} as const;

// Common API endpoints
export const API_ENDPOINTS = {
  BORROWING_POWER: '/api/borrowing-power',
  REPAYMENT_CALCULATOR: '/api/repayment-calculator',
  REFINANCE_CALCULATOR: '/api/refinance-calculator',
} as const;

// Common liability categories
export const LIABILITY_CATEGORIES = {
  CREDIT_CARD: 'CREDIT_CARD',
  PERSONAL_LOAN: 'PERSONAL_LOAN',
  OTHER: 'OTHER',
} as const;
