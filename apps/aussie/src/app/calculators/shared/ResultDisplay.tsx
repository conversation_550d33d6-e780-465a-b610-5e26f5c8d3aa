import React from 'react';
import { Alert, Box, Group, Paper, Stack, Text } from '@mantine/core';

import { LinkButtonWithTracking } from '@gp/ui/components';

import { COLORS, COMMON_TEXT, FONTS } from './constants';
import { formatCurrency } from './utils';

import classes from './ResultDisplay.module.css';

interface ResultDisplayProps {
  title: string;
  minAmount: number;
  maxAmount: number;
  loading?: boolean;
  error?: string;
  showProgressBar?: boolean;
  brokerSection?: {
    title: string;
    description: string;
    buttonText: string;
    buttonHref: string;
  };
  disclaimer?: string;
}

export function ResultDisplay({
  title,
  minAmount,
  maxAmount,
  loading = false,
  error,
  showProgressBar = true,
  brokerSection,
  disclaimer,
}: ResultDisplayProps) {
  const formatAmount = (amount: number): string => {
    return loading ? '---,---' : formatCurrency(amount);
  };

  return (
    <Box>
      {error && (
        <Alert color="red" mb="lg">
          {error}
        </Alert>
      )}

      <Stack gap={0}>
        <Paper p="lg" bg="white" className={classes.cardTop}>
          <Stack gap="md" align="center">
            <Text size="xl" fw={600} c={COLORS.GRAY_8} ta="center" className={classes.title}>
              {title}
            </Text>

            <Group gap="md" align="center" justify="center">
              <Text size="36px" fw={700} c={COLORS.GRAPE_8} className={classes.amount}>
                {formatAmount(minAmount)}
              </Text>
              <Text size="xl" c={COLORS.GRAY_6} className={classes.toText}>
                {COMMON_TEXT.TO}
              </Text>
              <Text size="36px" fw={700} c={COLORS.GRAPE_8} className={classes.amount}>
                {formatAmount(maxAmount)}
              </Text>
            </Group>

            {showProgressBar && (
              <Box w="100%" mt="md">
                <Box className={classes.progressTrack}>
                  <Box className={classes.progressBar} />
                </Box>
              </Box>
            )}

            <Box w="100%" h="1px" bg="gray.3" mt="md" className={classes.dottedDivider} />

            {disclaimer && (
              <Text
                component="div"
                size="sm"
                c={COLORS.GRAY_7}
                ta="center"
                mt="sm"
                className={classes.disclaimer}
              >
                {disclaimer}
              </Text>
            )}
          </Stack>
        </Paper>

        {brokerSection && (
          <Paper p="xl" bg={COLORS.GRAPE_8} className={classes.brokerCard}>
            <Stack gap="md" align="center">
              <Text size="xl" fw={600} c={COLORS.WHITE} ta="center" className={classes.brokerTitle}>
                {brokerSection.title}
              </Text>
              <Text size="md" c={COLORS.WHITE} ta="center" className={classes.brokerText}>
                {brokerSection.description}
              </Text>
              <LinkButtonWithTracking
                href={brokerSection.buttonHref}
                label={brokerSection.buttonText}
                size="lg"
                bg={COLORS.YELLOW_5}
                c={COLORS.GRAPE_8}
                fw={600}
                mt="sm"
                radius="xl"
                w="280px"
                variant="filled"
              >
                {brokerSection.buttonText}
              </LinkButtonWithTracking>
            </Stack>
          </Paper>
        )}
      </Stack>

      <Alert color="blue" variant="light" radius="md" mt="lg">
        <Text component="span" size="sm" style={{ fontFamily: FONTS.SECONDARY }}>
          {COMMON_TEXT.CALCULATOR_DISCLAIMER}
        </Text>
      </Alert>
    </Box>
  );
}
