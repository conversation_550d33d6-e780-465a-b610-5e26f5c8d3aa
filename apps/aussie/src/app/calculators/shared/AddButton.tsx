import React from 'react';
import { Group, Text } from '@mantine/core';

import { ButtonWithTracking } from '@gp/ui/components';

import { COLORS } from './constants';

interface AddButtonProps {
  onClick: () => void;
  children: React.ReactNode;
}

import classes from './AddButton.module.css';

export function AddButton({ onClick, children }: AddButtonProps) {
  return (
    <ButtonWithTracking
      label="Add"
      variant="subtle"
      onClick={onClick}
      classNames={{ root: classes.root }}
    >
      <Group gap="xs" align="center">
        <Text size="md" fw={700} c={COLORS.GRAPE_8} className={classes.plus}>
          +
        </Text>
        <Text size="md" c={COLORS.GRAPE_8} className={classes.label}>
          {children}
        </Text>
      </Group>
    </ButtonWithTracking>
  );
}
