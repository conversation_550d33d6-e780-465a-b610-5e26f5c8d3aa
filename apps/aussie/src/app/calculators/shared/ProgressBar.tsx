import React from 'react';
import { Box, Group, Stack, Text } from '@mantine/core';

import { COLORS } from './constants';

import classes from './ProgressBar.module.css';

interface ProgressBarProps {
  minAmount: number;
  maxAmount: number;
  minLabel?: string;
  maxLabel?: string;
}

export function ProgressBar({ minAmount, maxAmount, minLabel, maxLabel }: ProgressBarProps) {
  const formatCurrency = (amount: number): string => {
    if (amount === 0) return '0';

    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`.replace('.0M', 'M');
    }
    return `${(amount / 1000).toFixed(0)}K`;
  };

  const calculateProgressBar = () => {
    if (minAmount === 0 && maxAmount === 0) {
      return { left: 0, width: 0, rangeMin: 0, rangeMax: 0 };
    }

    const rangeMin = Math.floor(minAmount / 100000) * 100000;
    const rangeMax = Math.ceil(maxAmount / 100000) * 100000;
    const rangeSize = rangeMax - rangeMin;

    const left = rangeSize > 0 ? ((minAmount - rangeMin) / rangeSize) * 100 : 0;
    const width = rangeSize > 0 ? ((maxAmount - minAmount) / rangeSize) * 100 : 0;

    return { left, width, rangeMin, rangeMax };
  };

  const { left, width, rangeMin, rangeMax } = calculateProgressBar();

  return (
    <Stack gap="xs" w="100%" mt="sm">
      <Box className={classes.track}>
        <Box className={classes.bar} style={{ left: `${left}%`, width: `${width}%` }} />
      </Box>
      <Group justify="space-between">
        <Text size="sm" c={COLORS.GRAY_6} className={classes.label}>
          {minLabel || `$${formatCurrency(rangeMin)}`}
        </Text>
        <Text size="sm" c={COLORS.GRAY_6} className={classes.label}>
          {maxLabel || `$${formatCurrency(rangeMax)}`}
        </Text>
      </Group>
    </Stack>
  );
}
