 Step-by-step refactor guide for calculators/shared

 Goals
 - Remove inline styles and replace with CSS Modules or theme tokens
 - Use tracked components from @gp/ui/components where applicable
 - Preserve existing design and functionality; no new fields

 Scope
 - RemoveButton.tsx
 - AddButton.tsx
 - ProgressBar.tsx
 - FAQSection.tsx
 - CalculatorDisclaimer.tsx
 - LegalDisclaimer.tsx
 - ResultDisplay.tsx

 1) RemoveButton
 - Replaced Mantine Button with ButtonWithTracking to align with Event Tracking rules
 - Moved inline styles to RemoveButton.module.css with classNames.root
 - Preserved size, shape, colors via theme tokens (grape, white) and CSS vars

 Files changed:
 - RemoveButton.tsx
 - RemoveButton.module.css

 2) AddButton
 - Replaced Mantine Button with ButtonWithTracking
 - Moved inline styles to AddButton.module.css and applied via classNames.root
 - Kept text structure (+ label) and colors per theme

 Files changed:
 - AddButton.tsx
 - AddButton.module.css

 3) ProgressBar
 - Extracted inline styles into ProgressBar.module.css (track, bar, label)
 - Preserved dynamic left/width calculations and visual structure
 - Kept color tokens using CSS variables

 Files changed:
 - ProgressBar.tsx
 - ProgressBar.module.css

 4) FAQSection
 - Moved all inline styles to FAQSection.module.css
 - Kept custom expand/collapse logic; no behavior changes
 - Applied theme fonts per aussie-theme rules

 Files changed:
 - FAQSection.tsx
 - FAQSection.module.css

 5) CalculatorDisclaimer
 - Replaced all inline styles with CalculatorDisclaimer.module.css
 - Retained structure, content, and typography sizing
 - Continued use of CollapsibleDisclaimer wrapper from @gp/ui/components

 Files changed:
 - CalculatorDisclaimer.tsx
 - CalculatorDisclaimer.module.css

 6) LegalDisclaimer
 - Replaced inline styles on details/summary and text with LegalDisclaimer.module.css
 - Converted summary chevron rotation from styled-jsx to CSS module
 - No changes to content or layout

 Files changed:
 - LegalDisclaimer.tsx
 - LegalDisclaimer.module.css

 7) ResultDisplay
 - Moved all inline styles into ResultDisplay.module.css
 - Replaced CTA Button with ButtonWithTracking (kept same href/size/label)
 - Preserved progress bar visuals with CSS module classes
 - No copy or layout changes

 Files changed:
 - ResultDisplay.tsx
 - ResultDisplay.module.css

 Validation Checklist
 - Visual parity: Compare before/after in browser
 - Interaction parity: expand/collapse, clicks, navigation unchanged
 - Tracking: ButtonWithTracking used for CTA and icon actions where applicable
 - No new props or fields introduced
 - Fonts comply with aussie-theme (tt-commons-pro for headings, Barlow for body) per workspace memory

 Notes
 - All components continue to use Mantine tokens and CSS variables for colors and spacing
 - Event tracking is added only via tracked components; no additional events were introduced

