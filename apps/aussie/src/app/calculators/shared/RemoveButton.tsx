import React from 'react';
import { Button } from '@mantine/core';

import { ButtonWithTracking } from '@gp/ui/components';

import { COLORS } from './constants';

interface RemoveButtonProps {
  onClick: () => void;
}

import classes from './RemoveButton.module.css';

export function RemoveButton({ onClick }: RemoveButtonProps) {
  return (
    <ButtonWithTracking
      label="Remove"
      size="xs"
      w="32px"
      h="32px"
      p={0}
      radius="50%"
      onClick={onClick}
      classNames={{ root: classes.root }}
      variant="filled"
    >
      ×
    </ButtonWithTracking>
  );
}
