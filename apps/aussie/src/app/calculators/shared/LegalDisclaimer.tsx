'use client';

import React, { useEffect, useState } from 'react';
import { Container, Stack, Text } from '@mantine/core';

import classes from './LegalDisclaimer.module.css';

export function LegalDisclaimer() {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch by not rendering details until mounted
  if (!mounted) {
    return (
      <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
        <Stack gap="md">
          <div className={classes.summary}>
            <span className={classes.chevron}>►</span>
            LEGAL DISCLAIMER
          </div>
        </Stack>
      </Container>
    );
  }

  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="md">
        <details className={classes.details} open={isOpen}>
          <summary
            className={classes.summary}
            onClick={(e) => {
              e.preventDefault();
              setIsOpen(!isOpen);
            }}
          >
            <span className={classes.chevron}>►</span>
            LEGAL DISCLAIMER
          </summary>
          {isOpen && (
            <Stack gap="lg" pt="md">
              <Text size="sm" c="gray.7" className={classes.text}>
                Aussie does not provide financial or investment advice. This material does not take
                account of your objectives, financial circumstances or needs. Aussie recommends that
                you seek independent financial, legal and taxation advice before making an
                investment decision.
              </Text>

              <Text size="sm" c="gray.7" fw={600} className={classes.text}>
                IMPORTANT INFORMATION: Any information provided does not constitute an offer of
                credit and are examples of what may be available to you based on the information
                available. It does not take into account any product features or any applicable
                fees. Lending criteria and the basis upon which we assess what you may be able to
                afford may change at any time without notice. For Fixed Rate home loans, break costs
                may be payable which can be significant if you change the whole or part of your
                fixed rate loan or where additional or early repayments are made during the fixed
                rate period.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                *Source: CoreLogic February 2025 Index and Lendi Group Rates based on our Generally
                Available Criteria. Repayment based on a $650,000 variable loan at 6.20% p.a. over
                30 years. Interest savings calculated using a comparison between 6.20% and 5.65%
                rates over the life of a 30-year variable loan with consistent repayments. Scenarios
                are for illustrative purposes only and may not apply to your situation. Not all
                lenders are available through all brokers.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                **Not all lenders are available through all brokers.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                ^Individual lenders may charge fees to the customer.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                ^^All applications are subject to lender assessment and approval. Cashback offers
                may be provided by some lenders and may only be available for particular products,
                terms and conditions apply.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                Door to More Competition AU18+ only. Ends 29/6/25 11:59pm AEST. Entry limits apply
                (see full T&amp;Cs for details). aussie.com.au/home-loans/door-to-more
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                We are committed to protecting customer privacy and do not sell customer
                information, please refer to our Privacy Policy for more information.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                Our mission is to help Aussies reach their property goals faster. We can compare
                thousands of home loans from over 25 leading lenders.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                © 2024 Lendi Group Distribution Pty Ltd ABN ************** Australian Credit Licence
                246786. Lendi Group Pty Ltd, which is the ultimate holding company of the Aussie and
                Lendi businesses, is owned by numerous shareholders including banks such as CBA,
                1835i (ANZ&apos;s external venture capital partner) and Macquarie Bank, the Lendi
                founders and employees, and a number of Australian institutional investors and
                sophisticated investors, including UniSuper.
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                Credit for the Aussie Bridge products are provided by Bridgit Financial Services Pty
                Ltd ACN *********** Australian Credit Licence Number 532542
              </Text>

              <Text size="sm" c="gray.7" className={classes.text}>
                Information regarding listing numbers are supplied by Lendi Group Services Pty Ltd
                (ABN **************) and related entities on behalf of RP Data Pty Ltd trading as
                CoreLogic Asia Pacific.
              </Text>

              <Text size="sm" c="gray.7" fw={600} ta="center" className={classes.footer}>
                Lendi Group Distribution Pty Ltd | Level 28, Grosvenor Place, 225 George Street,
                Sydney NSW 2000
              </Text>
            </Stack>
          )}
        </details>
      </Stack>
    </Container>
  );
}
