'use client';

import React from 'react';
import { Box, Container, Grid, Group, Paper, Stack, Text, Title } from '@mantine/core';
import Image from 'next/image';

import { calculatorData, CalculatorType } from './constants';

interface OtherCalculatorsProps {
  calculators: CalculatorType[];
  title?: string;
}

export function OtherCalculators({
  calculators,
  title = 'Other calculators',
}: OtherCalculatorsProps) {
  const selectedCalculators = calculators.map((type) => calculatorData[type]);

  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="xl">
        <Title
          order={2}
          size="h1"
          ta="center"
          c="gray.9"
          style={{ fontFamily: 'tt-commons-pro, sans-serif' }}
        >
          {title}
        </Title>

        <Grid gutter="lg">
          {selectedCalculators.map((calculator, index) => (
            <Grid.Col key={calculator.id} span={{ base: 12, sm: 6, lg: 3 }}>
              <Paper
                h="100%"
                p="xl"
                style={{
                  border: '1px solid #e9ecef',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'box-shadow 0.2s ease',
                  '&:hover': {
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  },
                }}
                component="a"
                href={calculator.href}
              >
                <Stack gap="md" h="100%" justify="space-between">
                  <Stack gap="md">
                    <Box ta="center">
                      <Image
                        src={calculator.icon}
                        alt={calculator.title}
                        width={64}
                        height={64}
                        style={{ display: 'block', margin: '0 auto' }}
                      />
                    </Box>

                    <Title
                      order={3}
                      size="h4"
                      c="grape.8"
                      ta="center"
                      style={{
                        fontFamily: 'tt-commons-pro, sans-serif',
                        textDecoration: 'underline',
                      }}
                    >
                      {calculator.title}
                    </Title>

                    <Text
                      size="sm"
                      c="gray.7"
                      ta="center"
                      style={{
                        fontFamily: 'Barlow, sans-serif',
                        lineHeight: 1.5,
                      }}
                    >
                      {calculator.description}
                    </Text>
                  </Stack>

                  <Group justify="center" mt="auto">
                    <Group gap="xs" align="center">
                      <Text
                        size="md"
                        fw={600}
                        c="grape.8"
                        style={{
                          fontFamily: 'Barlow, sans-serif',
                          textDecoration: 'underline',
                        }}
                      >
                        {calculator.linkText}
                      </Text>
                    </Group>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>
          ))}
        </Grid>
      </Stack>
    </Container>
  );
}
