'use client';

import React, { useState } from 'react';
import { Box, Container, Paper, Stack, Text, Title } from '@mantine/core';

import { COLORS, FONTS } from './constants';

import classes from './FAQSection.module.css';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  title: string;
  faqs: FAQItem[];
}

export function FAQSection({ title, faqs }: FAQSectionProps) {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="xl">
        <Title order={2} size="h1" ta="center" c="gray.9" className={classes.title}>
          {title}
        </Title>

        <Stack gap="md">
          {faqs.map((faq, index) => (
            <Paper key={index} withBorder className={classes.paper}>
              <Box className={classes.item} onClick={() => toggleItem(index)}>
                <Stack gap="md">
                  <Box className={classes.row}>
                    <Text
                      component="div"
                      size="md"
                      fw={600}
                      c={COLORS.GRAPE_8}
                      className={classes.question}
                    >
                      {faq.question}
                    </Text>
                    <Text
                      component="span"
                      size="xl"
                      c={COLORS.GRAPE_8}
                      className={classes.plus}
                      style={{ transform: openItems.has(index) ? 'rotate(45deg)' : 'rotate(0deg)' }}
                    >
                      +
                    </Text>
                  </Box>

                  {openItems.has(index) && (
                    <Text size="md" c="gray.7" className={classes.answer}>
                      {faq.answer}
                    </Text>
                  )}
                </Stack>
              </Box>
            </Paper>
          ))}
        </Stack>
      </Stack>
    </Container>
  );
}
