/**
 * Format a number as currency with proper formatting
 */
export function formatCurrency(amount: number | null): string {
  if (amount === null) return '$ -';
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format a number as currency for display in progress bars and ranges
 */
export function formatRangeAmount(amount: number): string {
  if (amount === 0) return '0';

  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`.replace('.0M', 'M');
  }
  return `${(amount / 1000).toFixed(0)}K`;
}

/**
 * Calculate monthly repayment amount
 */
export function calculateMonthlyRepayment(
  principal: number,
  annualRate: number,
  years: number
): number {
  const monthlyRate = annualRate / 100 / 12;
  const totalPayments = years * 12;

  if (monthlyRate === 0) return principal / totalPayments;

  return (
    (principal * monthlyRate * Math.pow(1 + monthlyRate, totalPayments)) /
    (Math.pow(1 + monthlyRate, totalPayments) - 1)
  );
}

/**
 * Calculate total interest paid over loan term
 */
export function calculateTotalInterest(
  principal: number,
  monthlyRepayment: number,
  years: number
): number {
  const totalPayments = years * 12;
  const totalRepaid = monthlyRepayment * totalPayments;
  return totalRepaid - principal;
}

/**
 * Calculate total repayments over loan term
 */
export function calculateTotalRepayments(monthlyRepayment: number, years: number): number {
  return monthlyRepayment * years * 12;
}

/**
 * Get the appropriate API endpoint based on the current environment
 */
export function getApiEndpoint(baseEndpoint: string): string {
  // Check if we're in a browser environment
  // if (typeof window !== 'undefined') {
  //   // In browser, use relative endpoint (will use current domain)
  //   return baseEndpoint;
  // }

  // Get the app environment from environment variables
  const appEnvironment =
    process.env.APP_ENVIRONMENT ||
    process.env.NEXT_PUBLIC_APP_ENVIRONMENT ||
    process.env.NX_PUBLIC_APP_ENVIRONMENT;

  // Get base URL from environment variables or use defaults based on APP_ENVIRONMENT
  const baseUrl =
    process.env.API_BASE_URL ||
    process.env.NEXT_PUBLIC_API_BASE_URL ||
    process.env.NX_PUBLIC_API_BASE_URL ||
    (() => {
      switch (appEnvironment) {
        case 'development': {
          return 'https://gp-aussie-latest.lendi-paas-dev.net';
        }
        case 'staging': {
          return 'https://gp-aussie-latest.lendi-paas-stg.net';
        }
        case 'production': {
          return 'https://gp-aussie-latest.lendi-paas.net';
        }
        default: {
          // Fallback to environment detection if APP_ENVIRONMENT is not set
          const nodeEnv = process.env.NODE_ENV;
          const isDev = process.env.NODE_ENV === 'development';
          const isStaging =
            process.env.NEXT_PUBLIC_ENV === 'staging' ||
            process.env.VERCEL_ENV === 'preview' ||
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'staging';
          const isProduction = process.env.NODE_ENV === 'production' && !isStaging;

          if (isProduction) {
            return 'https://gp-aussie-latest.lendi-paas.net';
          } else if (isStaging) {
            return 'https://gp-aussie-latest.lendi-paas-stg.net';
          } else if (isDev) {
            return 'https://gp-aussie-latest.lendi-paas-dev.net';
          }
          // Fallback to production
          return 'https://gp-aussie-latest.lendi-paas.net';
        }
      }
    })();

  return `${baseUrl}${baseEndpoint}`;
}
