 .cardTop {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom: none;
  border: 1px solid var(--mantine-color-gray-2);
 }

 .title {
   font-family: <PERSON>, sans-serif;
 }

 .amount {
   font-family: tt-commons-pro, sans-serif;
   line-height: 1;
 }

 .toText {
   font-family: <PERSON>, sans-serif;
   margin: 0 16px;
 }

.progressTrack {
  height: 8px;
  background-color: var(--mantine-color-gray-2);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

 .progressBar {
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   background-color: var(--mantine-color-yellow-5);
   border-radius: 4px;
 }

 .dottedDivider {
  border-top: 1px dotted var(--mantine-color-gray-3);
 }

 .brokerCard {
   border-bottom-left-radius: 12px;
   border-bottom-right-radius: 12px;
   border-top: none;
   border-top-left-radius: 0;
   border-top-right-radius: 0;
 }

 .brokerTitle {
   font-family: tt-commons-pro, sans-serif;
 }

 .brokerText {
   font-family: <PERSON>, sans-serif;
 }

 .disclaimer {
   font-family: <PERSON>, sans-serif;
 }

