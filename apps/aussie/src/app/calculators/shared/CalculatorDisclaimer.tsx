'use client';

import React from 'react';
import { Stack, Text, Title } from '@mantine/core';

import { CollapsibleDisclaimer } from '@gp/ui/components';

import { CalculatorType } from './constants';

import classes from './CalculatorDisclaimer.module.css';

interface CalculatorDisclaimerProps {
  type: CalculatorType;
}

export function CalculatorDisclaimer({ type }: CalculatorDisclaimerProps) {
  const getDisclaimerContent = () => {
    switch (type) {
      case CalculatorType.BORROWING_POWER:
        return {
          title: 'Borrowing calculator assumptions',
          intro:
            'The information provided by this borrowing power calculator should be treated as a guide only, and not be relied on as true indication of your borrowing power, or a quote or indication of pre-qualification for any home loan product.',
          assumptionsTitle: 'Our assumptions about you when calculating borrowing power:',
          assumptions: [
            'Your income is PAYG base salary',
            'If there is one applicant, you are single. If there are two applicants you are married/defacto, and property ownership are split equally',
            'Repayments do not include liabilities related to properties you already own',
            'If you intend to live in the property, the repayment type will be principal and interest. If you are looking to buy an investment, the repayment will be interest only for 1 year.',
          ],
          conclusion:
            'These assumptions allow us to help you determine estimated borrowing power amounts that will help with your research. In practice, actual home loan calculations may differ based on how a lender interprets these factors in regards to any given loan.',
          additional:
            'Before applying for a loan, your actual monthly expenditure and other factors will have to be taken into account in calculating your actual borrowing capacity.',
          brokerNote:
            'Your Aussie Broker can provide a more accurate borrowing power estimate based on your lender of choice.',
        };

      case CalculatorType.REFINANCE:
        return {
          title: 'Refinance calculator assumptions',
          intro:
            'The information provided by this refinance calculator should be treated as a guide only, and not be relied on as true indication of your potential savings, or a quote or indication of pre-qualification for any home loan product.',
          assumptionsTitle: 'Our assumptions when calculating refinance savings:',
          assumptions: [
            'Interest rates remain constant over the loan term',
            'No additional fees or charges are included in the calculation',
            'Repayments are made on time and in full',
            'The new loan term is the same as the remaining term on your current loan',
          ],
          conclusion:
            'These assumptions allow us to help you determine estimated refinance savings that will help with your research. In practice, actual refinance calculations may differ based on how a lender interprets these factors in regards to any given loan.',
          additional:
            'Before applying for a refinance, your actual financial circumstances and other factors will have to be taken into account in calculating your actual savings.',
          brokerNote:
            'Your Aussie Broker can provide a more accurate refinance estimate based on your lender of choice.',
        };

      case CalculatorType.REPAYMENT:
        return {
          title: "For Aussie's Loan Calculators",
          intro:
            'Your home loan repayments are a combination of your outstanding loan amount, also called the principal, and the interest charged on the loan by your lender. If you have an interest only loan you will delay repaying the principal and only pay the interest charged for a set period of time.The information provided by this loan calculator should be treated as a guide only, and not be relied on as true indication of your loan repayments, or a quote or indication of pre-qualification for any loan product. The Aussie Calculators calculate the required repayment amount based on the terms of your loan: primarily loan amount, interest rate, repayment frequency and loan term.',
          assumptionsTitle: 'Key calculator assumptions',
          assumptions: [
            'Length of Month - It is assumed that each month is of equal length.',
            'Weeks and fortnights in a year - 52 and 26, respectively.',
            'Rounding - Calculated dollar amounts are rounded to the nearest whole cent.',
          ],
          conclusion:
            'In practice, actual loan calculations may differ based on how a lender interprets these factors in regards to any given loan. For example, we calculate fortnightly repayments based on an exact two week period, whereas some lenders may require half of month repayments, which will result in a higher figure. These assumptions allow us to help you determine estimated repayment amounts that will help with your research.',
          additional: '',
          brokerNote: '',
        };

      default:
        return {
          title: 'Calculator assumptions',
          intro:
            'This calculator provides an estimate only. Actual results may vary based on lender criteria and current market conditions.',
          assumptionsTitle: 'Our assumptions:',
          assumptions: [],
          conclusion:
            'These assumptions allow us to help you determine estimated results that will help with your research.',
          additional:
            'Before applying for a loan, your actual financial circumstances and other factors will have to be taken into account.',
          brokerNote:
            'Your Aussie Broker can provide a more accurate estimate based on your lender of choice.',
        };
    }
  };

  const content = getDisclaimerContent();

  return (
    <CollapsibleDisclaimer title="Calculator Disclaimer">
      <Stack gap="lg">
        <Title order={2} size="h2" c="gray.8" className={classes.title}>
          {content.title}
        </Title>

        <Text size="md" c="gray.7" className={classes.text}>
          {content.intro}
        </Text>

        {content.assumptions.length > 0 && (
          <>
            <Text size="md" fw={600} c="gray.8" className={classes.assumptionsTitle}>
              {content.assumptionsTitle}
            </Text>

            <Stack gap="sm" pl="md">
              {content.assumptions.map((assumption, index) => (
                <Text key={index} component="div" size="md" c="gray.7" className={classes.text}>
                  <Text component="span" fw={600}>
                    •
                  </Text>
                  <Text component="span" ml="md">
                    {assumption}
                  </Text>
                </Text>
              ))}
            </Stack>
          </>
        )}

        <Text size="md" c="gray.7" className={classes.text}>
          {content.conclusion}
        </Text>

        <Text size="md" c="gray.7" className={classes.text}>
          {content.additional}
        </Text>

        <Text size="md" c="gray.7" className={`${classes.text} ${classes.divider}`}>
          {content.brokerNote}
        </Text>
      </Stack>
    </CollapsibleDisclaimer>
  );
}
