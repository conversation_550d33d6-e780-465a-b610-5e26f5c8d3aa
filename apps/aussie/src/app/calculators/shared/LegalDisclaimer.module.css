 .details {
   border: none;
   margin-bottom: 0;
 }

 .summary {
   cursor: pointer;
   padding: 12px 0;
   border-bottom: 1px solid #e9ecef;
   list-style: none;
   display: flex;
   align-items: center;
   gap: 8px;
   font-family: tt-commons-pro, sans-serif;
   font-weight: 600;
   color: #5c5f66;
   font-size: 16px;
   line-height: 1.5;
 }

 .chevron {
   font-size: 18px;
   line-height: 1;
   transition: transform 0.2s ease;
 }

 .text {
   font-family: <PERSON>, sans-serif;
   line-height: 1.6;
 }

 .footer {
   font-family: Barlow, sans-serif;
   line-height: 1.6;
   padding-top: 16px;
   border-top: 1px solid #e9ecef;
 }

 details[open] .chevron {
  transform: rotate(90deg);
}

