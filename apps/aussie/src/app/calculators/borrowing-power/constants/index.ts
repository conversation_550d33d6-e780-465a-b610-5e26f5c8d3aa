// Import shared constants
import { getApiEndpoint } from '../../shared/utils';

export {
  ANIMATIONS,
  API_ENDPOINTS,
  APPLICANT_OPTIONS,
  COLORS,
  COMMON_TEXT,
  DEPENDANT_OPTIONS,
  ENVIRONMENT_URLS,
  FONTS,
  LIABILITY_CATEGORIES,
  STATE_OPTIONS,
} from '../../shared/constants';

// Text constants
export const FORM_LABELS = {
  YOUR_DETAILS: 'Your details',
  WHO_IS_LOAN_FOR: 'Who is this loan for?',
  DEPENDANTS: 'How many dependants do you financially support?',
  YOUR_INCOME: 'Your income',
  TOTAL_ANNUAL_INCOME: 'What is your total annual income before tax?',
  PARTNER_ANNUAL_INCOME: 'What is your partner&apos;s total annual income before tax?',
  LOAN_DETAILS: 'Loan Details',
  PROPERTY_VALUE: 'What is the estimated value of the property you want to buy?',
  LOAN_AMOUNT: 'How much do you want to borrow?',
  POSTCODE: 'What is the postcode of the property you want to buy?',
  STATE: 'What state is the property in?',
  MONTHLY_EXPENSES: 'Monthly expenses',
  LIVING_EXPENSES: 'What are your total monthly living expenses?',
  TOTAL_LIABILITIES: 'Your total liabilities',
  CREDIT_CARDS: 'Credit cards',
  CREDIT_CARD_LIMIT: 'Credit card limit',
  HOME_LOANS: 'Home loans',
  MONTHLY_REPAYMENT: 'Monthly repayment',
  PERSONAL_LOANS: 'Personal loans',
  OTHER_LIABILITIES: 'Other liabilities',
  ADD_CREDIT_CARD: 'Add another credit card',
  ADD_HOME_LOAN: 'Add another home loan',
  ADD_PERSONAL_LOAN: 'Add another personal loan',
  ADD_OTHER_LIABILITY: 'Add another liability',
} as const;

export const RESULT_TEXT = {
  BORROWING_POWER_TITLE: 'Your approximate borrowing power is',
  LENDER_DISCLAIMER: 'Lender assessment rates will yield different results.',
  BROKER_DISCLAIMER:
    'Your Aussie Broker can provide a more accurate borrowing power estimate based on your lender of choice.',
  TALK_TO_BROKER: 'Talk to a broker now',
  BROKER_DESCRIPTION:
    'Run the numbers with our calculators, then book an appointment with an Aussie Broker to discuss your options.',
} as const;

export const BUTTON_TEXT = {
  CALCULATE: 'Calculate borrowing power',
  UPDATE: 'Update your results',
  LOADING: 'Calculating...',
} as const;

// Default values
export const DEFAULT_VALUES = {
  SECURITY_VALUE: 800000,
  LOAN_AMOUNT: 640000,
  POSTCODE: '2000',
  MONTHLY_EXPENSE: 3000,
  INCOME: 100000,
  CREDIT_LIMIT: 5000,
  MONTHLY_REPAYMENT: 500,
  REMAINING_TERMS: 1,
} as const;

// Validation constants
export const VALIDATION_MESSAGES = {
  LOAN_AMOUNT_MIN_MAX: 'Loan amount must be min $10,000 and max $100 million',
  LOAN_AMOUNT_REQUIRED: 'Loan amount must be greater than 0',
} as const;
