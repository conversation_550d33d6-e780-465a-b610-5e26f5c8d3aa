import { DEFAULT_VALUES, LIABILITY_CATEGORIES } from '../constants';

export interface LiabilityItem {
  limit?: number;
  monthlyRepayment?: number;
}

export interface APILiability {
  category: string;
  creditLimit: number;
  monthlyRepayment: number;
  remainingTerms: number;
}

export const createLiabilityHandlers = (
  items: LiabilityItem[],
  setItems: (items: LiabilityItem[]) => void
) => ({
  add: () => setItems([...items, { limit: 0, monthlyRepayment: 0 }]),
  remove: (index: number) => setItems(items.filter((_, i) => i !== index)),
  update: (index: number, field: string, value: number) => {
    const updated = items.map((item, i) => (i === index ? { ...item, [field]: value } : item));
    setItems(updated);
  },
});

export const buildAPIPayload = (
  creditCards: LiabilityItem[],
  homeLoans: LiabilityItem[],
  personalLoans: LiabilityItem[],
  otherLiabilities: LiabilityItem[]
): APILiability[] => [
  ...creditCards.map((card) => ({
    category: LIABILITY_CATEGORIES.CREDIT_CARD,
    creditLimit: card.limit || 0,
    monthlyRepayment: 0,
    remainingTerms: DEFAULT_VALUES.REMAINING_TERMS,
  })),
  ...homeLoans.map((loan) => ({
    category: LIABILITY_CATEGORIES.OTHER,
    creditLimit: 0,
    monthlyRepayment: loan.monthlyRepayment || 0,
    remainingTerms: DEFAULT_VALUES.REMAINING_TERMS,
  })),
  ...personalLoans.map((loan) => ({
    category: LIABILITY_CATEGORIES.PERSONAL_LOAN,
    creditLimit: 0,
    monthlyRepayment: loan.monthlyRepayment || 0,
    remainingTerms: DEFAULT_VALUES.REMAINING_TERMS,
  })),
  ...otherLiabilities.map((liability) => ({
    category: LIABILITY_CATEGORIES.OTHER,
    creditLimit: 0,
    monthlyRepayment: liability.monthlyRepayment || 0,
    remainingTerms: DEFAULT_VALUES.REMAINING_TERMS,
  })),
];
