export const dynamic = 'force-dynamic';

import { Box, Container } from '@mantine/core';
import type { Metadata } from 'next';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';
import { PageTracking } from '@gp/ui/components';

import { CalculatorDisclaimer, LegalDisclaimer, OtherCalculators } from '../shared';
import { CalculatorType } from '../shared/constants';
import { BorrowingPowerCalculator } from './components/BorrowingPowerCalculator';
import { BorrowingPowerFAQs } from './components/BorrowingPowerFAQs';

export const metadata: Metadata = {
  title: 'Mortgage Borrowing Power Calculator | How much can I borrow? | Aussie Home Loans',
  description:
    "Want to know your borrowing capacity? Discover how much you can borrow for your mortgage with Aussie's online borrowing power calculator.",
  alternates: {
    canonical: `${process.env.BASE_URL}/calculators/borrowing-power/`,
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/calculators/borrowing-power/`,
    title: 'Mortgage Borrowing Power Calculator | How much can I borrow? | Aussie Home Loans',
    description:
      "Want to know your borrowing capacity? Discover how much you can borrow for your mortgage with Aussie's online borrowing power calculator.",
    siteName: 'Aussie Home Loans',
    images: [{ url: 'https://www.aussie.com.au/assets/favicon.ico', width: 800, height: 600 }],
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function BorrowingPowerPage() {
  return (
    <PageTracking
      name="Borrowing Power Calculator"
      category="AUSSIE_HOMES"
      withMarketingCloudTracking
    >
      <Box bg="white" h="100vh">
        <script
          type="application/ld+json"
          key="breadcrumb_json_ld"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: `${process.env.BASE_URL}/`,
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: 'Calculators',
                  item: `${process.env.BASE_URL}/calculators/`,
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  name: 'Borrowing Power',
                  item: `${process.env.BASE_URL}/calculators/borrowing-power/`,
                },
              ],
            }),
          }}
        />
        <MultiNavbar />
        <Container size="responsive" py={{ base: 'lg', sm: 'xxl' }}>
          <BorrowingPowerCalculator />
        </Container>

        <Box bg="white">
          <CalculatorDisclaimer type={CalculatorType.BORROWING_POWER} />
        </Box>

        <Box bg="white">
          <BorrowingPowerFAQs />
        </Box>

        <Box bg="white">
          <OtherCalculators
            calculators={[
              CalculatorType.SAVINGS,
              CalculatorType.STAMP_DUTY,
              CalculatorType.EXTRA_REPAYMENTS,
              CalculatorType.REPAYMENTS,
            ]}
          />
        </Box>

        <Box bg="white">
          <LegalDisclaimer />
        </Box>

        <Box bg="white">
          <Footer />
        </Box>
      </Box>
    </PageTracking>
  );
}
