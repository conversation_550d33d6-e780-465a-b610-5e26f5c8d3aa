import React from 'react';
import { Group, Text } from '@mantine/core';

import { formatCurrency } from '../../shared/utils';
import { COLORS, COMMON_TEXT } from '../constants';

import classes from './BorrowingPowerDisplay.module.css';

interface BorrowingPowerDisplayProps {
  minAmount: number;
  maxAmount: number;
  loading: boolean;
}

export function BorrowingPowerDisplay({
  minAmount,
  maxAmount,
  loading,
}: BorrowingPowerDisplayProps) {
  const formatAmount = (amount: number): string => {
    return loading ? '---,---' : formatCurrency(amount);
  };

  return (
    <Group gap="md" align="center" justify="center">
      <Text
        size="36px"
        fw={700}
        c={COLORS.GRAPE_8}
        className={`${classes.amountText} ${
          loading ? classes.amountTextLoading : classes.amountTextNotLoading
        }`}
      >
        {formatAmount(minAmount)}
      </Text>
      <Text size="xl" c={COLORS.GRAY_6} className={classes.separatorText}>
        {COMMON_TEXT.TO}
      </Text>
      <Text
        size="36px"
        fw={700}
        c={COLORS.GRAPE_8}
        className={`${classes.amountText} ${
          loading ? classes.amountTextLoading : classes.amountTextNotLoading
        }`}
      >
        {formatAmount(maxAmount)}
      </Text>
    </Group>
  );
}
