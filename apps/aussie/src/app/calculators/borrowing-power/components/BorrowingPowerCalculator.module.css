 .heroTitle {
   font-family: tt-commons-pro, sans-serif;
 }

 .heroText {
   font-family: Barlow, sans-serif;
 }

 .sectionTitle {
   font-family: tt-commons-pro, sans-serif;
 }

 .radio {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-grape-8);
 }

 .radio[data-checked='true'] {
  background-color: var(--mantine-color-grape-8);
  border-color: var(--mantine-color-grape-8);
 }

 .input {
  border-color: var(--mantine-color-gray-3);
   font-size: 16px;
 }

 .input:focus,
 .input:focus-within {
  border-color: var(--mantine-color-grape-8);
 }

 .dottedDivider {
  border-top: 2px dotted var(--mantine-color-gray-3);
 }

 .verticalDivider {
  border-left: 2px dotted var(--mantine-color-gray-3);
   min-height: 400px;
   align-self: stretch;
 }

 .sticky {
   position: sticky;
   top: 20px;
   z-index: 10;
 }

 .resultCardTop {
  border: 2px solid var(--mantine-color-grape-8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
   border-top-left-radius: 12px;
   border-top-right-radius: 12px;
   border-bottom-left-radius: 0;
   border-bottom-right-radius: 0;
   border-bottom: none;
 }

 .resultTitle {
   font-family: Barlow, sans-serif;
 }

 .resultDotted {
  border-top: 1px dotted var(--mantine-color-gray-3);
 }

 .brokerCard {
  border: 2px solid var(--mantine-color-grape-8);
   border-top: none;
   border-bottom-left-radius: 12px;
   border-bottom-right-radius: 12px;
   border-top-left-radius: 0;
   border-top-right-radius: 0;
 }

 .brokerTitle {
   font-family: tt-commons-pro, sans-serif;
 }

 .brokerText {
   font-family: Barlow, sans-serif;
 }

 .flexInput {
   flex: 1;
 }

 .gridDivider {
   display: flex;
   align-items: stretch;
   justify-content: center;
   padding: 0;
 }

 .disclaimerText {
   font-family: tt-commons-pro, sans-serif;
 }