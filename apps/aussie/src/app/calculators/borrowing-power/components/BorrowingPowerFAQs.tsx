'use client';

import React from 'react';
import { Box, Container, Stack, Text, Title } from '@mantine/core';

import classes from './BorrowingPowerFAQs.module.css';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: 'What is borrowing power?',
    answer:
      '<a href="https://www.aussie.com.au/insights/articles/home-loans-101-how-much-can-i-borrow/" target="_blank" rel="noopener noreferrer">Borrowing power</a>, also known as borrowing capacity, is the loan amount that a lender is likely to approve a borrower for.',
  },
  {
    question: 'What is my borrowing power?',
    answer:
      'Find out your borrowing capacity in seconds by using our borrowing power calculator above.\n\n<a href="https://www.aussie.com.au/book-appointment/" target="_blank" rel="noopener noreferrer">Speak to an Aussie Broker</a> if you\'d like to receive a more accurate borrowing power estimate based on your choice of lender.',
  },
  {
    question: 'How is my borrowing power calculated?',
    answer:
      'Borrowing capacity is calculated using several factors, including your personal details, income (such as your salary and any other income you earn) and expenses (such as your living expenses and personal loans).\n\nEach lender will have their own criteria for borrowing power, so your borrowing power may differ slightly from lender to lender.',
  },
  {
    question: 'What is good borrowing power?',
    answer:
      "There is no one-size-fits-all number when it comes to borrowing power. Generally, it may be a good idea to ensure your home loan repayments don't exceed 30% of your gross income.\n\nTry Aussie's Home Loan Repayments Calculator to discover your estimated repayments so you can determine what may be viable for your situation.",
  },
  {
    question: 'Does having a guarantor increase borrowing power?',
    answer:
      'With a <a href="https://www.aussie.com.au/insights/articles/guarantor-home-loans/" target="_blank" rel="noopener noreferrer">guarantor</a>, you could usually borrow up to 95% of the value of a property without paying Lenders Mortgage Insurance. However, the maximum LVR changes from lender to lender.\n\nHaving a guarantor means that the loan is secured by two properties, reducing the lender\'s risk. It\'s important to remember that you\'ll still need to show a lender you can meet repayments yourself, as guarantors don\'t typically make financial contributions to your mortgage repayments.\n\nThe easiest way to get an idea of your borrowing power is to use our <a href="https://www.aussie.com.au/calculators/mortgage-repayments/" target="_blank" rel="noopener noreferrer">Mortgage Repayments Calculator</a>.',
  },
  {
    question: 'How can I increase my borrowing power?',
    answer:
      'There are several ways you could increase your borrowing power. Some of these include:\n\n• Pay off your debts\n• Improve your credit score\n• Claim all your income\n• Cut down on your expenses\n• Reduce your credit card limits\n• Save more for your home loan deposit\n• Extend your home loan repayment term\n• Strengthen your savings habits\n\nYou can <a href="https://www.aussie.com.au/insights/articles/home-loans-101-how-much-can-i-borrow/" target="_blank" rel="noopener noreferrer">read more about how to boost your borrowing power here</a>.',
  },
  {
    question: 'Does HECS affect borrowing power?',
    answer:
      'Yes, HECS-HELP debt will generally affect your borrowing power, just like any other debt.\n\nHow this affects your borrowing power will depend on the amount owing and the minimum repayment rate, which is determined by your income.\n\n<a href="https://www.aussie.com.au/insights/articles/hecs-home-loan-changes/" target="_blank" rel="noopener noreferrer">Find out more about recent HECs home loan changes and what it means for your borrowing power</a>.',
  },
  {
    question: 'What impacts my borrowing power?',
    answer:
      'Several factors can impact your borrowing power, but lenders will typically take the following into consideration:\n\n• Income and assets\n• Credit history\n• Savings history\n• Debts\n• Typical monthly spending\n• The type of loan you want, as well as the loan term and interest rate\n• Home loan deposit size\n• The market value of your desired property\n• Personal characteristics such as age, job and number of applicants\n\nYou can <a href="https://www.aussie.com.au/insights/articles/home-loans-101-how-much-can-i-borrow/" target="_blank" rel="noopener noreferrer">read more about what impacts your borrowing power here</a>.',
  },
  {
    question: 'How much can I borrow using the equity in my home?',
    answer:
      '<a href="https://www.aussie.com.au/insights/articles/what-is-home-equity/" target="_blank" rel="noopener noreferrer">Home equity</a> is the difference between a property\'s current market value and any debt held against it. Basically, it is the proportion of the home that you own outright.\n\nIt\'s important to remember, though, that you might not be able to use the entire amount of your available equity, depending on how property prices are performing.\n\nLenders will generally allow you to borrow 80% of your home\'s current value, minus your outstanding debt.',
  },
  {
    question: 'Does equity increase your borrowing power?',
    answer:
      'Yes, if you have <a href="https://www.aussie.com.au/home-loans/refinance/accessing-equity-by-refinancing-your-home-loan/" target="_blank" rel="noopener noreferrer">equity</a> in an existing property you could substantially boost your borrowing power. Lenders will likely lend you no more than 80% of your home\'s current value.\n\nTo calculate your home\'s usable equity, take 80% of the value of your property minus your outstanding loan balance.\n\nFor example, let\'s say your property\'s value is $500,000 and you have an outstanding loan balance of $200,000:\n\n• Your home\'s value = $500,000 x 0.80% = $400,000\n• Your home\'s potential useable equity = $400,000 – $200,000 = $200,000\n\nSo, if your home is worth $500,000 and you still owe $200,000 on your mortgage, you have $200,000 of useable equity towards the purchase of an investment property.\n\nBut you\'ll still need to show the lender you can afford the repayments on the full loan amount, which will include both the previous mortgage and the new one. You can learn more about <a href="https://www.aussie.com.au/home-loans/refinance/accessing-equity-by-refinancing-your-home-loan/" target="_blank" rel="noopener noreferrer">accessing equity by refinancing your home loan</a>, and use our <a href="https://www.aussie.com.au/calculators/mortgage-repayments/" target="_blank" rel="noopener noreferrer">Mortgage Repayments Calculator</a> to get a better idea of your estimated repayments.',
  },
];

export function BorrowingPowerFAQs() {
  return (
    <Container size="responsive" py={{ base: 'xl', sm: 'xxl' }}>
      <Stack gap="xl">
        <Title order={2} size="h1" ta="center" c="gray.9" className={classes.faqTitle}>
          Borrowing Power FAQs
        </Title>

        <Box className={classes.faqContainer}>
          <Stack gap="xs">
            {faqData.map((faq, index) => (
              <details key={index} className={classes.faqItem}>
                <summary className={classes.faqSummary}>
                  <span className={classes.faqChevron}>▶</span>
                  <span className={classes.faqQuestion}>{faq.question}</span>
                </summary>
                <div className={classes.faqContent}>
                  <Text size="md" c="gray.7">
                    {faq.answer.split('\n\n').map((paragraph, index) => (
                      <React.Fragment key={index}>
                        {paragraph.split('\n').map((line, lineIndex) => (
                          <React.Fragment key={lineIndex}>
                            <span dangerouslySetInnerHTML={{ __html: line }} />
                            {lineIndex < paragraph.split('\n').length - 1 && <br />}
                          </React.Fragment>
                        ))}
                        {index < faq.answer.split('\n\n').length - 1 && (
                          <>
                            <br />
                            <br />
                          </>
                        )}
                      </React.Fragment>
                    ))}
                  </Text>
                </div>
              </details>
            ))}
          </Stack>
        </Box>
      </Stack>
    </Container>
  );
}
