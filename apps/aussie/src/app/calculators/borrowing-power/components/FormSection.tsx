import React from 'react';
import { Stack, Title } from '@mantine/core';

import { COLORS } from '../constants';

import classes from './FormSection.module.css';

interface FormSectionProps {
  title: string;
  children: React.ReactNode;
}

export function FormSection({ title, children }: FormSectionProps) {
  return (
    <Stack gap="lg">
      <Title order={3} size="h3" c={COLORS.GRAY_8} className={classes.sectionTitle}>
        {title}
      </Title>
      {children}
    </Stack>
  );
}
