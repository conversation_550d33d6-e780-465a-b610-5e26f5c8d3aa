'use client';

import React, { useState } from 'react';
import {
  Alert,
  Box,
  Grid,
  Group,
  NumberInput,
  Paper,
  Radio,
  Select,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useForm } from '@mantine/form';

import { ButtonWithTracking, LinkButtonWithTracking } from '@gp/ui/components';
import { sendEvent } from '@gp/util/interaction-studio';

import { AddButton, ProgressBar, RemoveButton } from '../../shared';
import { formatCurrency } from '../../shared/utils';
import {
  BUTTON_TEXT,
  COMMON_TEXT,
  DEFAULT_VALUES,
  RESULT_TEXT,
  VALIDATION_MESSAGES,
} from '../constants';
import { BorrowingPowerDisplay } from './BorrowingPowerDisplay';

import classes from './BorrowingPowerCalculator.module.css';

interface FormValues {
  securityValue: number;
  loanAmount: number;
  numberOfApplicants: number;
  noOfDependents: number;
  postcode: string;
  state: string;
  monthlyExpense: number;
  incomes: number[];
  liabilities: any[];
}

interface BorrowingPowerResult {
  minBorrowingPower?: {
    amount: number;
    funderId: string;
    productId: string;
  };
  maxBorrowingPower?: {
    amount: number;
    funderId: string;
    productId: string;
  };
  error?: string;
}

export function BorrowingPowerCalculator() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<BorrowingPowerResult | null>(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);

  // Liability states
  const [creditCards, setCreditCards] = useState([{ limit: 0 }]);
  const [homeLoans, setHomeLoans] = useState([{ monthlyRepayment: 0 }]);
  const [personalLoans, setPersonalLoans] = useState([{ monthlyRepayment: 0 }]);
  const [otherLiabilities, setOtherLiabilities] = useState([{ monthlyRepayment: 0 }]);

  const form = useForm<FormValues>({
    initialValues: {
      securityValue: 800000,
      loanAmount: 550000,
      numberOfApplicants: 1,
      noOfDependents: 0,
      postcode: '2000',
      state: 'NSW',
      monthlyExpense: 0,
      incomes: [100000],
      liabilities: [],
    },
    validate: {
      loanAmount: (value) => {
        if (!value || value <= 0) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_REQUIRED;
        }
        if (value < 10000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        if (value > 100000000) {
          return VALIDATION_MESSAGES.LOAN_AMOUNT_MIN_MAX;
        }
        return null;
      },
      incomes: (value) => {
        if (!value || value.length === 0) {
          return 'Your total annual income must be between $10,000 and $100,000,000';
        }

        for (let i = 0; i < value.length; i++) {
          const income = value[i];

          // Primary applicant (index 0) must have income between 10,000 and 100,000,000
          if (i === 0) {
            if (!income || income <= 0) {
              return 'Your total annual income must be between $10,000 and $100,000,000';
            }
            if (income < 10000 || income > 100000000) {
              return 'Your total annual income must be between $10,000 and $100,000,000';
            }
          }

          // Partner's income (index 1) can be 0, but if provided, must be within range
          if (i === 1) {
            if (income < 0) {
              return 'Your total annual income must be between $10,000 and $100,000,000';
            }
            if (income > 0 && (income < 10000 || income > 100000000)) {
              return 'Your total annual income must be between $10,000 and $100,000,000';
            }
          }
        }
        return null;
      },
    },
  });

  // Liability management functions
  const addCreditCard = () => setCreditCards([...creditCards, { limit: 0 }]);
  const removeCreditCard = (index: number) => {
    if (creditCards.length > 1) {
      setCreditCards(creditCards.filter((_, i) => i !== index));
    }
  };
  const updateCreditCard = (index: number, field: string, value: number) => {
    const updated = creditCards.map((card, i) =>
      i === index ? { ...card, [field]: value } : card
    );
    setCreditCards(updated);
  };

  const addHomeLoan = () => setHomeLoans([...homeLoans, { monthlyRepayment: 0 }]);
  const removeHomeLoan = (index: number) => {
    if (homeLoans.length > 1) {
      setHomeLoans(homeLoans.filter((_, i) => i !== index));
    }
  };
  const updateHomeLoan = (index: number, field: string, value: number) => {
    const updated = homeLoans.map((loan, i) => (i === index ? { ...loan, [field]: value } : loan));
    setHomeLoans(updated);
  };

  const addPersonalLoan = () => setPersonalLoans([...personalLoans, { monthlyRepayment: 0 }]);
  const removePersonalLoan = (index: number) => {
    if (personalLoans.length > 1) {
      setPersonalLoans(personalLoans.filter((_, i) => i !== index));
    }
  };
  const updatePersonalLoan = (index: number, field: string, value: number) => {
    const updated = personalLoans.map((loan, i) =>
      i === index ? { ...loan, [field]: value } : loan
    );
    setPersonalLoans(updated);
  };

  const addOtherLiability = () =>
    setOtherLiabilities([...otherLiabilities, { monthlyRepayment: 0 }]);
  const removeOtherLiability = (index: number) => {
    if (otherLiabilities.length > 1) {
      setOtherLiabilities(otherLiabilities.filter((_, i) => i !== index));
    }
  };
  const updateOtherLiability = (index: number, field: string, value: number) => {
    const updated = otherLiabilities.map((liability, i) =>
      i === index ? { ...liability, [field]: value } : liability
    );
    setOtherLiabilities(updated);
  };

  const handleSubmit = async (values: FormValues) => {
    setLoading(true);
    setResult(null);
    setHasSubmitted(true);

    try {
      // Build liabilities array from all liability types
      const allLiabilities = [
        ...creditCards.map((card) => ({
          category: 'CREDIT_CARD',
          creditLimit: card.limit,
        })),
        ...homeLoans.map((loan) => ({
          category: 'OTHER',
          creditLimit: 1,
          monthlyRepayment: loan.monthlyRepayment,
          remainingTerms: 1,
        })),
        ...personalLoans.map((loan) => ({
          category: 'PERSONAL_LOAN',
          creditLimit: 1,
          monthlyRepayment: loan.monthlyRepayment,
          remainingTerms: 1,
        })),
        ...otherLiabilities.map((liability) => ({
          category: 'OTHER',
          creditLimit: 1,
          monthlyRepayment: liability.monthlyRepayment,
          remainingTerms: 1,
        })),
      ];

      // Filter incomes based on number of applicants
      const relevantIncomes =
        values.numberOfApplicants === 1
          ? [values.incomes[0]]
          : [values.incomes[0], values.incomes[1] || 0];

      // Create dependantAges structure based on number of applicants and dependents
      let dependantAges: number[][];

      if (values.numberOfApplicants === 1) {
        // One applicant
        if (values.noOfDependents === 0) {
          dependantAges = [[]];
        } else {
          dependantAges = [Array(values.noOfDependents).fill(10)];
        }
      } else {
        // Two applicants
        if (values.noOfDependents === 0) {
          dependantAges = [[], []];
        } else {
          dependantAges = [Array(values.noOfDependents).fill(10), []];
        }
      }
      const payload = {
        data: {
          securityValue: values.securityValue,
          loanAmount: values.loanAmount,
          numberOfApplicants: values.numberOfApplicants,
          dependantAges: dependantAges,
          postcode: values.postcode,
          state: values.state,
          monthlyExpense: values.monthlyExpense,
          incomes: relevantIncomes,
          liabilities: allLiabilities,
        },
      };

      // Get the appropriate Lendi API URL based on environment
      const getLendiApiUrl = () => {
        const appEnvironment =
          process.env.NEXT_PUBLIC_APP_ENVIRONMENT || process.env.NX_PUBLIC_APP_ENVIRONMENT;

        switch (appEnvironment) {
          case 'development':
            return 'https://api.lendi-dev.net/v2/serviceability/ctp-mbp/';
          case 'staging':
            return 'https://api.lendi-stg.net/v2/serviceability/ctp-mbp/';
          case 'production':
          default:
            return 'https://api.lendi.com.au/v2/serviceability/ctp-mbp/';
        }
      };

      // Call the Lendi API directly
      const response = await fetch(getLendiApiUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Lendi API error! Status: ${response.status}`, errorText);
        throw new Error(`Borrowing power calculation failed with status ${response.status}`);
      }

      const data = await response.json();

      // Process the Lendi API response
      if (data.data && data.data.minBorrowingPower && data.data.maxBorrowingPower) {
        setResult({
          minBorrowingPower: data.data.minBorrowingPower,
          maxBorrowingPower: data.data.maxBorrowingPower,
        });
      } else {
        console.error('Invalid Lendi API response structure:', data);
        setResult({
          error: 'Invalid response format from borrowing power API',
        });
      }
    } catch (error) {
      console.error('Error calling Lendi borrowing power API:', error);
      setResult({
        error: 'Unable to calculate borrowing power. Please try again later.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApplicantChange = (value: string | null) => {
    const numApplicants = parseInt(value || '1');
    form.setFieldValue('numberOfApplicants', numApplicants);

    // Adjust incomes array based on number of applicants
    const currentIncomes = form.values.incomes;

    if (numApplicants === 1) {
      form.setFieldValue('incomes', [currentIncomes[0] || 100000]);
    } else {
      form.setFieldValue('incomes', [currentIncomes[0] || 100000, currentIncomes[1] || 0]);
    }
  };

  return (
    <Stack gap="xl">
      <style>
        {`
          @keyframes pulse {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.4;
            }
          }
        `}
      </style>

      <Box ta="center" py="xl">
        <Title order={1} mb="md" c="grape.8" className={classes.heroTitle}>
          Home Loan Borrowing Power Calculator
        </Title>
        <Text size="lg" c="gray.6" className={classes.heroText}>
          Wondering how much you can borrow for a home loan? Start your property journey by
          calculating your borrowing power estimate in a few simple steps
        </Text>
      </Box>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Grid>
          <Grid.Col span={{ base: 12, md: 5 }}>
            <Stack gap="lg">
              {/* Step 1: Your details */}
              <Stack gap="xl">
                <Title order={3} c="grape.8" className={classes.sectionTitle}>
                  Your details
                </Title>

                {/* Who is this loan for? */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    Who is this loan for?
                  </Text>
                  <Radio.Group
                    value={form.values.numberOfApplicants.toString()}
                    onChange={handleApplicantChange}
                  >
                    <Stack gap="sm">
                      <Radio value="1" label="Me" classNames={{ radio: classes.radio }} />
                      <Radio value="2" label="Me plus one" classNames={{ radio: classes.radio }} />
                    </Stack>
                  </Radio.Group>
                </Stack>

                {/* How many dependants */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    How many dependants do you financially support?
                  </Text>
                  <Select
                    placeholder="0"
                    data={[
                      { value: '0', label: '0' },
                      { value: '1', label: '1' },
                      { value: '2', label: '2' },
                      { value: '3', label: '3' },
                      { value: '4', label: '4' },
                      { value: '5', label: '5+' },
                    ]}
                    value={form.values.noOfDependents.toString()}
                    onChange={(value) => {
                      const numDependants = Number(value) || 0;
                      form.setFieldValue('noOfDependents', numDependants);
                    }}
                    classNames={{ input: classes.input }}
                  />
                </Stack>
              </Stack>

              {/* Step 2: Your income */}
              <Stack gap="xl">
                <Title order={3} c="grape.8" className={classes.sectionTitle}>
                  Your income
                </Title>

                {/* What is your total annual income before tax? */}
                <Stack gap="md">
                  <Text fw={600} size="md" c="gray.8">
                    What is your total annual income before tax?
                  </Text>
                  <NumberInput
                    placeholder="100,000"
                    thousandSeparator=","
                    hideControls
                    required
                    value={form.values.incomes[0] || 0}
                    allowNegative={false}
                    error={form.errors.incomes}
                    onChange={(value) => {
                      const newIncomes = [...form.values.incomes];
                      if (value === '') {
                        newIncomes[0] = 0;
                      } else if (typeof value === 'number') {
                        newIncomes[0] = value;
                      }
                      form.setFieldValue('incomes', newIncomes);
                      // Trigger validation immediately
                      form.validateField('incomes');
                    }}
                    onBlur={() => {
                      // Format the value on blur
                      if (form.values.incomes[0]) {
                        formatCurrency(form.values.incomes[0]);
                        // The NumberInput will automatically format with thousandSeparator
                      }
                    }}
                    classNames={{ input: classes.input }}
                  />
                  <Text size="sm" c="gray.6">
                    Salary excluding super
                  </Text>
                </Stack>

                {/* Show second income field if "Me plus one" is selected */}
                {form.values.numberOfApplicants === 2 && (
                  <Stack gap="md">
                    <Text fw={600} size="md" c="gray.8">
                      What is your partner&apos;s total annual income before tax?
                    </Text>
                    <NumberInput
                      placeholder="100,000"
                      thousandSeparator=","
                      defaultValue={DEFAULT_VALUES.INCOME}
                      hideControls
                      value={form.values.incomes[1] || 0}
                      allowNegative={false}
                      error={form.errors.incomes}
                      onChange={(value) => {
                        const newIncomes = [...form.values.incomes];
                        if (value === '') {
                          newIncomes[1] = 0;
                        } else if (typeof value === 'number') {
                          newIncomes[1] = value;
                        }
                        form.setFieldValue('incomes', newIncomes);
                        // Trigger validation immediately
                        form.validateField('incomes');
                      }}
                      onBlur={() => {
                        // Format the value on blur
                        if (form.values.incomes[1]) {
                          formatCurrency(form.values.incomes[1]);
                          // The NumberInput will automatically format with thousandSeparator
                        }
                      }}
                      classNames={{ input: classes.input }}
                    />
                    <Text size="sm" c="gray.6">
                      Salary excluding super
                    </Text>
                  </Stack>
                )}
              </Stack>

              {/* Step 3: Your total liabilities */}
              <Stack gap="xl">
                <Title order={3} c="grape.8" className={classes.sectionTitle}>
                  Your total liabilities
                </Title>

                {/* Credit or store card */}
                <Stack gap="lg">
                  <Text fw={600} size="md" c="gray.8">
                    Credit or store card
                  </Text>
                  {creditCards.map((card, index) => (
                    <Box key={index}>
                      <Stack gap="xs">
                        <Text size="sm" c="gray.7">
                          What is the credit limit on this card?
                        </Text>
                        <Group gap="sm" align="center">
                          <NumberInput
                            placeholder="0"
                            thousandSeparator=","
                            hideControls
                            value={card.limit}
                            allowNegative={false}
                            onChange={(value) => {
                              if (value === '') {
                                updateCreditCard(index, 'limit', 0);
                              } else if (typeof value === 'number') {
                                updateCreditCard(index, 'limit', value);
                              }
                            }}
                            onBlur={() => {
                              // Format the value on blur
                              if (card.limit) {
                                formatCurrency(card.limit);
                                // The NumberInput will automatically format with thousandSeparator
                              }
                            }}
                            classNames={{ input: classes.input }}
                            className={classes.flexInput}
                          />
                          {index > 0 && <RemoveButton onClick={() => removeCreditCard(index)} />}
                        </Group>
                        <Text size="xs" c="gray.6">
                          Enter the limit of the card.
                        </Text>
                      </Stack>
                    </Box>
                  ))}
                  <AddButton onClick={addCreditCard}>Add credit or store card</AddButton>
                </Stack>

                {/* Home loan */}
                <Stack gap="lg">
                  <Text fw={600} size="md" c="gray.8">
                    Home loan
                  </Text>
                  {homeLoans.map((loan, index) => (
                    <Box key={index}>
                      <Stack gap="xs">
                        <Text size="sm" c="gray.7">
                          What is your monthly repayment?
                        </Text>
                        <Group gap="sm" align="center">
                          <NumberInput
                            placeholder="0"
                            thousandSeparator=","
                            hideControls
                            value={loan.monthlyRepayment}
                            allowNegative={false}
                            onChange={(value) => {
                              if (value === '') {
                                updateHomeLoan(index, 'monthlyRepayment', 0);
                              } else if (typeof value === 'number') {
                                updateHomeLoan(index, 'monthlyRepayment', value);
                              }
                            }}
                            onBlur={() => {
                              // Format the value on blur
                              if (loan.monthlyRepayment) {
                                formatCurrency(loan.monthlyRepayment);
                                // The NumberInput will automatically format with thousandSeparator
                              }
                            }}
                            classNames={{ input: classes.input }}
                            className={classes.flexInput}
                          />
                          {index > 0 && <RemoveButton onClick={() => removeHomeLoan(index)} />}
                        </Group>
                        <Text size="xs" c="gray.6">
                          Enter the amount you pay monthly for an existing home loan.
                        </Text>
                      </Stack>
                    </Box>
                  ))}
                  <AddButton onClick={addHomeLoan}>Add home loan</AddButton>
                </Stack>

                {/* Personal loan */}
                <Stack gap="lg">
                  <Text fw={600} size="md" c="gray.8">
                    Personal loan
                  </Text>
                  {personalLoans.map((loan, index) => (
                    <Box key={index}>
                      <Stack gap="xs">
                        <Text size="sm" c="gray.7">
                          What is your monthly repayment?
                        </Text>
                        <Group gap="sm" align="center">
                          <NumberInput
                            placeholder="0"
                            thousandSeparator=","
                            hideControls
                            value={loan.monthlyRepayment}
                            allowNegative={false}
                            onChange={(value) => {
                              if (value === '') {
                                updatePersonalLoan(index, 'monthlyRepayment', 0);
                              } else if (typeof value === 'number') {
                                updatePersonalLoan(index, 'monthlyRepayment', value);
                              }
                            }}
                            onBlur={() => {
                              // Format the value on blur
                              if (loan.monthlyRepayment) {
                                formatCurrency(loan.monthlyRepayment);
                                // The NumberInput will automatically format with thousandSeparator
                              }
                            }}
                            classNames={{ input: classes.input }}
                            className={classes.flexInput}
                          />
                          {index > 0 && <RemoveButton onClick={() => removePersonalLoan(index)} />}
                        </Group>
                        <Text size="xs" c="gray.6">
                          Include amount you pay monthly towards personal loans and any hire
                          purchases.
                        </Text>
                      </Stack>
                    </Box>
                  ))}
                  <AddButton onClick={addPersonalLoan}>Add personal loan</AddButton>
                </Stack>

                {/* Other liabilities */}
                <Stack gap="lg">
                  <Text fw={600} size="md" c="gray.8">
                    Other liabilities
                  </Text>
                  {otherLiabilities.map((liability, index) => (
                    <Box key={index}>
                      <Stack gap="xs">
                        <Text size="sm" c="gray.7">
                          What is your monthly repayment?
                        </Text>
                        <Group gap="sm" align="center">
                          <NumberInput
                            placeholder="0"
                            thousandSeparator=","
                            hideControls
                            value={liability.monthlyRepayment}
                            allowNegative={false}
                            onChange={(value) => {
                              if (value === '') {
                                updateOtherLiability(index, 'monthlyRepayment', 0);
                              } else if (typeof value === 'number') {
                                updateOtherLiability(index, 'monthlyRepayment', value);
                              }
                            }}
                            onBlur={() => {
                              // Format the value on blur
                              if (liability.monthlyRepayment) {
                                formatCurrency(liability.monthlyRepayment);
                                // The NumberInput will automatically format with thousandSeparator
                              }
                            }}
                            classNames={{ input: classes.input }}
                            className={classes.flexInput}
                          />
                          {index > 0 && (
                            <RemoveButton onClick={() => removeOtherLiability(index)} />
                          )}
                        </Group>
                        <Text size="xs" c="gray.6">
                          Total amount you pay monthly for all borrowers for any other liabilities
                          such as tax debt, or other lines of credit
                        </Text>
                      </Stack>
                    </Box>
                  ))}
                  <AddButton onClick={addOtherLiability}>Add additional liability</AddButton>
                </Stack>

                {/* Calculate Button */}
                <ButtonWithTracking
                  label={
                    loading
                      ? BUTTON_TEXT.LOADING
                      : hasSubmitted
                      ? BUTTON_TEXT.UPDATE
                      : BUTTON_TEXT.CALCULATE
                  }
                  type="submit"
                  loading={loading}
                  size="lg"
                  fullWidth
                  mt="lg"
                  bg="grape.8"
                  c="white"
                  position="Borrowing Power Calculator"
                  purpose="calculate_borrowing_power"
                  onClick={() =>
                    sendEvent('Borrowing Power Calculated', {
                      calculator_type: 'borrowing_power',
                      number_of_applicants: form.values.numberOfApplicants,
                    })
                  }
                >
                  {loading
                    ? BUTTON_TEXT.LOADING
                    : hasSubmitted
                    ? BUTTON_TEXT.UPDATE
                    : BUTTON_TEXT.CALCULATE}
                </ButtonWithTracking>
              </Stack>
            </Stack>
          </Grid.Col>

          {/* Divider - vertical on desktop, horizontal on mobile */}
          <Grid.Col span={1} visibleFrom="md" className={classes.gridDivider}>
            <Box w="1px" className={classes.verticalDivider} />
          </Grid.Col>

          {/* Mobile divider */}
          <Grid.Col span={12} hiddenFrom="md">
            <Box w="100%" h="1px" my="xl" className={classes.dottedDivider} />
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 5 }}>
            <Box className={classes.sticky}>
              {result?.error && (
                <Alert color="red" mb="lg">
                  {result.error}
                </Alert>
              )}

              <Stack gap={0}>
                {/* White Results Section */}
                <Paper p="lg" bg="white" className={classes.resultCardTop}>
                  <Stack gap="md" align="center">
                    <Text size="xl" fw={600} c="gray.8" ta="center" className={classes.resultTitle}>
                      {RESULT_TEXT.BORROWING_POWER_TITLE}
                    </Text>

                    <BorrowingPowerDisplay
                      minAmount={result?.minBorrowingPower?.amount || 0}
                      maxAmount={result?.maxBorrowingPower?.amount || 0}
                      loading={loading}
                    />

                    <ProgressBar
                      minAmount={result?.minBorrowingPower?.amount || 0}
                      maxAmount={result?.maxBorrowingPower?.amount || 0}
                    />

                    <Box w="100%" h="1px" bg="gray.3" mt="md" className={classes.resultDotted} />

                    <Text
                      size="sm"
                      c="gray.7"
                      ta="center"
                      mt="sm"
                      className={classes.disclaimerText}
                    >
                      {RESULT_TEXT.LENDER_DISCLAIMER}
                      <br />
                      {RESULT_TEXT.BROKER_DISCLAIMER}
                    </Text>
                  </Stack>
                </Paper>

                {/* Purple Talk to Broker Section */}
                <Paper p="xl" bg="grape.8" className={classes.brokerCard}>
                  <Stack gap="md" align="center">
                    <Text size="xl" fw={600} c="white" ta="center" className={classes.brokerTitle}>
                      {COMMON_TEXT.TALK_TO_BROKER}
                    </Text>

                    <Text size="md" c="white" ta="center" className={classes.brokerText}>
                      {RESULT_TEXT.BROKER_DESCRIPTION}
                    </Text>

                    <LinkButtonWithTracking
                      size="lg"
                      bg="yellow.5"
                      c="grape.8"
                      fw={600}
                      mt="sm"
                      radius="xl"
                      w="280px"
                      href="/book-appointment"
                      position="Borrowing Power Calculator"
                      purpose="book_appointment"
                      label={COMMON_TEXT.BOOK_APPOINTMENT}
                      onClick={() =>
                        sendEvent('CTA Clicked', {
                          cta_text: COMMON_TEXT.BOOK_APPOINTMENT,
                          cta_position: 'Borrowing Power Calculator',
                        })
                      }
                    >
                      {COMMON_TEXT.BOOK_APPOINTMENT}
                    </LinkButtonWithTracking>
                  </Stack>
                </Paper>
              </Stack>
            </Box>
          </Grid.Col>
        </Grid>
      </form>
    </Stack>
  );
}
