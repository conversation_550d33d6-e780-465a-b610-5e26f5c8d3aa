import React from 'react';
import { Group, NumberInput, Stack, Text } from '@mantine/core';

import { AddButton, RemoveButton } from '../../shared';
import { formatCurrency } from '../../shared/utils';
import { COLORS } from '../constants';

import classes from './LiabilitySection.module.css';

interface LiabilityItem {
  limit?: number;
  monthlyRepayment?: number;
}

interface LiabilitySectionProps {
  title: string;
  items: LiabilityItem[];
  onAdd: () => void;
  onRemove: (index: number) => void;
  onUpdate: (index: number, field: string, value: number) => void;
  addButtonText: string;
  inputType: 'limit' | 'repayment';
  inputLabel: string;
}

export function LiabilitySection({
  title,
  items,
  onAdd,
  onRemove,
  onUpdate,
  addButtonText,
  inputType,
  inputLabel,
}: LiabilitySectionProps) {
  return (
    <Stack gap="md">
      <Text size="md" fw={600} c={COLORS.GRAY_8} className={classes.sectionTitle}>
        {title}
      </Text>

      {items.map((item, index) => (
        <Group key={index} align="center">
          <NumberInput
            label={index === 0 ? inputLabel : ''}
            placeholder="0"
            value={inputType === 'limit' ? item.limit || 0 : item.monthlyRepayment || 0}
            allowNegative={false}
            onChange={(value) => {
              if (value === '') {
                onUpdate(index, inputType, 0);
              } else if (typeof value === 'number') {
                onUpdate(index, inputType, value);
              }
            }}
            onBlur={() => {
              // Format the value on blur
              const currentValue =
                inputType === 'limit' ? item.limit || 0 : item.monthlyRepayment || 0;
              if (currentValue) {
                formatCurrency(currentValue);
                // The NumberInput will automatically format with thousandSeparator and prefix
              }
            }}
            thousandSeparator=","
            prefix="$"
            min={0}
            hideControls
            flex={1}
            classNames={{ input: classes.input }}
          />
          {items.length > 1 && <RemoveButton onClick={() => onRemove(index)} />}
        </Group>
      ))}

      <AddButton onClick={onAdd}>{addButtonText}</AddButton>
    </Stack>
  );
}
