# Calculators

This directory contains all calculator applications for the Aussie website. The calculators are organized with shared components and constants to promote code reuse and maintainability.

## Structure

```
calculators/
├── shared/                    # Shared components and constants
│   ├── constants.ts          # Common constants (colors, fonts, options)
│   ├── utils.ts              # Shared utility functions
│   ├── AddButton.tsx         # Reusable add button component
│   ├── RemoveButton.tsx      # Reusable remove button component
│   ├── CalculatorDisclaimer.tsx # Reusable disclaimer component
│   ├── ProgressBar.tsx       # Reusable progress bar component
│   ├── ResultDisplay.tsx     # Reusable result display component
│   ├── FAQSection.tsx        # Reusable FAQ section component
│   ├── OtherCalculators.tsx  # Reusable other calculators component
│   ├── LegalDisclaimer.tsx   # Reusable legal disclaimer component
│   └── index.ts              # Export all shared components
├── borrowing-power/          # Borrowing power calculator
├── refinance-home-loan/      # Refinance calculator
├── repayment-calculator/     # Repayment calculator
└── README.md                 # This file
```

## Shared Components

### Constants (`shared/constants.ts`)

Common constants used across all calculators:

- **Form Options**: `LOAN_TERM_OPTIONS`, `STATE_OPTIONS`, `APPLICANT_OPTIONS`, etc.
- **Styling**: `COLORS`, `FONTS`, `ANIMATIONS`
- **Text**: `COMMON_TEXT` for common phrases like "Calculate", "Book appointment"
- **API Endpoints**: `API_ENDPOINTS` for calculator-specific endpoints

### Components

#### `AddButton`
Reusable button for adding items (e.g., liabilities, extra repayments).

```tsx
import { AddButton } from '../shared';

<AddButton onClick={handleAdd}>Add another liability</AddButton>
```

#### `RemoveButton`
Reusable circular remove button.

```tsx
import { RemoveButton } from '../shared';

<RemoveButton onClick={handleRemove} />
```

#### `CalculatorDisclaimer`
Collapsible disclaimer section with customizable title and content.

```tsx
import { CalculatorDisclaimer } from '../shared';

<CalculatorDisclaimer title="Calculator assumptions">
  <Text>Your disclaimer content here...</Text>
</CalculatorDisclaimer>
```

#### `ProgressBar`
Dynamic progress bar with customizable range and labels.

```tsx
import { ProgressBar } from '../shared';

<ProgressBar 
  minAmount={100000} 
  maxAmount={500000}
  minLabel="$100K"
  maxLabel="$500K"
/>
```

#### `ResultDisplay`
Complete result display with title, amounts, progress bar, and broker section.

```tsx
import { ResultDisplay } from '../shared';

<ResultDisplay
  title="Your borrowing power is"
  minAmount={300000}
  maxAmount={450000}
  loading={false}
  error={error}
  brokerSection={{
    title: "Talk to a broker now",
    description: "Get personalized advice",
    buttonText: "Book appointment",
    buttonHref: "/book-appointment/"
  }}
/>
```

#### `FAQSection`
Interactive FAQ section with collapsible items.

```tsx
import { FAQSection } from '../shared';

const faqs = [
  {
    question: "What is borrowing power?",
    answer: "Borrowing power is the maximum amount..."
  }
];

<FAQSection title="Borrowing Power FAQs" faqs={faqs} />
```

#### `OtherCalculators`
Grid of other calculator links with icons.

```tsx
import { OtherCalculators, CalculatorType } from '../shared';

<OtherCalculators
  calculators={[
    CalculatorType.SAVINGS,
    CalculatorType.STAMP_DUTY,
    CalculatorType.EXTRA_REPAYMENTS,
  ]}
  title="Other calculators"
/>
```

#### `LegalDisclaimer`
Comprehensive legal disclaimer section.

```tsx
import { LegalDisclaimer } from '../shared';

<LegalDisclaimer />
```

### Utilities (`shared/utils.ts`)

Common utility functions:

- `formatCurrency(amount)` - Format number as currency
- `formatRangeAmount(amount)` - Format for progress bar ranges
- `calculateMonthlyRepayment(principal, rate, years)` - Calculate loan repayments
- `calculateTotalInterest(principal, monthlyRepayment, years)` - Calculate total interest
- `calculateTotalRepayments(monthlyRepayment, years)` - Calculate total repayments

## Calculator Structure

Each calculator follows this structure:

```
calculator-name/
├── components/           # Calculator-specific components
│   ├── CalculatorName.tsx    # Main calculator component
│   ├── CalculatorDisplay.tsx # Result display component
│   └── CalculatorFAQs.tsx    # Calculator-specific FAQs
├── constants/           # Calculator-specific constants
│   └── index.ts        # Imports shared constants + specific ones
├── utils/              # Calculator-specific utilities
│   └── index.ts        # Calculator-specific utility functions
└── page.tsx            # Calculator page component
```

## Best Practices

### 1. Use Shared Components
Always use shared components when possible instead of creating duplicates.

```tsx
// ✅ Good - Use shared component
import { AddButton } from '../shared';

// ❌ Bad - Don't create duplicate
import { AddButton } from './AddButton';
```

### 2. Import from Shared Index
Import multiple shared components from the index file.

```tsx
// ✅ Good - Import from shared index
import { AddButton, RemoveButton, ProgressBar } from '../shared';

// ❌ Bad - Multiple imports
import { AddButton } from '../shared/AddButton';
import { RemoveButton } from '../shared/RemoveButton';
```

### 3. Use Shared Constants
Import shared constants and only define calculator-specific ones.

```tsx
// ✅ Good - Import shared, define specific
export {
  COLORS,
  FONTS,
  ANIMATIONS,
  LOAN_TERM_OPTIONS,
} from '../../shared/constants';

export const CALCULATOR_SPECIFIC_CONSTANT = 'value';
```

### 4. Consistent Naming
Use consistent naming conventions:

- Components: `PascalCase` (e.g., `BorrowingPowerCalculator`)
- Constants: `UPPER_SNAKE_CASE` (e.g., `LOAN_TERM_OPTIONS`)
- Files: `kebab-case` (e.g., `borrowing-power-calculator.tsx`)

### 5. Type Safety
Always define proper TypeScript interfaces for props and data.

```tsx
interface CalculatorProps {
  initialValue?: number;
  onCalculate: (result: CalculationResult) => void;
}
```

### 6. Error Handling
Implement proper error handling for API calls and user inputs.

```tsx
try {
  const result = await calculateBorrowingPower(formData);
  setResult(result);
} catch (error) {
  setError('Unable to calculate. Please try again.');
}
```

### 7. Accessibility
Ensure all components are accessible:

- Use semantic HTML elements
- Provide proper ARIA labels
- Support keyboard navigation
- Include proper focus management

### 8. Performance
Optimize for performance:

- Use React.memo for expensive components
- Implement proper loading states
- Debounce user inputs where appropriate
- Lazy load non-critical components

## Adding a New Calculator

1. **Create the calculator directory**:
   ```bash
   mkdir calculators/new-calculator
   mkdir calculators/new-calculator/components
   mkdir calculators/new-calculator/constants
   mkdir calculators/new-calculator/utils
   ```

2. **Set up constants**:
   ```tsx
   // calculators/new-calculator/constants/index.ts
   export {
     COLORS,
     FONTS,
     ANIMATIONS,
     COMMON_TEXT,
   } from '../../shared/constants';

   export const NEW_CALCULATOR_CONSTANTS = {
     // Calculator-specific constants
   };
   ```

3. **Create the main calculator component**:
   ```tsx
   // calculators/new-calculator/components/NewCalculator.tsx
   import { AddButton, ProgressBar } from '../../shared';
   ```

4. **Create the page**:
   ```tsx
   // calculators/new-calculator/page.tsx
   import { OtherCalculators, CalculatorType } from '../shared';
   ```

5. **Add to shared constants**:
   ```tsx
   // calculators/shared/constants.ts
   export enum CalculatorType {
     // ... existing types
     NEW_CALCULATOR = 'new_calculator',
   }
   ```

## Testing

Each calculator should have:

- Unit tests for utility functions
- Integration tests for form submission
- Accessibility tests for keyboard navigation
- Visual regression tests for UI components

## Deployment

Calculators are deployed as part of the main Aussie application. Each calculator is a Next.js page that follows the same deployment process as the rest of the application. 