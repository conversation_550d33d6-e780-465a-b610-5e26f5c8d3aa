import { Container, Stack, Text, Title } from '@mantine/core';
import { Brand } from '@lendi/lala-utils';

import { FunnelCTAs, FunnelFAQs } from '@gp/feature/funnels';
import { PageTracking, ProductReviewWidget } from '@gp/ui/components';

export const dynamic = 'force-dynamic';

export default async function ApplyNow() {
  return (
    <PageTracking name="Basics page" category="FUNNELS">
      <Stack gap={48} align="center" flex="1">
        <Container maw={800} pt={48} px={16}>
          <Stack align="center">
            <Title order={2} fw={600} c="primary" ta="center">
              Unlock new possibilities with Aussie
            </Title>
            <Text size="lg">
              For over 30 years Aussie has helped over a million Australians realise their property
              dreams and goals. Now, it&apos;s your turn.
            </Text>
            <Text size="lg">
              <strong>Let&apos;s fast-track your dream home journey in just 2 minutes.</strong>{' '}
              Create your account now to unlock personalised options and let us make things easy.
            </Text>
          </Stack>
        </Container>
        <Container maw={570} px={16}>
          <FunnelCTAs />
        </Container>
        <Container px={16}>
          <Stack gap="xs" align="center">
            <ProductReviewWidget brand={Brand.Aussie} />
          </Stack>
        </Container>
        <Container fluid bg="gray.0" py={48} px={16} flex="1">
          <FunnelFAQs type={Brand.Aussie} />
        </Container>
      </Stack>
    </PageTracking>
  );
}
