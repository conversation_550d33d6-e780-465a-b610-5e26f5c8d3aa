'use client';
import { useSearchParams } from 'next/navigation';

import { FunnelVariant, StaticAuth } from '@gp/feature/auth';

export default function StaticAuthPage() {
  const searchParams = useSearchParams();
  const variantParam = searchParams.get('variant') as FunnelVariant;
  const variant = (variantParam as FunnelVariant) || FunnelVariant.NEW_PURCHASE;
  return <StaticAuth variant={variant} />;
}
