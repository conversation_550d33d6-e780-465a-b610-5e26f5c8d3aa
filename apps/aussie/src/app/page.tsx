import { Stack } from '@mantine/core';
import Link from 'next/link';

export default async function Home() {
  return (
    <Stack gap={16} mih="100vh">
      <Link href="/home-loans/basics">Funnel 1</Link>
      <Link href="/property">Property Hub</Link>
      <Link href="/insights">Insights Article Hub</Link>
      <Link href="/mobile-app/connect-with-me/?brokerid=">Mobile Store Broker Page</Link>
      <Link href="/buyers-agent/confirmation">Buyers Agent Confirmation Page</Link>
      <Link href="/buyers-agent/transaction-complete">Buyers Agent Transaction Complete Page</Link>
      <Link href="/conveyancing/engagement">Conveyancing Funnel</Link>
      <Link href="/calculators/borrowing-power">Borrowing Power Calculator</Link>
      <Link href="/calculators/mortgage-repayments">Mortgage Repayments Calculator</Link>
      <Link href="/calculators/refinance-home-loan">Refinance Home Loan Calculator</Link>
      <Link href="/auth/sign-in">Static Auth</Link>
    </Stack>
  );
}
