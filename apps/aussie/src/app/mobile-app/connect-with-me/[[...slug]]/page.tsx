import { redirect } from 'next/navigation';

import { getSimpleBrokerById } from '@gp/data-access/broker';
import { getSimpleStoreById } from '@gp/data-access/store';
import { ConnectWithBroker, ConnectWithStore } from '@gp/feature/mobile-app';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface PageProps {
  params: { slug?: string[] };
  searchParams: {
    brokerid?: string;
    storeid?: string;
  };
}

export default async function ConnectWithMe({ params, searchParams }: PageProps) {
  const { slug = [] } = params;
  const mobileAppLP = '/lp/mobileapp/';

  if (slug.length > 1 || (slug.length === 1 && slug[0] !== 'welcome')) {
    redirect(mobileAppLP);
  }

  const isConnected = slug[0] === 'welcome';

  const { brokerid, storeid } = searchParams;

  if (brokerid) {
    const broker = await getSimpleBrokerById(brokerid);
    if (!broker) redirect(mobileAppLP);
    return <ConnectWithBroker broker={broker} isConnected={isConnected} />;
  }

  if (storeid) {
    const store = await getSimpleStoreById(storeid);
    if (!store) redirect(mobileAppLP);
    return <ConnectWithStore store={store} isConnected={isConnected} />;
  }

  return redirect(mobileAppLP);
}
