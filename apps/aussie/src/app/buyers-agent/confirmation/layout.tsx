import { Container, SimpleGrid, Stack } from '@mantine/core';
import { Metadata } from 'next';

import { SimpleNavbar } from '@gp/feature/aussie-navbar';

export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh">
      <SimpleNavbar withActions={false} />
      <Container role="main" px="md" py="xxl" fluid>
        <Container size="responsive" maw={945}>
          <SimpleGrid cols={{ base: 1, md: 2 }} spacing={{ base: 24, md: 48 }}>
            {children}
          </SimpleGrid>
        </Container>
      </Container>
    </Stack>
  );
}
