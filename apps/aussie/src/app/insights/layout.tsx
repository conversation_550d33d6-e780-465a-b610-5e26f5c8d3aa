import { Container, Stack } from '@mantine/core';

import Footer from '@gp/feature/aussie-footer';
import { MultiNavbar } from '@gp/feature/aussie-navbar';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Stack gap={0} mih="100vh" bg="white">
      <MultiNavbar />
      <Container pb="xxl" fluid>
        {children}
      </Container>
      <Footer />
    </Stack>
  );
}
