import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { ArticleList, TAG_PAGE } from '@gp/feature/articles-hub';

import { getTagData } from '../../../utils/getTagData';
import { FINANCIAL_TAG_SLUG } from './constants';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const slug = (await params).slug;
  const { title, description } = getTagData(slug, FINANCIAL_TAG_SLUG);

  return {
    title: title || 'The latest Aussie property news',
    description:
      description ||
      'Property tips, rate and home loan updates, and smart guides to help you own your journey.',
  };
}

export const dynamic = 'force-dynamic';

export default async function ArticlesTagPage({ params }: { params: Promise<{ slug: string }> }) {
  const slug = (await params).slug;
  const { tagIdKey } = getTagData(slug, FINANCIAL_TAG_SLUG);

  if (!tagIdKey) {
    notFound();
  }

  return <ArticleList tagIdKey={tagIdKey as TAG_PAGE} />;
}
