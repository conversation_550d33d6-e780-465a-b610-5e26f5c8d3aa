import { TAG_ITEMS, TAG_PAGE } from '@gp/feature/articles-hub';

export const getTagData = (slug: string, slugList: Record<string, string>) => {
  const tagIdKey = Object.keys(slugList)[Object.values(slugList).indexOf(slug)];
  return {
    tagIdKey: tagIdKey as TAG_PAGE,
    title: `${TAG_ITEMS[tagIdKey as TAG_PAGE].hero.heading}`,
    description: `${TAG_ITEMS[tagIdKey as TAG_PAGE].hero.subtitle}`,
  };
};
