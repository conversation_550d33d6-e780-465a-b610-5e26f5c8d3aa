// import { CopilotKit } from '@copilotkit/react-core';
import { ColorSchemeScript } from '@mantine/core';
import type { Metadata } from 'next';
import { cookies } from 'next/headers';
import Script from 'next/script';
import { Brand } from '@lendi/lala-utils';

import { AppProviders } from '@gp/shared/app-providers';
// import '@copilotkit/react-ui/styles.css';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { getLDInitFlags, getLDSingleKindContext } from '@gp/shared/server-launchdarkly';
import { aussieTheme } from '@gp/theme/aussie';

import DatadogInit from '../datadog-init';

export const metadata: Metadata = {
  title: 'Search for Properties, Real Estate & Location Insights | Aussie Homes',
  description:
    'Thinking about buying a property to live in or an investment property? Aussie Homes lets you view property insights and trends, access suburb, state and LGA profiles to help you make informed property and investment decisions.',
};

const orgJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Corporation',
  '@id': process.env.BASE_URL,
  logo: 'https://www.aussie.com.au/assets/favicon.ico',
  legalName: 'AHL Investments Pty Ltd',
  name: 'Aussie Home Loans',
  sameAs: [
    'https://www.linkedin.com/company/aussie/',
    'https://twitter.com/aussie',
    'https://www.facebook.com/AussieHomeLoans',
    'https://www.instagram.com/aussiehomeloans/',
    'https://www.youtube.com/channel/UCIUF_t04qDGNQ5gOcApBbXA',
  ],
  url: process.env.BASE_URL,
  address: {
    '@type': 'PostalAddress',
    streetAddress: 'L28, 225 George Street',
    addressLocality: 'Sydney',
    addressRegion: 'NSW',
    postalCode: '2000',
    addressCountry: 'AU',
  },
  contactPoint: [
    {
      '@type': 'ContactPoint',
      telephone: '13 13 33',
      contactType: 'customer service',
      areaServed: 'AU',
      availableLanguage: ['English'],
    },
  ],
};

const ENV_VARS = {
  GP_API_BASE_URL: process.env['API_BASE_URL'] || '',
  GP_BASE_URL: process.env['BASE_URL'] || '',
  GP_AGENTS_SERVICE_API_BASE_URL: process.env['AGENTS_SERVICE_API_BASE_URL'] || '',
  GP_APP_ENVIRONMENT: process.env['APP_ENVIRONMENT'] || '',
  GP_LAUNCHDARKLY_API_KEY: process.env['LAUNCHDARKLY_API_KEY'] || '',
  GP_LAUNCHDARKLY_CLIENT_ID: process.env['LAUNCHDARKLY_CLIENT_ID'] || '',
  GP_LAUNCHDARKLY_ENV_KEY: process.env['LAUNCHDARKLY_ENV_KEY'] || '',
  GP_MAPBOX_ACCESS_TOKEN: process.env['MAPBOX_ACCESS_TOKEN'] || '',
  GP_AMPLITUDE_WRITE_KEY: process.env['AUSSIE_AMPLITUDE_KEY'] || '',
  GP_AREA_BOUNDARY_MAPS_URL: process.env['AREA_BOUNDARY_MAPS_URL'] || '',
  GP_GOOGLE_MAPS_API_KEY: process.env['GOOGLE_MAPS_API_KEY'] || '',
  GP_GOOGLE_MAPS_ID: process.env['GOOGLE_MAPS_ID'] || '',
  GP_RECAPTCHA_CLIENT_KEY: process.env['RECAPTCHA_CLIENT_KEY'] || '',
  GP_APP_BRAND: Brand.Aussie,
};

const AUSSIE_IS_SCRIPTS: Record<string, string> = {
  development: 'https://cdn.evgnet.com/beacon/auscred/aussie_dev/scripts/evergage.min.js',
  staging: 'https://cdn.evgnet.com/beacon/auscred/aussiestaging/scripts/evergage.min.js',
  production: 'https://cdn.evgnet.com/beacon/auscred/aussieproduction/scripts/evergage.min.js',
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const _cookies = cookies();
  const targetId = _cookies.get('targetId')?.value || _cookies.get('target_id')?.value; // Generate a new targetId if not present
  const authsession = _cookies.get('authsession')?.value; // sessionId,userId
  const [_, userId] = (authsession || '').split(',');
  const ldContext = getLDSingleKindContext(Brand.Aussie, targetId, userId);
  const initAllFlags = await getLDInitFlags(ldContext);

  return (
    <html lang="en">
      <head>
        <ColorSchemeScript />
        <script
          type="application/ld+json"
          key="org_json_ld"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(orgJsonLd) }}
        />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
      </head>
      <body>
        <DatadogInit />
        {/* Uncomment the CopilotKit when ready to use */}
        {/* <link rel="stylesheet" href="/copilotkit.css" /> */}
        {/* <CopilotKit runtimeUrl={`${ENV_VARS.GP_API_BASE_URL}/v1/property-hub/copilotkit`}> */}
        <AppProviders
          envVars={ENV_VARS}
          brand={Brand.Aussie}
          theme={aussieTheme}
          launchdarklyInit={{ bootstrap: initAllFlags.toJSON(), context: ldContext }}
        >
          {children}
        </AppProviders>
        {/* </CopilotKit> */}
      </body>
      {ENV_VARS.GP_APP_ENVIRONMENT in AUSSIE_IS_SCRIPTS && (
        <Script
          key="interaction_studio"
          src={AUSSIE_IS_SCRIPTS[ENV_VARS.GP_APP_ENVIRONMENT]}
          strategy="afterInteractive"
        />
      )}
    </html>
  );
}
