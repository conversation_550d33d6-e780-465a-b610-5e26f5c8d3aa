// Necessary if using App Router to ensure this file runs on the client
'use client';

import { datadogRum } from '@datadog/browser-rum';
import { reactPlugin } from '@datadog/browser-rum-react';

import { getEnvVar } from '@gp/util/data-service';

datadogRum.init({
  applicationId: 'c11a8b0a-66a0-478d-8d1f-c8ee7b497315',
  clientToken: 'pubfb8162621270b6adf0dd15d825860dff',
  site: 'datadoghq.com',
  service: 'aussie-contentful-cms',
  env: getEnvVar('GP_APP_ENVIRONMENT'),
  // Specify a version number to identify the deployed version of your application in Datadog
  // version: '1.0.0',
  sessionSampleRate: 0,
  sessionReplaySampleRate: 0,

  trackUserInteractions: true,
  trackResources: true,
  trackLongTasks: true,
  defaultPrivacyLevel: 'mask-user-input',
  plugins: [reactPlugin({ router: true })],
  // Specify URLs to propagate trace headers for connection between RUM and backend trace
});

datadogRum.onReady(() => {
  console.log('Datadog RUM initialized');
  if (typeof window !== 'undefined') {
    datadogRum.startView(window.location.pathname);
  }
});

export default function DatadogInit() {
  // Render nothing - this component is only included so that the init code
  // above will run client-side
  return null;
}
