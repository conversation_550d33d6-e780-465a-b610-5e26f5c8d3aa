import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-current-path', request.nextUrl.pathname);

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  let tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
  tomorrow = new Date(tomorrow.toDateString()); // not construct a new obj is for Date obj is expensieve
  // tomorrow hours here is 0 and syd time is UTC+11
  tomorrow.setTime(tomorrow.getTime() - 9 * 60 * 60 * 1000);
  response.headers.set('Cache-Control', `public`);
  response.headers.set('Expires', tomorrow.toUTCString());
  if (request.nextUrl.pathname.startsWith('/growth-product/_next/static')) {
    response.headers.set('x-robots-tag', 'noindex, nofollow');
  }
  return response;
}
