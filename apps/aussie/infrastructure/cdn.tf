data "aws_wafv2_web_acl" "lendi_waf" {
  provider = aws.us
  name = "wafv2-web-acl-cloudfront-${var.environment}"
  scope = "CLOUDFRONT" 
}
module "cdn" {
  source = "terraform-aws-modules/cloudfront/aws"

  // race condition here where the cert must be attached first before we can associate the alias to it
  aliases = [local.cloudfront_alias[var.environment]] # if we are able to point the sitemap / scrapers or whatever to another domain

  comment             = "Aussie Growth Product CDN"
  enabled             = true
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = true
  wait_for_deployment = false
  http_version        = "http2and3"

  # setup logging if desired, but we typically don't
  # logging_config = {
  #   bucket = "logs-my-cdn.s3.amazonaws.com"
  # }

  origin = {
    growth_product = {
      domain_name = local.env_backend[var.environment]
      custom_origin_config = {
        http_port              = 80
        https_port             = 443
        origin_protocol_policy = "match-viewer"
        origin_ssl_protocols   = ["TLSv1.2"]
      }
      custom_header = [
        {
          name  = "x-lendi-waf-allow-key"
          value = var.environment != "production" ? data.aws_ssm_parameter.waf_allow_key.value : "secure-lower-envs-only"
        }
      ]
    }
  }

  default_cache_behavior = {
    target_origin_id             = "growth_product"
    viewer_protocol_policy       = "redirect-to-https"
    allowed_methods              = ["GET", "HEAD", "OPTIONS"]
    cached_methods               = ["GET", "HEAD"]
    compress                     = true
    query_string                 = true
    cache_policy_id              = data.aws_cloudfront_cache_policy.cache.id // "UseOriginCacheControlHeaders-QueryStrings" is the default but uses the cookie header to cache | growth-product-cache-policy is created via lendi
    origin_request_policy_name   = "Managed-AllViewer"
    use_forwarded_values         = false
  }

  viewer_certificate = {
    acm_certificate_arn          = local.cloudfront_certs[var.environment]
    ssl_support_method           = "sni-only"
    minimum_protocol_version     = "TLSv1.2_2021"
  }


  tags = {
    LENDI_TEAM          = "growth"
    REPO                = "growth-product"
    ENV                 = var.environment
    DATA_CLASSIFICATION = "public"
    DEPLOY_TOOL         = "ltd terraform"
  }
  web_acl_id = data.aws_wafv2_web_acl.lendi_waf.arn
}