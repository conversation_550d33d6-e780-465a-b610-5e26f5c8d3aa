locals {
  subdomain  = "gp-aussie-latest"          // actual lcd subdomain that is setup
  cloudfront = "growth-product-cloudfront" // cloudfront alias
  env_backend = {
    "development" = "${local.subdomain}.lendi-paas-dev.net"
    "staging"     = "${local.subdomain}.lendi-paas-stg.net"
    "production"  = "${local.subdomain}.lendi-paas.net"
  }
  cloudfront_alias = {
    "development" = "${local.cloudfront}.aussie-dev.com.au"
    "staging"     = "${local.cloudfront}.aussie-stg.com.au"
    "production"  = "${local.cloudfront}.aussie.com.au"
  }
  cloudfront_certs = {
    "development" = "arn:aws:acm:us-east-1:702880128631:certificate/e34e4a61-e82b-4274-bfd0-5e0e2297a060" // *.aussie-dev.com.au
    "staging"     = "arn:aws:acm:us-east-1:106772905355:certificate/ad5b79ae-8362-4760-a568-51d08c1794c8" // *.aussie-stg.com.au
    "production"  = "arn:aws:acm:us-east-1:362442693667:certificate/d47e5e9d-3119-44db-88e8-5a3ead5655f3" // aussie.com.au
  }
}

data "aws_ssm_parameter" "waf_allow_key" {
  name            = "/platform/waf/growth-product-allow-key"
  with_decryption = true
}

data "aws_cloudfront_cache_policy" "cache" {
  name = "growth-product-cache-policy"
}

variable "environment" {
  type    = string
  default = "development"
  validation {
    condition     = can(regex("^(development|staging|production)$", var.environment))
    error_message = "Invalid environment provided ${var.environment}"
  }
}

variable "db_size" {
  type = map(any)
  default = {
    development = "db.t4g.medium"
    staging     = "db.t4g.medium"
    production  = "db.m6g.large"
  }
}

variable "db_storage_capacity" {
  type = map(any)
  default = {
    development = 450
    staging     = 400
    production  = 400
  }
}

variable "db_storage_iops" {
  type = map(any)
  default = {
    development = 12000
    staging     = 12000
    production  = 12000
  }
}

variable "ssh_tunnel_security_group" {
  type        = map(any)
  description = "security group ID of the SSH tunnel"
  default = {
    development = "sg-06abd7705238f11a1"
    staging     = "sg-0c6996d0e1d881946"
    production  = "sg-0a98d7480f13cb8ef"
  }
}
