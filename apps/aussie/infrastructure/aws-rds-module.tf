module "postgres-aws-rds" {
  source = "s3::https://s3-ap-southeast-2.amazonaws.com/platform-terraform-module-management/aws-rds-v2/"
  environment = var.environment
  team = "growth"
  project = "property-hub"
  tag = "latest"
  git_repo = "growth-product"
  create_r53_record = false
  db_size = var.db_size[var.environment]
  db_storage_capacity = var.db_storage_capacity[var.environment]
  db_storage_iops = var.db_storage_iops[var.environment]
  enable_rds_proxy_access = true
  whitelist_rds_proxy_security_groups = ["property-hub-latest"]
  whitelist_security_groups = [
    var.ssh_tunnel_security_group[var.environment],
  ]
  apply_immediately = true
}