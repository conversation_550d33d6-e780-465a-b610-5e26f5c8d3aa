#!/usr/bin/env bash
environment=$1;

source ./scripts/ci/setup-shell.sh

if [[ "$BUILDKITE_BRANCH" = "main" ]]; then
    tag="latest"
else
    tag=$BUILDKITE_BRANCH
fi
buildkite-agent artifact download dist/home-loan-health-check/$environment/* .
lsd deploy --environment $environment --project gp-home-loan-health-check --tag $tag --directory dist/home-loan-health-check/$environment -c '(\.html$|page-data\/|\.xml$|\.txt$)'
