plugins: &plugins
  - lendi-au/ssm#0.5.5:
      ssmkey: "NPM_TOKEN"
  - lendi-au/ssm#0.5.5:
      ssmkey: "platform/NPM_TOKEN"
      exportname: LENDI_NPM_TOKEN
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/lsd"
  - lendi-au/npm-global#1.1.0:
      env: "NPM_TOKEN"
      package: "@lendi/liam"
  - docker#v3.4.0:
      image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:latest
      workdir: /app
      environment:
        - "NPM_TOKEN"
        - "LENDI_NPM_TOKEN"
        - "AWS_REGION"
        - "AWS_DEFAULT_REGION"
      propagate-environment: true

artifacts: &artifacts
  - dist/home-loan-health-check/**/*
steps:
  - group: "📈 Home Loan Health Check Funnel"
    key: "home-loan-health-check"
    steps:
      #   ____  _    _ _____ _      _____
      #  |  _ \| |  | |_   _| |    |  __ \
      #  | |_) | |  | | | | | |    | |  | |
      #  |  _ <| |  | | | | | |    | |  | |
      #  | |_) | |__| |_| |_| |____| |__| |
      #  |____/ \____/|_____|______|_____/
      #  ==================================
      - label: ":webpack: Home Loan Health Check Funnel - Build (DEV)"
        agents:
          queue: build
        key: "home-loan-health-check:build-development"
        commands:
          - echo "⏳ Building Home Loan Health Check Funnel (DEV)"
          - ./apps/home-loan-health-check/ci/build.sh development
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Home Loan Health Check Funnel - Build (STG)"
        branches: 'main'
        agents:
          queue: build
        key: "home-loan-health-check:build-staging"
        commands:
          - echo "⏳ Building Home Loan Health Check Funnel (STG)"
          - ./apps/home-loan-health-check/ci/build.sh staging
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Home Loan Health Check Funnel - Build (PRE-PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "home-loan-health-check:build-preproduction"
        commands:
          - echo "⏳ Building Home Loan Health Check Funnel (PRE-PROD)"
          - ./apps/home-loan-health-check/ci/build.sh preproduction
        plugins: *plugins
        artifact_paths: *artifacts

      - label: ":webpack: Home Loan Health Check Funnel - Build (PROD)"
        branches: 'main'
        agents:
          queue: build
        key: "home-loan-health-check:build-production"
        commands:
          - echo "⏳ Building Home Loan Health Check Funnel (PROD)"
          - ./apps/home-loan-health-check/ci/build.sh production
        plugins: *plugins
        artifact_paths: *artifacts

      #   ____  ______ _____  _      ____  __     __
      #  |  _ \|  ____|  __ \| |    |  _ \ \ \   / /
      #  | | | | |__  | |  | | |    | | | | \ \_/ /
      #  | | | |  __| | |  | | |    | | | |  \   /
      #  | |_| | |____| |__| | |____| |_| |   | |
      #  |____/|______|_____/|______|____/    |_|
      #  ==========================================
      - label: ":codedeploy: Home Loan Health Check Funnel - Deploy (DEV)"
        agents:
          queue: development
        key: "home-loan-health-check:deploy-development"
        depends_on:
          - step: "home-loan-health-check:build-development"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Home Loan Health Check Funnel (DEV)"
          - ./apps/home-loan-health-check/ci/deploy.sh development
        plugins: *plugins

      - label: ":codedeploy: Home Loan Health Check Funnel - Deploy (STG)"
        if: build.branch == 'main'
        agents:
          queue: staging
        key: "home-loan-health-check:deploy-staging"
        depends_on:
          - step: "home-loan-health-check:build-staging"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Home Loan Health Check Funnel (STG)"
          - ./apps/home-loan-health-check/ci/deploy.sh staging
        plugins: *plugins

      - label: ":codedeploy: Home Loan Health Check Funnel - Deploy (PRE-PROD)"
        if: build.branch == 'main'
        agents:
          queue: preproduction
        key: "home-loan-health-check:deploy-preproduction"
        depends_on:
          - step: "home-loan-health-check:build-preproduction"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Home Loan Health Check Funnel (PRE-PROD)"
          - ./apps/home-loan-health-check/ci/deploy.sh preproduction
        plugins: *plugins

      - block: ':question: Release to Production'
        if: build.branch == 'main'
        key: "home-loan-health-check:deploy-production-check"
        prompt: 'Are you sure you want to release to production?'

      - label: ":codedeploy: Home Loan Health Check Funnel - Deploy (PROD)"
        if: build.branch == 'main'
        agents:
          queue: production
        key: "home-loan-health-check:deploy-production"
        depends_on:
          - step: "home-loan-health-check:build-production"
          - step: "home-loan-health-check:deploy-production-check"
        allow_dependency_failure: false
        commands:
          - echo "🚀 Deploying Home Loan Health Check Funnel (PROD)"
          - ./apps/home-loan-health-check/ci/deploy.sh production
        plugins: *plugins
