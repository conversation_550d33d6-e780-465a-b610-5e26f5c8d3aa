const { composePlugins, withNx } = require('@nx/webpack');
const { withReact } = require('@nx/react');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = composePlugins(
  withNx({
    outputHashing: isProduction ? 'all' : 'none',
    optimization: isProduction,
  }),
  withReact({
    outputHashing: isProduction ? 'all' : 'none',
    svgr: false,
    generateIndexHtml: false,
  }),
  (config) => {
    config.output = {
      ...config.output,
      publicPath: process.env.NX_PUBLIC_BASE_URL || '/',
    };
    config.devtool = isProduction ? 'source-map' : 'eval-source-map';
    config.stats = Object.assign({}, config.stats, {
      warnings: false,
      colors: true,
      chunks: true,
      assets: false,
      chunkGroups: false,
      chunkModules: false,
      chunkOrigins: false,
    });
    config.plugins?.push(
      new HtmlWebpackPlugin({
        template: './src/index.html',
        scriptLoading: 'module',
      })
    );

    // Add Buffer polyfill for browser compatibility
    config.resolve = {
      ...config.resolve,
      fallback: {
        ...config.resolve?.fallback,
        buffer: require.resolve('buffer'),
      },
    };

    config.plugins?.push(
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
      })
    );

    // Define environment variables for runtime
    config.plugins?.push(
      new webpack.DefinePlugin({
        'process.env.NX_PUBLIC_ENVIRONMENT': JSON.stringify(
          process.env.NX_PUBLIC_ENVIRONMENT || 'development'
        ),
        'process.env.NX_PUBLIC_API_BASE_URL': JSON.stringify(
          process.env.NX_PUBLIC_API_BASE_URL || ''
        ),
        'process.env.NX_PUBLIC_LAUNCHDARKLY_CLIENT_ID': JSON.stringify(
          process.env.NX_PUBLIC_LAUNCHDARKLY_CLIENT_ID || ''
        ),
      })
    );

    return config;
  }
);
