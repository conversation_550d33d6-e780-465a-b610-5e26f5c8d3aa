import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom';

import { FunnelLayout } from '@gp/feature/funnels';

const router = createBrowserRouter(
  [
    {
      path: '/',
      async lazy() {
        const { LoanDetails } = await import('@gp/feature/home-loan-health-check');
        return { Component: LoanDetails };
      },
    },
    {
      path: 'result',
      async lazy() {
        const { ResultLayout } = await import('@gp/feature/home-loan-health-check');
        return { Component: ResultLayout };
      },
      children: [
        {
          index: true,
          async lazy() {
            const { Result } = await import('@gp/feature/home-loan-health-check');
            return { Component: Result };
          },
        },
        {
          path: 'reduce-repayments',
          async lazy() {
            const { ReduceRepayments } = await import('@gp/feature/home-loan-health-check');
            return { Component: ReduceRepayments };
          },
        },
        {
          path: 'pay-loan-faster',
          async lazy() {
            const { PayLoanFaster } = await import('@gp/feature/home-loan-health-check');
            return { Component: PayLoanFaster };
          },
        },
        {
          path: 'fixed-rate-expiry-reminder',
          async lazy() {
            const { FixedRateExpiryReminder } = await import('@gp/feature/home-loan-health-check');
            return { Component: FixedRateExpiryReminder };
          },
        },
        {
          path: 'rba-variable',
          async lazy() {
            const { ReduceRepaymentsRBA } = await import('@gp/feature/home-loan-health-check');
            return { Component: ReduceRepaymentsRBA };
          },
        },
        {
          path: 'rba-fixed',
          async lazy() {
            const { FixedRateExpiryReminderRBA } = await import(
              '@gp/feature/home-loan-health-check'
            );
            return { Component: FixedRateExpiryReminderRBA };
          },
        },
      ],
    },
    {
      path: '*',
      Component: () => <Navigate to="/" />,
    },
  ],
  {
    ...(process.env.NODE_ENV === 'production' && { basename: '/home-loans/basics/health-check' }),
  }
);

export function App() {
  console.log('App Initialised');
  return (
    <FunnelLayout>
      <RouterProvider router={router} />
    </FunnelLayout>
  );
}

export default App;
