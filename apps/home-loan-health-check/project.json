{"name": "home-loan-health-check", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/home-loan-health-check/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/home-loan-health-check/webpack.config.js", "outputPath": "apps/home-loan-health-check/dist", "index": "apps/home-loan-health-check/src/index.html", "main": "apps/home-loan-health-check/src/main.tsx", "tsConfig": "apps/home-loan-health-check/tsconfig.app.json", "assets": ["apps/home-loan-health-check/src/growth-product-assets"], "postcssConfig": "apps/home-loan-health-check/postcss.config.js"}}, "serve": {"executor": "@nx/webpack:dev-server", "options": {"buildTarget": "build", "hmr": true, "port": 8000}}, "preview": {"executor": "nx:run-commands", "options": {"command": "echo", "args": ["use \"pnpm nx run home-loan-health-check:serve-static\" to preview the app"]}}, "serve-static": {"options": {"buildTarget": "build", "staticFilePath": "apps/home-loan-health-check/dist", "port": 8888, "spa": true}}}}