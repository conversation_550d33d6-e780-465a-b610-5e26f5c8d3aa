- key: deploy-block-stg
  block: ':rocket: Deploy Staging'
  branches: main
  depends_on: []

- label: Provision Stg Monitoring 📈
  key: provision-monitoring-stg
  agents:
    queue: staging
  concurrency: 1
  concurrency_group: ${BUILDKITE_PIPELINE_SLUG}/staging/${B<PERSON><PERSON><PERSON><PERSON>E_BRANCH}/provision-monitoring-stg
  branches: main
  retry:
    manual:
      permit_on_passed: true
  depends_on:
    - step: deploy-block-stg
  commands:
    - echo "--- Provision Stg Monitoring ---"
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-cms-contentful aussie-jam ajam staging growth-product-alerts aussie-stg.com.au lendi-paas-stg.net
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-cms-hygraph aussie-hygraph ahyg staging growth-product-alerts aussie-stg.com.au lendi-paas-stg.net
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-property aussie-property aprop staging growth-product-alerts aussie-stg.com.au lendi-paas-stg.net
    - ./monitoring/scripts/ci/provision-monitoring.sh lendi-cms-contentful lendi-jam ljam staging growth-product-alerts lendi-stg.net lendi-paas-stg.net

  plugins:
    - docker#v3.7.0:
        image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:node18
        workdir: /app
        environment:
          - BUILDKITE_BRANCH
          - BUILDKITE_BUILD_NUMBER
          - DATADOG_API_KEY
          - DATADOG_APP_KEY
        propagate-environment: true

- key: deploy-block-prod
  block: ':rocket: Deploy Production'
  branches: main
  depends_on:
    - step: deploy-block-stg

- label: Provision Prod Monitoring 📈
  key: provision-monitoring-prod
  agents:
    queue: production
  concurrency: 1
  concurrency_group: ${BUILDKITE_PIPELINE_SLUG}/production/${BUILDKITE_BRANCH}/provision-monitoring-prod
  branches: main
  retry:
    manual:
      permit_on_passed: true
  depends_on:
    - step: deploy-block-prod
  commands:
    - echo "--- Provision Prod Monitoring ---"
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-cms-contentful aussie-jam ajam production growth-product-alerts aussie.com.au lendi-paas.net
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-cms-hygraph aussie-hygraph ahyg production growth-product-alerts aussie.com.au lendi-paas.net
    - ./monitoring/scripts/ci/provision-monitoring.sh aussie-property aussie-property aprop production growth-product-alerts aussie.com.au lendi-paas.net
    - ./monitoring/scripts/ci/provision-monitoring.sh lendi-cms-contentful lendi-jam ljam production growth-product-alerts lendi.com.au lendi-paas.net

  plugins:
    - docker#v3.7.0:
        image: 291089888569.dkr.ecr.ap-southeast-2.amazonaws.com/platform-tools:node18
        workdir: /app
        environment:
          - BUILDKITE_BRANCH
          - BUILDKITE_BUILD_NUMBER
          - DATADOG_API_KEY
          - DATADOG_APP_KEY
        propagate-environment: true