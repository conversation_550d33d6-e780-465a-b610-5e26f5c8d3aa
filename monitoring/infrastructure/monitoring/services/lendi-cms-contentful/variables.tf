locals {
  module_name      = upper(var.short_name)
  service_name     = lower("${var.project}-${var.tag}-${var.environment}")
  slack_channel    = "@slack-Datadog-${replace(var.slack_channel, "^#", "")}"
}

variable "environment" {
  type        = string
  description = "Must be one of: development|staging|production"
}

variable "api_host" {
  type        = string
  description = "Host for the back end for monitoring"
}

variable "http_host" {
  type        = string
  description = "Host for the front end for monitoring"
}

variable "project" {
  type        = string
  description = "The name of your project/service in ECS"
}

variable "tag" {
  type        = string
  description = "The lcd tag you're using to deploy your app"
}

variable "state_bucket" {
  type        = string
  description = "The S3 bucket where the state file will be stored"
}

variable "state_file" {
  type        = string
  description = "The location where the state file will be stored"
}

variable "slack_channel" {
  type        = string
  description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
  type        = string
  description = "Team name"
}

variable "short_name" {
  type        = string
  description = "Short name of the project (e.g. fp, rd, sc, lads)"
}
