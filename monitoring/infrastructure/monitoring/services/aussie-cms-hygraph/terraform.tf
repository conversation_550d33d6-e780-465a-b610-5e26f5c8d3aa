locals {
  region = "ap-southeast-2"
  bucket = var.state_bucket
  key    = var.state_file
}

terraform {
  backend "s3" {
    region = "ap-southeast-2"
    # bucket and key are not specified here because they're being passed through as backend partials
  }
}

data "terraform_remote_state" "network" {
  count = 0 # to allow missing remote state on initial create
  backend = "s3"
  config = {
    region = local.region
    bucket = local.bucket
    key    = local.key
  }
}

