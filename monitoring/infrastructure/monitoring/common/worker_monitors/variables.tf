variable "module_name" {
  type = string
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "consumer_group" {
    type        = string
}

variable "service" {
    type        = string
}

variable "service_name" {
    type        = string
}

variable "team" {
    type        = string
}

variable "project" {
    type        = string
}

variable "namespace" {
    type        = string
}

variable "k8s_enabled" {
    type        = bool
    default     = true
}

variable "thresholds" {
    description = "Workers monitors threshold"
    type = object({
        consumer_group_lag = object({
            ok          = number
            critical    = number
        })
        high_error_rate_percentage = object({
            ok          = number
            critical    = number
        })
        high_average_latency_ms = object({
            ok          = number
            critical    = number
        })
        kafka_schema_validation_errors = object({
            ok          = number
            critical    = number
        })
        conditional_check_failed_exception_error_rate = object({
            ok          = number
            critical    = number
        })
    })
    default = {
        consumer_group_lag = {
            ok          = 0
            critical    = 60
        }
        high_error_rate_percentage = {
            ok          = 0
            critical    = 0.08
        }
        high_average_latency_ms = {
            ok          = 0
            critical    = 0.8
        }
        kafka_schema_validation_errors = {
            ok          = 0
            critical    = 1
        }
        conditional_check_failed_exception_error_rate = {
            ok          = 0
            critical    = 0.05
        }
    }
}

locals {
    // concat monitor naming variable
    // i.e. product-api-latest-development => CYL product-api - development
    monitor_name = "${var.module_name} worker - ${replace(var.service, "-latest-", " - ")}"
}