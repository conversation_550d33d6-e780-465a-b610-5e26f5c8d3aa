resource "datadog_monitor" "worker_consumer_group_lag" {
    count = var.environment=="production" ? 1 : 0
    name    = "${local.monitor_name}: Consumer Group Lag"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Consumer Group Lag > ${var.thresholds.consumer_group_lag.critical}. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "min(last_5m):avg:instaclustr.consumerGroupLag.count{consumergroup:${var.consumer_group}} by {topic} >= ${var.thresholds.consumer_group_lag.critical}"

    monitor_thresholds {
        ok          = var.thresholds.consumer_group_lag.ok
        critical    = var.thresholds.consumer_group_lag.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_high_error_rate" {
    name    = "${local.monitor_name}: High Error Rate"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.high_error_rate_percentage.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "sum(last_10m):(sum:trace.consumer.eachMessage.errors{env:${var.environment},service:${var.service}}.as_count() / sum:trace.consumer.eachMessage.hits{env:${var.environment},service:${var.service}}.as_count()) >= ${var.thresholds.high_error_rate_percentage.critical}"

    monitor_thresholds {
        ok          = var.thresholds.high_error_rate_percentage.ok
        critical    = var.thresholds.high_error_rate_percentage.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_high_average_latency" {
    name    = "${local.monitor_name}: High Average Latency"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Average Latency > ${ceil(var.thresholds.high_average_latency_ms.critical * 1000)}ms. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "min(last_10m):( sum:trace.consumer.eachMessage.duration{service:${var.service},env:${var.environment}}.rollup(sum).fill(zero) / sum:trace.consumer.eachMessage.hits{service:${var.service},env:${var.environment}}.rollup(sum).fill(zero) ) > ${var.thresholds.high_average_latency_ms.critical}"

    monitor_thresholds {
        ok          = var.thresholds.high_average_latency_ms.ok
        critical    = var.thresholds.high_average_latency_ms.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "worker_kafka_schema_validation_errors" {
    name    = "${local.monitor_name}: Kafka Schema Validation Errors"
    type    = "log alert"
    message = "{{#is_alert}}${var.service} Kafka Schema Validation Errors triggered. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "logs(\"service:${var.service} @msg:Skipping*validate*\").index(\"*\").rollup(\"count\").last(\"5m\") >= ${var.thresholds.kafka_schema_validation_errors.critical}"

    monitor_thresholds {
        ok          = var.thresholds.kafka_schema_validation_errors.ok
        critical    = var.thresholds.kafka_schema_validation_errors.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

module "lendi-datadog-k8s" {
    source = "s3::https://s3-ap-southeast-2.amazonaws.com/platform-terraform-module-management/k8s-datadog/"
    environment = var.environment
    slack_channel = var.slack_channel
    team = var.team
    project = "${var.project}-${var.service_name}"
    namespace = var.namespace
    count = var.k8s_enabled ? 1 : 0
    monitoring = true
}

resource "datadog_monitor" "conditional_check_failed_exception_error_rate" {
    name    = "${local.monitor_name}: ConditionalCheckFailedException Error Rate"
    type    = "log alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.conditional_check_failed_exception_error_rate.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "formula(\"query1 / query2\").last(\"60m\") >= ${var.thresholds.conditional_check_failed_exception_error_rate.critical}"

    variables {
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query1"
            indexes = ["*"]
            search {
                query = "service:${var.service} @msg:*conditional*request*failed*"
            }
        }
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query2"
            indexes = ["*"]
            search {
                query = "service:${var.service}"
            }
        }
    }

    monitor_thresholds {
        ok          = var.thresholds.conditional_check_failed_exception_error_rate.ok
        critical    = var.thresholds.conditional_check_failed_exception_error_rate.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}