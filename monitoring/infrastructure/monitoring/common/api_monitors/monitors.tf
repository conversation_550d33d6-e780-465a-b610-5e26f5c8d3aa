resource "datadog_monitor" "apm_request_latency" {
    name    = "${local.monitor_name}: Request Latency"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Average Latency > ${var.thresholds.apm_request_latency_seconds.critical}s. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "min(last_10m):avg:trace.koa.request.duration.by.service.90p{service:${var.service},env:${var.environment}} >= ${var.thresholds.apm_request_latency_seconds.critical}"

    monitor_thresholds {
        ok          = var.thresholds.apm_request_latency_seconds.ok
        critical    = var.thresholds.apm_request_latency_seconds.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "apm_error_rate" {
    name    = "${local.monitor_name}: Error Rate"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.apm_error_rate_percentage.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "min(last_10m):( sum:trace.koa.request.errors{service:${var.service},env:${var.environment}}.rollup(sum, 300) / sum:trace.koa.request.hits{service:${var.service},env:${var.environment}}.rollup(sum, 300) ) >= ${var.thresholds.apm_error_rate_percentage.critical}"

    monitor_thresholds {
        ok          = var.thresholds.apm_error_rate_percentage.ok
        critical    = var.thresholds.apm_error_rate_percentage.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "http_status_code_4xx_error_rate" {
    name    = "${local.monitor_name}: High Http Status Code [400-499] Error Rate"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Http Status Code [400-499] Error Rate > ${ceil(var.thresholds.http_status_code_4xx_error_rate_percentage.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query   = "sum(last_15m):moving_rollup(sum:trace.koa.request.hits{service:${var.service},http.status_code:4*}.as_count().rollup(sum, 900) / sum:trace.koa.request.hits{service:${var.service}}.as_count().rollup(sum, 900), 3600, 'avg') > ${var.thresholds.http_status_code_4xx_error_rate_percentage.critical}"

    monitor_thresholds {
        ok                 = var.thresholds.http_status_code_4xx_error_rate_percentage.ok
        critical           = var.thresholds.http_status_code_4xx_error_rate_percentage.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

module "lendi-datadog-k8s" {
    source = "s3::https://s3-ap-southeast-2.amazonaws.com/platform-terraform-module-management/k8s-datadog/"
    environment = var.environment
    slack_channel = var.slack_channel
    team = var.team
    project = "${var.project}-${var.service_name}"
    namespace = var.namespace
    count = var.k8s_enabled ? 1 : 0
    monitoring = true
}

resource "datadog_monitor" "conditional_check_failed_exception_error_rate" {
    name    = "${local.monitor_name}: ConditionalCheckFailedException Error Rate"
    type    = "log alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.conditional_check_failed_exception_error_rate.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "formula(\"query1 / query2\").last(\"60m\") >= ${var.thresholds.conditional_check_failed_exception_error_rate.critical}"

    variables {
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query1"
            indexes = ["*"]
            search {
                query = "service:${var.service} @msg:*conditional*request*failed*"
            }
        }
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query2"
            indexes = ["*"]
            search {
                query = "service:${var.service}"
            }
        }
    }

    monitor_thresholds {
        ok          = var.thresholds.conditional_check_failed_exception_error_rate.ok
        critical    = var.thresholds.conditional_check_failed_exception_error_rate.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}