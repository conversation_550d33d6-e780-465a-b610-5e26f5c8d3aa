variable "module_name" {
  type = string
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "service" {
    type        = string
}

variable "service_name" {
    type        = string
}

variable "team" {
    type        = string
}

variable "project" {
    type        = string
}

variable "namespace" {
    type        = string
}

variable "api_url" {
    type        = string
}

variable "k8s_enabled" {
    type        = bool
    default     = true
}

variable "thresholds" {
    description = "Api monitors threshold"
    type = object({
        apm_request_latency_seconds = object({
            ok          = number
            critical    = number
        })
        apm_error_rate_percentage = object({
            ok          = number
            critical    = number
        })
        http_status_code_4xx_error_rate_percentage = object({
            ok          = number
            critical    = number
        })
        conditional_check_failed_exception_error_rate = object({
            ok          = number
            critical    = number
        })
    })
    default = {
        apm_request_latency_seconds = {
            ok          = 0.5
            critical    = 1
        }
        apm_error_rate_percentage = {
            ok          = 0
            critical    = 0.05
        }
        http_status_code_4xx_error_rate_percentage = {
            ok          = 0.02
            critical    = 0.05
        }
        conditional_check_failed_exception_error_rate = {
            ok          = 0
            critical    = 0.05
        }
    }
}

locals {
    // concat monitor naming variable
    // i.e. product-api-latest-development => CYL product-api - development
    monitor_name    = "${var.module_name} api - ${replace(var.service, "-latest-", " - ")}"
}