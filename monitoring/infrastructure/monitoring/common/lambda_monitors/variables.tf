variable "module_name" {
  type = string
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "service" {
    type        = string
}

variable "team" {
    type        = string
}

variable "project" {
    type        = string
}

variable "thresholds" {
    description = "Lambda monitors threshold"
    type = object({
        iterator_age_ms = object({
            ok          = number
            critical    = number
        })
        apm_error_rate_percentage = object({
            ok          = number
            critical    = number
        })
        conditional_check_failed_exception_error_rate = object({
            ok          = number
            critical    = number
        })
    })
    default = {
        iterator_age_ms = {
            ok          = 1000
            critical    = 4000
        }
        apm_error_rate_percentage = {
            ok          = 0
            critical    = 0.05
        }
        conditional_check_failed_exception_error_rate = {
            ok          = 0
            critical    = 0.05
        }
    }
}

locals {
    // concat monitor naming variable
    // i.e. product-api-latest-development => CYL product-api - development
    monitor_name    = "${var.module_name} lambda - ${replace(var.service, "-latest-", " - ")}"
}