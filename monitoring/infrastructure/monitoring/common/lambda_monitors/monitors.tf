resource "datadog_monitor" "lambda_iterator_age" {
    name    = "${local.monitor_name}: Iterator Age"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Iterator Age > ${var.thresholds.iterator_age_ms.critical / 1000}s. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "min(last_10m):default_zero(avg:aws.lambda.iterator_age{functionname:${var.service},env:${var.environment}}) >= ${var.thresholds.iterator_age_ms.critical}"

    monitor_thresholds {
        ok          = var.thresholds.iterator_age_ms.ok
        critical    = var.thresholds.iterator_age_ms.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "apm_error_rate" {
    name    = "${local.monitor_name}: Error Rate"
    type    = "query alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.apm_error_rate_percentage.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "avg(last_10m):default_zero(sum:aws.lambda.errors{functionname:${var.service},env:${var.environment}} / sum:aws.lambda.invocations{functionname:${var.service},env:${var.environment}}) >= ${var.thresholds.apm_error_rate_percentage.critical}"

    monitor_thresholds {
        ok          = var.thresholds.apm_error_rate_percentage.ok
        critical    = var.thresholds.apm_error_rate_percentage.critical
    }

    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "conditional_check_failed_exception_error_rate" {
    name    = "${local.monitor_name}: ConditionalCheckFailedException Error Rate"
    type    = "log alert"
    message = "{{#is_alert}}${var.service} Error Rate > ${ceil(var.thresholds.conditional_check_failed_exception_error_rate.critical * 100)}%. Please check [service page](/apm/services/${var.service}?env=${var.environment}) and [logs](/logs?query=service:${var.service}) to understand why.){{/is_alert}} ${var.slack_channel}"
    query = "formula(\"query1 / query2\").last(\"60m\") >= ${var.thresholds.conditional_check_failed_exception_error_rate.critical}"
    
    variables {
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query1"
            indexes = ["*"]
            search {
                query = "service:${var.service} @message:*conditional*request*failed*"
            }
        }
        event_query {
            compute {
                aggregation = "count"
            }
            data_source = "logs"
            name = "query2"
            indexes = ["*"]
            search {
                query = "service:${var.service}"
            }
        }
    }

    monitor_thresholds {
        ok          = var.thresholds.conditional_check_failed_exception_error_rate.ok
        critical    = var.thresholds.conditional_check_failed_exception_error_rate.critical
    }
    
    tags = ["env:${var.environment}","service:${var.service}","team:${var.team}","project:${var.project}"]
}