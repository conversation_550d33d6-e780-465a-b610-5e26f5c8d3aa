resource "datadog_monitor" "sqs_messages_visible" {
    name    = "${local.monitor_name}: Messages Available"
    type    = "query alert"
    message = "SQS queue messages exist in sqs queue: ${var.queue_name}. Please check in AWS console to manually action for ${var.environment} environment. ${var.slack_channel}"
    query = "avg(last_10m):max:aws.sqs.approximate_number_of_messages_visible{queuename:${var.queue_name}} >= ${var.thresholds.messages_visible.critical}"

    monitor_thresholds {
        ok          = var.thresholds.messages_visible.ok
        critical    = var.thresholds.messages_visible.critical
    }

    renotify_interval = 1440
    renotify_statuses = ["alert"]

    tags = ["env:${var.environment}","service:${var.queue_name}","team:${var.team}","project:${var.project}"]
}

resource "datadog_monitor" "sqs_oldest_message" {
    name    = "${local.monitor_name}: Age of oldest message"
    type    = "query alert"
    message = "Message/s in SQS queue are older than ${var.thresholds.oldest_message_seconds.critical / 60} minutes for queue: ${var.queue_name}. Please check SQS queue in AWS console to manually action for ${var.environment} environment. ${var.slack_channel}"
    query = "avg(last_10m):max:aws.sqs.approximate_age_of_oldest_message{queuename:${var.queue_name}} >= ${var.thresholds.oldest_message_seconds.critical}"

    monitor_thresholds {
        ok          = var.thresholds.oldest_message_seconds.ok
        critical    = var.thresholds.oldest_message_seconds.critical
    }

    renotify_interval = 1440
    renotify_statuses = ["alert"]

    tags = ["env:${var.environment}","service:${var.queue_name}","team:${var.team}","project:${var.project}"]
}