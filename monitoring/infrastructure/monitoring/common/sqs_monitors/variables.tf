variable "module_name" {
  type = string
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
}

variable "queue_name" {
    type        = string
    description = "The name of the sqs dead letter queue"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
    type        = string
}

variable "project" {
    type        = string
}

variable "thresholds" {
    description = "SQS monitors threshold"
    type = object({
        messages_visible = object({
            ok          = number
            critical    = number
        })
        oldest_message_seconds = object({
            ok           = number
            critical     = number
        })
    })
    default = {
        messages_visible = {
            ok          = 5
            critical    = 20
        }
        oldest_message_seconds = {
            ok          = 0
            critical    = 600
        }
    }
}

locals {
    // concat monitor naming variable
    // i.e. product-api-latest-development => CYL product-api - development
    monitor_name    = "${var.module_name} sqs - ${var.queue_name} - ${var.environment}"
}