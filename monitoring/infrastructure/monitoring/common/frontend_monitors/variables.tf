variable "module_name" {
  type = string
}

variable "environment" {
    type        = string
    description = "Must be one of: development|staging|production"
}

variable "slack_channel" {
    type        = string
    description = "The slack channel integration where you'll be receiving monitoring notifications"
}

variable "team" {
    type        = string
}

variable "project" {
    type        = string
}

variable "frontend_url" {
    type        = string
}

variable "synthetics_tick_every" {
  type = map
  default = {
    development   = 1800,
    staging       = 1800,
    production    = 60,
  }
}

variable "status_code" {
  type = string
  description = "HTTP status code response"
  default = "200"
}

variable "content_type" {
  type = string
  description = "Content type of the response"
  default = "text/html"
}