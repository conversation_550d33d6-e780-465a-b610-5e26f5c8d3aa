#!/usr/bin/env bash

# if [ "${BUILDKITE_SOURCE_ENV_VAR:-}" = "schedule" ]; then
#   source ./scripts/ci/pull-latest.sh
# fi

source ./scripts/utils/setup-shell.sh

directory=$1
project=$2 # repository name
short_name=$3 # abbreviated name e.g. fp, rd, sc, lads
environment=$4
slack_channel=$5
http_host=$6
api_host=$7
monitoring_dir="./monitoring/infrastructure/monitoring/services/${directory}"
state_bucket="platform-terraform-states-${environment}"
state_file="${BUILDKITE_PIPELINE_SLUG}/monitoring/services/${directory}/tf.tfstate"

team="ui-systems"
tag="latest"

tfenv install 0.14.0
tfenv use 0.14.0

rm -rf .terraform*
liam exec --environment $environment --role deployer -- \
terraform init -input=false -backend-config="bucket=${state_bucket}" \
-backend-config="key=${state_file}" $monitoring_dir

set +e
liam exec --environment $environment --role deployer -- \
terraform apply -auto-approve -refresh=true -var environment=$environment \
-var project=$project -var short_name=$short_name -var state_bucket=$state_bucket -var http_host=$http_host -var api_host=$api_host \
-var team=$team -var state_file=$state_file -var tag=$tag -var slack_channel=$slack_channel $monitoring_dir
exitCode=$?
set -e
exit $exitCode
