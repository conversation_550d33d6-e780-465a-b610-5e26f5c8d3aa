# Quals Monitoring

All of our monitoring for our services is configured in this repository using Terraform modules and deployed through the buildkite pipeline.

[![Build status](https://badge.buildkite.com/906c60b1bc168ddbd2c0fe5a23a3f7369ed9595631f9397bb9.svg)](https://buildkite.com/lendi-pty-ltd/quals-monitoring)

## How to Create New Terraform Monitors

The Datadog Terraform provider documentation is available [here](https://www.terraform.io/docs/providers/datadog/index.html).

- For monitoring updates and additions, the `monitors.tf` files are the ones you want to add your new resources to.
- For dashboard updates and additions, the `dashboards.tf` files are the ones you want to add your new resources to.
- For synthetics updates and additions the `synthetics.tf` files are the ones you want to add your new resources to.

## Notes

If you haven't done so, you will need to set up the Slack Integration for your monitoring [here](https://app.datadoghq.com/account/settings#integrations/slack).
