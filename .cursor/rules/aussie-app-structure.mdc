---
description: 
globs: 
alwaysApply: false
---
# Aussie App Project Structure and Conventions

The `aussie` application is a frontend app located at [`apps/aussie/`](mdc:../apps/aussie) in the workspace. This rule describes its structure and key conventions.

## Directory Layout
- **[`ci/`](mdc:../apps/aussie/ci)**: Contains CI/CD pipeline files and scripts (e.g., `pipeline.yml`, Dockerfile, etc.)
- **[`src/`](mdc:../apps/aussie/src)**: Main source code for the app
  - `app/`: Next.js app directory (routes, pages, etc.)
  - Other subdirectories for features, components, or utilities
- **[`public/`](mdc:../apps/aussie/public)**: Static assets (images, icons, etc.)
- **[`infrastructure/`](mdc:../apps/aussie/infrastructure)**: Infrastructure-as-code resources for the app (e.g., Terraform files)

## Key Files
- [`next.config.js`](mdc:../apps/aussie/next.config.js): Next.js configuration
- [`postcss.config.js`](mdc:../apps/aussie/postcss.config.js): PostCSS configuration
- [`project.json`](mdc:../apps/aussie/project.json): Nx project configuration
- [`tsconfig.json`](mdc:../apps/aussie/tsconfig.json): TypeScript configuration
- [`next-env.d.ts`](mdc:../apps/aussie/next-env.d.ts): Next.js TypeScript environment definitions
- [`.eslintrc.json`](mdc:../apps/aussie/.eslintrc.json): ESLint configuration

## Conventions
- Use Next.js for app structure and routing.
- Place static assets in the `public/` directory.
- Use the `ci/` directory for all CI/CD and deployment scripts.
- Store infrastructure code in the `infrastructure/` directory.
- Use TypeScript for all source code.
- Follow Nx and Next.js best practices for configuration and code organization.

For more details, see the [Nx documentation](mdc:https:/nx.dev) and [Next.js documentation](mdc:https:/nextjs.org/docs).

## Coding Standards and Best Practices
- **Linting**: The app uses [`.eslintrc.json`](mdc:../apps/aussie/.eslintrc.json) for ESLint configuration. Always lint your code before committing.
- **TypeScript**: All code should be written in TypeScript. Use [`tsconfig.json`](mdc:../apps/aussie/tsconfig.json) for type checking and configuration.
- **Component Structure**: Organize React components by feature or domain within `src/` for maintainability.
- **Styling**: Use CSS modules or global CSS as appropriate. Configure PostCSS via [`postcss.config.js`](mdc:../apps/aussie/postcss.config.js).
- **Imports**: Prefer absolute imports (configured via Nx/TypeScript) for clarity and to avoid deep relative paths.
- **Testing**: Write unit and integration tests for components and utilities. Follow workspace or team testing standards.
- **Code Reviews**: All changes should be peer-reviewed to ensure code quality and adherence to standards.
- **Documentation**: Document complex logic and public APIs with comments or markdown files as needed.

Follow Nx, Next.js, and your team's best practices for frontend development. For more, see the [Nx documentation](mdc:https:/nx.dev) and [Next.js documentation](mdc:https:/nextjs.org/docs).
