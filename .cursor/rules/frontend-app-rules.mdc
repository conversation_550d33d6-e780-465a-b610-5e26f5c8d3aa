---
description: 
globs: 
alwaysApply: false
---
# Frontend App Structure and Conventions

This workspace uses a monorepo structure managed by Nx. All frontend applications are located in the [`apps/`](mdc:../apps) directory. Each app follows these conventions:

## Directory Layout
- **`apps/<app-name>/`**: Root for each frontend app (e.g., `aussie`, `lendi`, `home-loan-health-check`, etc.)
  - **`src/`**: Main source code for the app
    - **`app/`**: Next.js app directory (for Next.js apps)
    - **`components/`**: Shared or app-specific React components (if present)
    - **`public/`**: Static assets (images, icons, etc.)
    - **`styles/`** or `styles.css`: App-level styles (if present)
  - **`next.config.js`**: Next.js configuration (for Next.js apps)
  - **`postcss.config.js`**: PostCSS configuration
  - **`project.json`**: Nx project configuration (may be present for some apps)
  - **`ci/`**: CI/CD scripts and pipeline definitions

## Conventions
- **Framework**: Most frontend apps use Next.js (see `next.config.js`).
- **Styling**: Use PostCSS and CSS modules or global CSS as appropriate.
- **Assets**: Place static assets in the `public/` directory.
- **CI/CD**: App-specific CI/CD scripts are in the `ci/` subdirectory.
- **Testing**: Each app may have its own Jest configuration (`jest.config.ts`).
- **TypeScript**: All apps use TypeScript for type safety.

## Nx Integration
- Use Nx commands for building, serving, testing, and linting apps.
- App dependencies and targets are managed via `project.json` or the global Nx configuration.

## Best Practices
- Keep shared logic in `libs/` and import into apps as needed.
- Use environment variables and configuration files for environment-specific settings.
- Follow Nx and Next.js best practices for project structure and code organization.

For more details, see the [Nx documentation](mdc:https:/nx.dev) and [Next.js documentation](mdc:https:/nextjs.org/docs).

# CI/CD Pipeline Rules for Frontend Apps

Each frontend app may include a `pipeline.yml` file to define its CI/CD process. These files are typically located at:
- `apps/<app-name>/ci/pipeline.yml`

## Purpose
- The `pipeline.yml` file defines the build, test, and deployment steps for the app in the CI/CD system.
- It may include steps for linting, running tests, building Docker images, deploying to environments, and more.

## Conventions
- Place the `pipeline.yml` file inside the `ci/` directory of the app (e.g., `apps/aussie/ci/pipeline.yml`).
- Use clear, descriptive step names and comments to document each stage of the pipeline.
- Reference reusable scripts or commands from the `scripts/` directory when possible to avoid duplication.
- Ensure environment variables and secrets are managed securely and not hardcoded in the pipeline file.
- Keep the pipeline file focused on the app's needs; shared logic should be abstracted to scripts or templates if possible.

## Best Practices
- Validate the pipeline configuration after changes by running it in a test environment or using CI linting tools.
- Keep the pipeline steps minimal and fast to ensure quick feedback cycles.
- Document any non-obvious steps or dependencies directly in the `pipeline.yml` file using comments.
- Coordinate with the DevOps team for changes that impact shared infrastructure or deployment processes.

For more details, see your organization's CI/CD documentation or consult with the DevOps team.

# Monitoring Project Rules

## 1. Directory Structure
- The monitoring project is located at [`monitoring/`](mdc:../monitoring) in the workspace root.
- Key subdirectories and files:
  - [`pipeline.yml`](mdc:../monitoring/pipeline.yml): CI/CD pipeline definition
  - [`scripts/`](mdc:../monitoring/scripts): Utility and CI scripts for monitoring
  - [`infrastructure/`](mdc:../monitoring/infrastructure): Infrastructure-as-code (IaC) resources
  - [`.gitignore`](mdc:../monitoring/.gitignore): Git ignore rules
  - [`README.md`](mdc:../monitoring/README.md): Project documentation

## 2. `pipeline.yml` Conventions
- Use `pipeline.yml` to automate build, test, and deployment of monitoring resources.
- Steps should be clear, descriptive, and documented with comments.
- Reference scripts from the `scripts/` directory to avoid duplication.
- Manage environment variables and secrets securely; do not hardcode sensitive data.
- Keep the pipeline focused on monitoring needs; abstract shared logic to scripts or templates.
- Validate changes using CI linting tools or test environments.

## 3. `scripts/` Directory
- Place all reusable scripts for monitoring tasks in the `scripts/` directory.
- Scripts should be modular, well-documented, and referenced from `pipeline.yml` or other automation tools.
- Use consistent naming conventions and file permissions for executability.

## 4. `infrastructure/` Directory
- Store all infrastructure-as-code (IaC) resources for monitoring in the `infrastructure/` directory.
- Organize by service, environment, or technology (e.g., Terraform, CloudFormation).
- Document the purpose and usage of each IaC resource in the `README.md` or inline comments.

## 5. Documentation Practices
- Keep [`README.md`](mdc:../monitoring/README.md) up to date with:
  - Project overview and purpose
  - Setup and usage instructions
  - Descriptions of key scripts and infrastructure
  - Any required environment variables or secrets
- Document non-obvious steps or dependencies in `pipeline.yml` and scripts using comments.
- Coordinate with DevOps/SRE teams for changes impacting shared monitoring infrastructure.

For more details, refer to the documentation in [`monitoring/README.md`](mdc:../monitoring/README.md) and consult with the DevOps/SRE team as needed.
