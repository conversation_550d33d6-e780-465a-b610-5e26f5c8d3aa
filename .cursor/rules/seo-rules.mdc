## SEO Rules

These rules define how to implement SEO across the workspace (Next.js App Router). Apply them to every new page/route, and update existing pages when touching related code.

### Core Principles
- **Use Next.js Metadata API** (App Router) for all static SEO metadata
- **Generate dynamic metadata** for dynamic routes using `generateMetadata`
- **Always define canonical URLs** using `process.env.BASE_URL`
- **Provide Open Graph and Twitter metadata** for share previews
- **Add JSON-LD structured data** where relevant (Organization, FAQ, Breadcrumbs, Product/Calculator as applicable)
- **Maintain semantic headings** (one H1 per page; descending hierarchy)
- **Accessible content** (alt text for images; descriptive link text)
- **Indexation control** via `robots`, `noindex`, and sitemaps
- **Performance-aware SEO** (CWV/CLS safe UI; preload, preconnect when needed)

### Metadata: Static Pages (App Router)
Use `export const metadata` in `page.tsx` or `layout.tsx` when the content is static.

```ts
// page.tsx
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Mortgage Repayment Calculator | Aussie Homes',
  description:
    'Calculate your estimated mortgage repayments and see how extra repayments affect your loan.',
  alternates: {
    canonical: `${process.env.BASE_URL}/calculators/repayment-calculator/`,
  },
  openGraph: {
    type: 'website',
    url: `${process.env.BASE_URL}/calculators/repayment-calculator/`,
    title: 'Mortgage Repayment Calculator | Aussie Homes',
    description:
      'Calculate your estimated mortgage repayments and see how extra repayments affect your loan.',
    siteName: 'Aussie Home Loans',
    images: [{ url: 'https://www.aussie.com.au/assets/favicon.ico', width: 800, height: 600 }],
  },
  twitter: {
    card: 'summary_large_image',
    site: 'https://twitter.com/Aussie',
    creator: '@Aussie',
  },
  robots: {
    index: true,
    follow: true,
  },
};
```

### Metadata: Dynamic Pages
Use `export async function generateMetadata()` to build metadata per params/search params.

```ts
// e.g., app/property/suburbs/[slug]/page.tsx
import type { Metadata, ResolvingMetadata } from 'next';

export async function generateMetadata(
  { params }: { params: { slug: string } },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const title = `${decodeURIComponent(params.slug)} | Suburb Insights | Aussie Homes`;
  const url = `${process.env.BASE_URL}/property/suburbs/${params.slug}/`;

  return {
    title,
    description: `Explore property insights and trends for ${decodeURIComponent(params.slug)}.`,
    alternates: { canonical: url },
    openGraph: { url, title, siteName: 'Aussie Home Loans' },
    robots: { index: true, follow: true },
  };
}
```

### Structured Data (JSON-LD)
Add JSON-LD using a script tag in the Head or within the component (App Router allows in layout/page). Use a stable `key` for each block.

- **Organization**: Already present in root layout; keep it updated (logo, urls)
- **FAQPage**: Use for accordion/FAQ content (e.g., calculators)
- **BreadcrumbList**: Use on deep content pages

```tsx
// FAQPage example
const faqJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map((f) => ({
    '@type': 'Question',
    name: f.question,
    acceptedAnswer: { '@type': 'Answer', text: f.answer },
  })),
};

<head>
  <script type="application/ld+json" key="faq_json_ld" dangerouslySetInnerHTML={{ __html: JSON.stringify(faqJsonLd) }} />
</head>
```

```tsx
// BreadcrumbList example
const breadcrumbJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: [
    { '@type': 'ListItem', position: 1, name: 'Home', item: `${process.env.BASE_URL}/` },
    { '@type': 'ListItem', position: 2, name: 'Calculators', item: `${process.env.BASE_URL}/calculators/` },
    { '@type': 'ListItem', position: 3, name: 'Borrowing Power', item: `${process.env.BASE_URL}/calculators/borrowing-power/` },
  ],
};
```

### Headings & Content
- **One H1 per page** (Title component with `order={1}`)
- Use `Title` and `Text` sizes from theme (no custom fonts inline)
- Keep heading hierarchy (H1 → H2 → H3…)
- Provide **alt** for all images and descriptive link text (no "click here")

### Canonical, Robots & Indexing
- Always set `alternates.canonical` to an absolute URL using `BASE_URL`
- For pages not intended to be indexed (e.g., previews, internal-only), set robots: `{ index: false, follow: false }`
- Ensure internal build/static asset routes remain `noindex` (middleware already sets `x-robots-tag` for build assets)

### Sitemaps
- Expose sitemaps under `/sitemap.xml` (and chunked versions as implemented)
- Ensure new routes that are SEO targets are included in site map generation logic

### Open Graph & Social Sharing
- Provide meaningful OG/Twitter `title`, `description`, and `images`
- Use absolute image URLs
- Keep titles ≤ 60 chars; descriptions ≤ 155–160 chars

### Internationalization (if applicable)
- Add `alternates.languages` with `hreflang` when multiple locales are supported

### Performance & CWV
- Avoid layout shifts: reserve space for images/components
- Use `loading="lazy"` for below-the-fold images
- Preconnect to critical origins (e.g., fonts/CDNs) via `<link rel="preconnect" href="..." />` in layout when justified
- Avoid blocking scripts; use `<Script strategy="afterInteractive" />` for non-critical scripts

### Calculators: SEO-Specific
- Each calculator page must:
  - Define static/dynamic metadata (title, description, canonical)
  - Include FAQPage JSON-LD when an FAQ section is present
  - Keep headings consistent with theme and content hierarchy

### Do / Don’t
- **Do**: Use theme fonts and sizes (no inline font-family)
- **Do**: Use env-derived absolute URLs for canonical/OG URLs
- **Don’t**: Use inline random colors/fonts
- **Don’t**: Duplicate `script` JSON-LD keys; prefer stable `key` values

### Review Checklist (per page)
- [ ] metadata.title, metadata.description present
- [ ] `alternates.canonical` is absolute
- [ ] Open Graph and Twitter configured
- [ ] JSON-LD added when applicable (FAQ, Breadcrumbs)
- [ ] One H1; proper heading hierarchy
- [ ] Images have alt text
- [ ] Robots/indexing is correct
- [ ] No inline random colors/fonts; uses theme
- [ ] No layout shifts for key elements
